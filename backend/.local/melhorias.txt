1 - trocar
preg_match por str_contains

preg_match('/PHP/', $string)

str_contains($string, 'PHP')

2 - trocar
explode() > strtok()

$string = "apple,banana,orange,grape";

// Using explode (Slower for large strings)
$array = explode(",", $string);
foreach ($array as $word) {
    echo $word . "\n";
}

// Using strtok (Faster)
$token = strtok($string, ",");
while ($token !== false) {
    echo $token . "\n";
    $token = strtok(",");
}


3. array_map() vs. foreach
Faster: foreach
array_map() introduces function call overhead, while foreach processes elements inline.

Example:
$array = [1, 2, 3, 4, 5];

// Using array_map (Slower)
$result = array_map(fn($n) => $n * 2, $array);

// Using foreach (Faster)
foreach ($array as &$n) {
    $n *= 2;
}



4. json_decode() vs. json_decode($json, true)
Faster: json_decode($json, true)
The associative array conversion (true parameter) is slightly faster than creating objects.

Example:
$json = '{"name": "<PERSON>", "age": 30}';

// Using json_decode (Slower)
$object = json_decode($json);

// Using json_decode with true (Faster)
$array = json_decode($json, true);



5. get_headers() vs. curl
Faster: curl
get_headers() is simpler but performs additional internal processing, while curl is optimized for HTTP requests.

Example:
$url = "https://example.com";

// Using get_headers (Slower)
$headers = get_headers($url);

// Using curl (Faster)
$ch = curl_init($url);
curl_setopt($ch, CURLOPT_NOBODY, true);
curl_exec($ch);
curl_close($ch);




6. in_array() vs. isset() for associative arrays
Faster: isset()
isset() is an O(1) operation, while in_array() iterates over the array (O(n)).

Example:
$array = ["name" => "John", "age" => 30];

// Using in_array (Slower)
if (in_array(30, $array)) {
    echo "Found!";
}

// Using isset (Faster)
if (isset($array["age"])) {
    echo "Found!";
}



7. array_merge() vs. + for associative arrays
Faster: +
array_merge() creates a new array, while + only appends keys that don't exist.

Example:
$array1 = ["a" => 1, "b" => 2];
$array2 = ["b" => 3, "c" => 4];

// Using array_merge (Slower)
$result = array_merge($array1, $array2);

// Using + (Faster)
$result = $array1 + $array2;



8. count() in loops vs. Precomputed value
Faster: Precomputed value
Calling count() in every iteration is slower than storing its value.

Example:
$array = range(1, 1000);

// Using count in loop (Slower)
for ($i = 0; $i < count($array); $i++) {
    echo $array[$i];
}

// Using precomputed count (Faster)
$size = count($array);
for ($i = 0; $i < $size; $i++) {
    echo $array[$i];
}




9. array_filter() vs. foreach
Faster: foreach
array_filter() involves additional function calls.

Example:
$array = [1, 2, 3, 4, 5];

// Using array_filter (Slower)
$result = array_filter($array, fn($n) => $n % 2 == 0);

// Using foreach (Faster)
$result = [];
foreach ($array as $n) {
    if ($n % 2 == 0) {
        $result[] = $n;
    }
}