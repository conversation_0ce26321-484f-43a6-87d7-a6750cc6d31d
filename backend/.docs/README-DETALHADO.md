# Instruções Detalhadas - Backend API Cury Cliente

Este documento fornece instruções detalhadas para a instalação, configuração e uso do projeto Backend API do Cury Cliente.

## 🚀 [Fluxo de Desenvolvimento e CI/CD](/.docs/fluxo-cicd.md)
**Atenção Desenvolvedores:** Por favor, leia nosso [Guia de Fluxo de Desenvolvimento e CI/CD](/.docs/fluxo-cicd.md) antes de começar a trabalhar no projeto!

## Sumário
1. [Requisitos do Sistema](#requisitos-do-sistema)
2. [Instalação](#instalação)
3. [Configuração](#configuração)
4. [Uso](#uso)
5. [Estrutura do Projeto](#estrutura-do-projeto)
6. [Desenvolvimento](#desenvolvimento)
7. [Solução de Problemas](#solução-de-problemas)

## Requisitos do Sistema

Antes de começar, certifique-se de que seu sistema atende aos seguintes requisitos:

- Docker (latest version)
- Git
- Composer
- Node.js (v20.12.2 recomendado, será instalado via NVM se necessário)
- Yarn

## Instalação

1. Clone o repositório:
   ```bash
   <NAME_EMAIL>:studiowox/cury_app_cliente_backend.git
   cd cury_app_cliente_backend
   ```

2. Baixe o .env na raiz do projeto
https://drive.google.com/file/d/1HDMmivAWtyvAYFeOOlJY0r6axdoShwK1/view?usp=sharing

#### [# Variáveis de Ambiente (.env)](/.docs/env.md)
Por favor, leia o documento sobre [Variáveis de Ambiente](/.docs/env.md) para entender melhor seu uso.
 

3. Rode o comando
   ```bash
    7z x env.7z
    ```
Use a senha passada pelo Tech Lead.

4- Instale o server
   ```bash
    ./install.sh
   ```

   Se o comando falhar devido a permissões, torne o arquivo executável:
   ```bash
   chmod +x install.sh && ./install.sh
   ```

5. Apague o env compactado

```bash
rm env.7z
```

   Este script irá:
   - Verificar e instalar as dependências necessárias (incluindo Node.js v20.12.2 via NVM)
   - Configurar o Docker
   - Instalar dependências do projeto via Composer e Yarn
   - Executar migrações do banco de dados
   - Iniciar o servidor de desenvolvimento automaticamente

## Configuração

Após a instalação, você pode precisar configurar alguns aspectos do projeto:

1. Configurações do Banco de Dados:
   - As configurações do banco de dados são gerenciadas pelo Docker. Verifique o arquivo `docker-compose.yml` para detalhes.

2. Configurações do Laravel:
   - Revise e ajuste as configurações em `config/app.php` e outros arquivos em `config/` conforme necessário.

3. Configurações do Horizon:
   - As configurações do Laravel Horizon podem ser ajustadas em `config/horizon.php`.

## Uso

1. Para iniciar o servidor após a instalação inicial:
 
```bash
./start.sh
```
Este script oferecerá opções para limpar caches e filas antes de iniciar o servidor.

Você poderá executar já com as opções de limpeza de caches e filas.
 
```bash
./start.sh -y -d
```

2. Acesse o painel admin:
 
```
   - URL: http://localhost:81/admin
   - Email: <EMAIL>
   - Senha: 12345678
```

3. No painel admin, execute as seguintes atualizações:

```
- Atualizar Data de Negociação
- Atualizar com dados do Salesforce
- Atualizar Record Types
- Atualizar Owners
- Atualizar Videos e Categorias
- Atualizar Feriados
- Atualizar Knowledge
```

4. Para parar o servidor, pressione Ctrl+C no terminal onde `start.sh` está rodando ou rode o comando:

```bash
./stop.sh
```

## Estrutura do Projeto

[Descreva aqui a estrutura de diretórios do projeto e o propósito de cada diretório principal]

## Desenvolvimento

1. Fluxo de trabalho Git:
   - Siga o [Guia de Fluxo de Desenvolvimento e CI/CD](/.docs/fluxo-cicd.md)
   - Use a nomenclatura correta para branches e commits:
     - Branches: `<tipo>/<descrição>` (ex: feature/novo-login, bugfix/correcao-calculo)
     - Commits: `<tipo>[escopo opcional]: <descrição>` (ex: feat(auth): adiciona autenticação de dois fatores)

    Leia nosso [Guia de Fluxo de Desenvolvimento e CI/CD](/.docs/fluxo-cicd.md)

2. Testes (em desenvolvimento):
```
...
```
3. Manutenção:
   - Mantenha as dependências atualizadas regularmente
   - Revise e atualize as migrações do banco de dados conforme necessário

Para suporte adicional, entre em contato com a equipe de desenvolvimento.
