# Variáveis de Ambiente (.env)

Este documento explica as variáveis de ambiente utilizadas no arquivo .env do projeto, incluindo valores padrão sugeridos.

## Configurações da Aplicação

- `APP_NAME`: Nome da aplicação
  - Padrão: "Cury Cliente"
- `APP_ENV`: Ambiente da aplicação
  - Padrão: "local"
- `APP_KEY`: Chave de criptografia da aplicação criada pelo laravel
  - Padrão: [crie com o comando sail artisan key:generate ou php artisan key:generate]
- `APP_DEBUG`: Ativa/desativa o modo de debug
  - Padrão: true
- `APP_TIMEZONE`: Fuso horário da aplicação
  - Padrão: "UTC"
- `APP_URL`: URL base da aplicação : Porta padrão
  - Padrão: "http://localhost:82"
- `VITE_HMR_HOST`: Host para Hot Module Replacement do Vite : Porta padrão
  - Padrão: "localhost:82/admin"
- `APP_LOCALE`: Localização padrão da aplicação
  - Padrão: "pt"
- `APP_FALLBACK_LOCALE`: Localização de fallback
  - Padrão: "pt"
- `APP_FAKER_LOCALE`: Localização para o Faker
  - Padrão: "pt_BR"
- `APP_MAINTENANCE_DRIVER`: Driver para o modo de manutenção
  - Padrão: "file"
- `APP_MAINTENANCE_STORE`: Armazenamento para o modo de manutenção
  - Padrão: "database"
- `BCRYPT_ROUNDS`: Número de rounds para o bcrypt
  - Padrão: 12

## Logging

- `LOG_CHANNEL`: Canal de log padrão
  - Padrão: "stack"
- `LOG_STACK`: Pilha de logs
  - Padrão: "single"
- `LOG_DEPRECATIONS_CHANNEL`: Canal para logs de depreciações
  - Padrão: null
- `LOG_LEVEL`: Nível de log
  - Padrão: "debug"

## Banco de Dados

- `DB_CONNECTION`: Tipo de conexão do banco de dados
  - Padrão: "pgsql"
- `DB_HOST`: Host do banco de dados
  - Padrão: "pgsql"
- `DB_PORT`: Porta do banco de dados
  - Padrão: 5432
- `DB_DATABASE`: Nome do banco de dados
  - Padrão: "curycliente"
- `DB_USERNAME`: Usuário do banco de dados
  - Padrão: "sail"
- `DB_PASSWORD`: Senha do banco de dados
  - Padrão: password

## Portas

- `APP_PORT`: Porta da aplicação
  - Padrão: 82
- `VITE_PORT`: Porta do Vite
  - Padrão: 5174

## Sessão

- `SESSION_DRIVER`: Driver de sessão
  - Padrão: "database"
- `SESSION_LIFETIME`: Tempo de vida da sessão (em minutos)
  - Padrão: 60
- `SESSION_ENCRYPT`: Ativa/desativa criptografia de sessão
  - Padrão: false
- `SESSION_PATH`: Caminho da sessão
  - Padrão: "/admin"
- `SESSION_DOMAIN`: Domínio da sessão
  - Padrão: null

## Serviços

- `BROADCAST_CONNECTION`: Conexão para broadcasting
  - Padrão: "log"
- `FILESYSTEM_DISK`: Disco para sistema de arquivos
  - Padrão: "s3"
- `QUEUE_CONNECTION`: Conexão para filas
  - Padrão: "redis"

## Cache

- `CACHE_STORE`: Armazenamento de cache
  - Padrão: "redis"
- `CACHE_PREFIX`: Prefixo para chaves de cache
  - Padrão: ""
- `MEMCACHED_HOST`: Host do Memcached
  - Padrão: "127.0.0.1"

## Redis

- `REDIS_CLIENT`: Cliente Redis
  - Padrão: "phpredis"
- `REDIS_HOST`: Host do Redis
  - Padrão: "redis"
- `REDIS_PASSWORD`: Senha do Redis
  - Padrão: null
- `REDIS_PORT`: Porta do Redis
  - Padrão: 6379
- `FORWARD_REDIS_PORT`: Porta encaminhada do Redis
  - Padrão: 6379
- `REDIS_DB`: Banco de dados Redis
  - Padrão: 0

## Email

- `MAIL_DRIVER`: Driver de email
  - Padrão: "smtp"
- `MAIL_HOST`: Host do servidor de email
  - Padrão: [Obtenha com o Tech Lead]
- `MAIL_PORT`: Porta do servidor de email
  - Padrão: [Obtenha com o Tech Lead]
- `MAIL_USERNAME`: Usuário do email
  - Padrão: [Obtenha com o Tech Lead]
- `MAIL_PASSWORD`: Senha do email
  - Padrão: [Obtenha com o Tech Lead]
- `MAIL_ENCRYPTION`: Tipo de criptografia do email
  - Padrão: tls
- `MAIL_FROM_ADDRESS`: Endereço de email "from"
  - Padrão: [Obtenha com o Tech Lead]
- `MAIL_FROM_NAME`: Nome "from" para emails
  - Padrão: "Cury Cliente"

## AWS

- `AWS_ACCESS_KEY_ID`: ID da chave de acesso AWS
  - Padrão: [Obtenha com o Tech Lead]
- `AWS_SECRET_ACCESS_KEY`: Chave secreta de acesso AWS
  - Padrão: [Obtenha com o Tech Lead]
- `AWS_BUCKET`: Nome do bucket AWS
  - Padrão: [Obtenha com o Tech Lead]
- `AWS_DEFAULT_REGION`: Região padrão AWS
  - Padrão: [Obtenha com o Tech Lead]
- `AWS_USE_PATH_STYLE_ENDPOINT`: Usar endpoint de estilo de caminho
  - Padrão: false
- `AWS_URL`: URL AWS
  - Padrão: [Obtenha com o Tech Lead]

## Vite

- `VITE_APP_NAME`: Nome da aplicação para o Vite
  - Padrão: "${APP_NAME}"

## Docker

- `WWWGROUP`: Grupo WWW para Docker
  - Padrão: 1000
- `WWWUSER`: Usuário WWW para Docker
  - Padrão: 1000

## Sanctum

- `SANCTUM_STATEFUL_DOMAINS`: Domínios stateful para Sanctum
  - Padrão: "localhost,127.0.0.1"

## Email Cury

- `EMAIL_CURY_INDIQUE`: Email para indicações Cury
  - Padrão: [Obtenha com o Tech Lead ou utilize seu email para teste]

## Sentry

- `SENTRY_LARAVEL_DSN`: DSN do Sentry para Laravel
  - Padrão: [Obtenha com o Tech Lead]
- `SENTRY_TRACES_SAMPLE_RATE`: Taxa de amostragem de traces do Sentry
  - Padrão: 1.0
- `SENTRY_PROFILES_SAMPLE_RATE`: Taxa de amostragem de perfis do Sentry
  - Padrão: 1.0

## Sienge
Aqui utilizamos duas versões (Prod e Homolog)

- `SIENGE_BASE_URL`: URL base do Sienge
  - Padrão: [Obtenha com o Tech Lead]
- `SIENGE_USERNAME`: Usuário do Sienge
  - Padrão: [Obtenha com o Tech Lead]
- `SIENGE_PASSWORD`: Senha do Sienge
  - Padrão: [Obtenha com o Tech Lead]

## Salesforce
A variável 'SALESFORCE_ENV' serve para definir de qual ambiente do salesforce iremos consumir e enviar dados.

PROD - USAR APENAS NO .ENV DO SERVIDOR FINAL.
UATFULL - Utiliza dados mais atualizados com o de producao. 
HOMOLOG - Utiliza dados menos atualizados com o de producao. 
DEVAPP - Ambiente praticamente sem dados. Utilizar para testar fluxos e códigos apex.

- `SALESFORCE_ENV`: Ambiente Salesforce padrão para desenvolvimento
  - Padrão: "UATFULL"

Para cada ambiente (*), substitua por PROD, UATFULL, HOMOLOG, ou DEVAPP:

Por ex:

SALESFORCE_URL_*
Teremos 4 versões da variavel
SALESFORCE_URL_PROD
SALESFORCE_URL_UATFULL
SALESFORCE_URL_HOMOLOG
SALESFORCE_URL_DEVAPP

No .env original que foi enviado fica mais claro. Se tiver dúvidas ainda pergunta ao Tech Lead.

- `SALESFORCE_URL_*`: URL do Salesforce
  - Padrão: [Obtenha com o Tech Lead]
- `SALESFORCE_BASE_URL_*`: URL base do Salesforce
  - Padrão: [Obtenha com o Tech Lead]
- `SALESFORCE_BASE_URL_IMG_*`: URL base para imagens no Salesforce
  - Padrão: [Obtenha com o Tech Lead]
- `SALESFORCE_BASE_URL_FILE_*`: URL base para arquivos no Salesforce
  - Padrão: [Obtenha com o Tech Lead]
- `SALESFORCE_CLIENT_ID_*`: ID do cliente Salesforce
  - Padrão: [Obtenha com o Tech Lead]
- `SALESFORCE_CLIENT_SECRET_*`: Segredo do cliente Salesforce
  - Padrão: [Obtenha com o Tech Lead]
- `SALESFORCE_USERNAME_*`: Nome de usuário Salesforce
  - Padrão: [Obtenha com o Tech Lead]
- `SALESFORCE_PASSWORD_*`: Senha Salesforce
  - Padrão: [Obtenha com o Tech Lead]
- `SALESFORCE_SECRET_TOKEN_*`: Token secreto Salesforce
  - Padrão: [Obtenha com o Tech Lead]
- `SALESFORCE_VERSION_*`: Versão da API Salesforce
  - Padrão: [Obtenha com o Tech Lead]

## New Relic

- `NEWRELIC`: Configuração do New Relic
  - Padrão: [Obtenha com o Tech Lead]

## Bitbucket

- `BITBUCKET_WORKSPACE`: Workspace do Bitbucket
  - Padrão: [Obtenha com o Tech Lead]
- `BITBUCKET_REPO_SLUG`: Slug do repositório Bitbucket
  - Padrão: [Obtenha com o Tech Lead]
- `BITBUCKET_ACCESS_TOKEN`: Token de acesso do Bitbucket
  - Padrão: [Obtenha com o Tech Lead]
- `BITBUCKET_SECRET`: Segredo do Bitbucket
  - Padrão: [Obtenha com o Tech Lead]

- `PASSWORD_DB_CARGA_INICIAL`: Chave para dezipar o banco da carga inicial
  - Padrão: [Obtenha com o Tech Lead]


Nota: Certifique-se de nunca compartilhar ou expor publicamente seu arquivo .env, pois ele contém informações sensíveis e credenciais. Para todas as variáveis marcadas com [Obtenha com o Tech Lead], é crucial que você obtenha os valores corretos diretamente com o Tech Lead do projeto, pois essas informações são sensíveis e específicas para cada ambiente de desenvolvimento.
