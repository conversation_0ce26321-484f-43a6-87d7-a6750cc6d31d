# Fluxo de Desenvolvimento e Processo de CI/CD

Este documento descreve o fluxo de desenvolvimento, o processo de Integração Contínua/Entrega Contínua (CI/CD), e as regras para nomenclatura de branches e mensagens de commit para nosso projeto.

## Links Importantes

- [Lista de Pull Requests](https://bitbucket.org/studiowox/cury_app_cliente_backend/pull-requests/)
- [Pipelines](https://bitbucket.org/studiowox/cury_app_cliente_backend/pipelines)

## Estrutura de Branches

- `master`: Branch principal, reflete o código em produção
- `homolog`: Branch única de homologação
- `feature/*`: Branches de desenvolvimento de features
- `bugfix/*`: Branches para correção de bugs
- `hotfix/*`: Branches para correções urgentes em produção
- `release/*`: Branches para preparação de novas versões

## Regras de Nomenclatura de Branches

1. Formato geral: `<tipo>/<descrição>`
2. Tipos permitidos: `feature`, `bugfix`, `hotfix`, `release`
3. Descrição:
   - Use letras minúsculas
   - Use hífens (-) para separar palavras
   - Seja breve, mas descritivo
   - Inclua o número do ticket/issue antes da descrição, se aplicável

4. Exemplos:
   - `feature/CCM-12-novo-sistema-login`
   - `bugfix/CCM-8-correcao-calculo-juros`
   - `hotfix/CCM-2erro-500-pagina-principal`
   - `release/v1.2.0`

5. Branches especiais:
   - `master`: branch principal de produção
   - `homolog`: branch de homologação

Nota: As branches 'master' e 'homolog' são consideradas branches especiais e são permitidas sem seguir o formato padrão.

## Regras para Mensagens de Commit

1. Formato: `<tipo>(escopo opcional): <descrição>`

2. Tipos permitidos:

   - `feat`: Nova funcionalidade
   - `fix`: Correção de bug
   - `docs`: Alterações na documentação
   - `style`: Formatação, ponto e vírgula faltando, etc (sem alteração de código)
   - `refactor`: Refatoração de código
   - `test`: Adição ou correção de testes
   - `chore`: Atualizações de tarefas de build, configurações de pacotes, etc

3. Escopo

    O escopo é opcional e deve ser incluído entre parenteses após o tipo. Ele fornece informações contextuais adicionais sobre qual parte do código está sendo afetada.

    #### 3.1 Referências do Escopo

    O escopo pode se referir a:

    - Um módulo ou componente específico (ex: "auth", "database", "ui")
    - Uma funcionalidade ou seção do projeto (ex: "login", "perfil-usuario", "relatorios")
    - Um arquivo ou diretório específico (ex: "config", "utils", "models")

    #### 3.2 Formatação do Escopo

    - Use letras minúsculas
    - Use hífens para separar palavras
    - Mantenha o escopo conciso, geralmente uma ou duas palavras

    #### 3.3 Exemplos de Uso do Escopo

    ```
    feat(auth): adiciona autenticação de dois fatores
    fix(extrato): corrige cálculo do total do extrato
    refactor(utils): simplifica função de formatação de data
    ```

    #### 3.4 Quando Usar o Escopo

    Utilize o escopo quando:

    - A mudança afeta uma parte específica e identificável do projeto
    - É necessário facilitar a compreensão rápida da área afetada pela mudança
    - O projeto é maior e a categorização adicional é útil

    #### 3.5 Quando Omitir o Escopo

    Omita o escopo quando:

    - As mudanças afetam múltiplas partes do projeto
    - A mudança é geral ou não se encaixa em um escopo específico
    - O projeto é menor e o escopo não adiciona valor significativo

4. Descrição:
   - Use o imperativo, presente: "adiciona" não "adicionado" ou "adicionando"
   - Primeira letra não capitalizada
   - Sem ponto final
   - Limite de 50 caracteres
   
5. Exemplos:
   - `feat(auth): adiciona autenticação por dois fatores`
   - `fix: corrige cálculo incorreto de juros`
   - `docs: atualiza README com novas instruções de instalação`

## Fluxo de Trabalho

1. **Criação de Nova Branch**
   - O desenvolvedor cria uma nova branch a partir de `master`, seguindo o padrão de nomenclatura:
     ```bash
     git checkout master
     git pull origin master
     git checkout -b <tipo>/<descrição>
     ```
   - Exemplos:
     ```bash
     git checkout -b feature/novo-sistema-login
     git checkout -b bugfix/correcao-calculo-juros
     git checkout -b hotfix/erro-500-pagina-principal
     git checkout -b release/v1.2.0
     ```
   - Desenvolve na branch, seguindo as regras de nomenclatura de commits.
   - Exemplo de commit:
     ```bash
     git commit -m "feat(auth): implementa autenticação de dois fatores"
     ```

2. **Revisão Inicial pelo Tech Lead**
   - Ao concluir o trabalho, o desenvolvedor faz push da branch para o Bitbucket:
     ```bash
     git push origin <sua-branch>
     ```
   - Um Pull Request para `homolog` é criado automaticamente no Bitbucket.
   - O Tech Lead revisa o código no PR.
   - Se aprovado, o PR fica pronto para ser mesclado em `homolog`.
   - Se reprovado, o desenvolvedor faz ajustes:
     ```bash
     git checkout <sua-branch>
     # Faça as alterações necessárias
     git commit -m "refactor(auth): ajusta lógica conforme feedback"
     git push origin <sua-branch>
     ```
   - O PR é atualizado automaticamente com as novas mudanças.

3. **Gerenciamento de Homologação**
   - O Tech Lead ou gerente de projeto decide qual branch será testada em homologação.
   - Apenas uma branch por vez é mesclada em `homolog`:
     ```bash
     # No Bitbucket, faça o merge do PR aprovado para homolog
     ```

4. **Teste pelo Cliente**
   - Deploy é feito no ambiente de homologação (geralmente automatizado via Bitbucket Pipelines).
   - O cliente testa as mudanças.

5. **Decisão do Cliente**
   - Se aprovado:
     - Um novo PR é criado automaticamente de `homolog` para `master` no Bitbucket.
   - Se reprovado:
     ```bash
     git checkout homolog
     git reset --hard master
     git push origin homolog --force
     ```
   - O desenvolvedor recebe feedback e retorna à branch original para correções.

6. **Merge para Master e Deploy**
   - Após aprovação final, o PR é mesclado em `master` via interface do Bitbucket.
   - Deploy automático para produção é iniciado (via Bitbucket Pipelines).

7. **Próxima Branch**
   - O processo se repete para a próxima branch na fila de homologação.

8. **Criação de Tag para Release**
   - Após um release ser mesclado em master:
     ```bash
     git checkout master
     git pull origin master
     git tag -a v1.2.0 -m "Release version 1.2.0"
     git push origin v1.2.0
     ```

Notas:
- Para hotfixes, o processo pode ser acelerado, criando um PR diretamente para master após aprovação, seguido de um backport para homolog.
- Mantenha a comunicação clara com a equipe sobre o estado de cada branch e PR.
- Use o Bitbucket Pipelines para automatizar testes e deployments sempre que possível.

## Gerenciamento de Múltiplas Branches

- Branches aprovadas pelo Tech Lead ficam em fila para teste em homologação.
- O Tech Lead ou gerente de projeto prioriza qual branch será testada a seguir.
- Apenas uma branch é testada por vez no ambiente de homologação.
- Hotfixes podem ter prioridade e um fluxo acelerado quando necessário.

## Processo Automatizado de CI/CD

1. **Verificações de PRs**
   - Executa testes automatizados em PRs para `homolog` e `master` via Bitbucket Pipelines.
   - Verifica se o nome da branch e as mensagens de commit seguem as regras estabelecidas.
   - Realiza análise estática de código (lint) para garantir a qualidade do código.
   - Executa uma etapa de build para garantir que o código compile corretamente.
   - Realiza verificações de segurança, incluindo análise de dependências vulneráveis.

2. **Reset Automático de Homolog**
   - Quando uma branch é reprovada em homologação, `homolog` é automaticamente resetada para o estado atual de `master` via Bitbucket Pipelines.

3. **Deploy Automático**
   - Deploy automático para o ambiente de homologação quando há merge em `homolog` via Bitbucket Pipelines.
   - Deploy automático para produção após merge em `master` via Bitbucket Pipelines.
   - Processo de deploy especial para hotfixes, com opção de rollback rápido se necessário.

4. **Monitoramento e Métricas**
   - Implementação de métricas de performance do pipeline para identificar e otimizar etapas lentas.
   - Monitoramento contínuo do ambiente de produção após deploys para detecção rápida de problemas.

## Versionamento Semântico

Adotamos o versionamento semântico (SemVer) para nosso projeto. O formato é MAJOR.MINOR.PATCH:

1. MAJOR: Mudanças incompatíveis com versões anteriores
2. MINOR: Adições de funcionalidades compatíveis com versões anteriores
3. PATCH: Correções de bugs compatíveis com versões anteriores

Exemplo: v1.2.3

## Feature Flags

Utilizamos feature flags para um controle mais granular sobre novas funcionalidades em produção. Isso nos permite:

- Lançar funcionalidades gradualmente
- Realizar testes A/B
- Desativar rapidamente funcionalidades problemáticas sem necessidade de rollback

## Diretrizes para Desenvolvedores

- Siga rigorosamente as regras de nomenclatura de branches e mensagens de commit.
- Use o Commitlint e Husky configurados no projeto para garantir a conformidade com as regras de commit:
  - Ao clonar o repositório pela primeira vez, execute `npm install` seguido de `npx husky install` para garantir que o Husky esteja configurado corretamente.
  - O Husky irá automaticamente verificar suas mensagens de commit usando o Commitlint antes de cada commit.
- Mantenha suas branches atualizadas com `master`:
  ```bash
  git checkout <sua-branch>
  git fetch origin
  git rebase origin/master
  ```
- Esteja preparado para que sua branch possa ficar em fila para teste em homologação.
- Comunique-se claramente sobre o estado e a prioridade das branches com o Tech Lead e a equipe.
- Antes de criar um PR, verifique se todos os commits seguem o padrão estabelecido.
- Se necessário, use `git rebase -i` para ajustar as mensagens de commit antes de criar o PR:
  ```bash
  git rebase -i HEAD~<número de commits>
  ```
- Para hotfixes, comunique imediatamente com o Tech Lead para determinar o processo adequado.

## Resolução de Conflitos de Merge

1. Sempre faça rebase da sua branch com a branch de destino antes de criar um PR:
   ```bash
   git checkout <sua-branch>
   git fetch origin
   git rebase origin/<branch-destino>
   ```
2. Se houver conflitos durante o rebase:
   - Resolva os conflitos manualmente em cada arquivo conflitante.
   - Após resolver, adicione os arquivos modificados com `git add`.
   - Continue o rebase com `git rebase --continue`.
   - Repita o processo até que todos os conflitos sejam resolvidos.
3. Faça push da sua branch atualizada:
   ```bash
   git push origin <sua-branch> --force-with-lease
   ```
4. Se os conflitos forem complexos, consulte o Tech Lead ou outro membro da equipe para assistência.

## Estratégia de Rollback

Em caso de problemas após o deploy:

1. **Rollback Rápido**: 
   - Para problemas críticos, faça rollback imediato para a versão anterior estável:
     ```bash
     git checkout master
     git reset --hard <tag-da-versao-anterior>
     git push origin master --force
     ```
   - Inicie o pipeline de deploy para a versão anterior.

2. **Hotfix**:
   - Para problemas menores, crie um hotfix seguindo o processo de hotfix descrito anteriormente.

3. **Feature Flag**:
   - Se o problema estiver relacionado a uma nova feature, desative-a usando feature flags sem necessidade de rollback completo.

4. **Comunicação**:
   - Informe imediatamente a equipe e stakeholders sobre o problema e as ações tomadas.

## Diretrizes para Revisão de Código

1. **Legibilidade**: O código é claro e fácil de entender?
2. **Funcionalidade**: O código faz o que se propõe a fazer?
3. **Performance**: Há otimizações óbvias que podem ser feitas?
4. **Segurança**: Existem vulnerabilidades potenciais?
5. **Testabilidade**: O código é testável? Foram adicionados testes adequados?
6. **Manutenibilidade**: O código segue os padrões do projeto e boas práticas?
7. **Reutilização**: Há oportunidades para reutilização de código existente?
8. **Documentação**: Os comentários e documentação são claros e úteis?

## Resolução de Problemas

Se encontrar problemas com o pipeline, o processo de CI/CD, ou as regras de commit:

1. Verifique os logs do pipeline no Bitbucket Pipelines.
2. Confirme se todas as variáveis de ambiente estão configuradas corretamente.
3. Verifique se as branches estão atualizadas e se não há conflitos.
4. Para problemas relacionados a nomes de branch ou mensagens de commit, revise as regras e ajuste conforme necessário.
5. Se o Commitlint ou Husky estiverem causando problemas, verifique suas configurações locais:
   ```bash
   cat .husky/commit-msg
   cat commitlint.config.js
   ```
6. Para problemas de build:
   - Verifique se todas as dependências estão instaladas e atualizadas.
   - Tente reproduzir o erro localmente para isolar o problema.
   - Revise as configurações de build no pipeline.

7. Para problemas de deploy:
   - Verifique as configurações de ambiente e variáveis de ambiente.
   - Revise os logs de deploy para identificar possíveis erros.
   - Certifique-se de que todos os serviços necessários estão disponíveis e configurados corretamente.

8. Se houver problemas com feature flags:
   - Verifique a configuração das feature flags no código e no serviço de gerenciamento de flags.
   - Certifique-se de que as condições para ativação/desativação das flags estão corretas.

9. Para problemas persistentes ou complexos:
   - Documente o problema detalhadamente, incluindo logs e passos para reproduzir.
   - Crie um ticket no sistema de rastreamento de issues do projeto.
   - Agende uma reunião de emergência com o Tech Lead e membros relevantes da equipe, se necessário.

## Métricas e KPIs

Para medir a eficácia do nosso processo de CI/CD, monitoramos as seguintes métricas:

1. **Tempo de ciclo**: Tempo médio desde o início do desenvolvimento até o deploy em produção.
2. **Frequência de deploy**: Número de deploys bem-sucedidos em produção por semana/mês.
3. **Taxa de falha de mudança**: Percentual de deploys que resultam em degradação do serviço ou rollback.
4. **Tempo médio de recuperação (MTTR)**: Tempo médio para recuperar o serviço após uma falha.
5. **Duração do pipeline**: Tempo médio para o pipeline CI/CD completar todas as etapas.
6. **Cobertura de testes**: Percentual do código coberto por testes automatizados.
7. **Débito técnico**: Medido através de análise estática de código e revisões regulares.

Essas métricas são revisadas mensalmente para identificar áreas de melhoria no processo.

## Treinamento e Onboarding

Para garantir que todos os membros da equipe estejam alinhados com nosso processo de CI/CD:

1. Novos membros da equipe recebem treinamento sobre o fluxo de trabalho e ferramentas utilizadas.
2. Documentação atualizada está sempre disponível no repositório do projeto.
3. Sessões de pair programming são incentivadas para compartilhar conhecimento.
4. Workshops trimestrais são realizados para discutir melhorias no processo e novas tecnologias.

## Melhoria Contínua

Nosso processo de CI/CD está em constante evolução. Encorajamos todos os membros da equipe a:

1. Propor melhorias no processo através de issues no repositório do projeto.
2. Participar ativamente nas reuniões de retrospectiva para discutir o que está funcionando bem e o que pode ser melhorado.
3. Manter-se atualizado sobre as melhores práticas da indústria em CI/CD e DevOps.
4. Experimentar novas ferramentas e técnicas em projetos menores ou branches de experimento antes de propor mudanças no fluxo principal.

## Conclusão

Este documento serve como um guia abrangente para nosso processo de CI/CD. Ele deve ser revisado e atualizado regularmente para refletir as melhores práticas e as necessidades em evolução do projeto e da equipe.

Lembre-se: o objetivo final do nosso processo de CI/CD é permitir entregas rápidas, confiáveis e de alta qualidade, sempre mantendo a estabilidade do nosso produto em produção.

Para quaisquer dúvidas ou sugestões sobre este processo, entre em contato com o Tech Lead ou abra uma issue no repositório do projeto.
