#!/bin/bash

# Mover o script de atualização para /usr/local/bin
# sudo cp /var/www/html/back/.scripts/update-backend.sh /usr/local/bin/update-backend
# sudo chmod +x /usr/local/bin/update-backend
# update-backend - para rodar o script


cd /var/www/html/back

git stash

git pull

sudo chmod -R 777 storage/ bootstrap/

php artisan optimize && php artisan optimize:clear && php artisan cache:clear && php artisan config:clear && php artisan route:clear && php artisan event:clear && php artisan view:clear

composer dump-autoload -o

sudo chmod -R 777 storage/ bootstrap/

npm install
npm run build

sudo systemctl restart php-fpm
sudo systemctl restart supervisord
sudo systemctl restart nginx
