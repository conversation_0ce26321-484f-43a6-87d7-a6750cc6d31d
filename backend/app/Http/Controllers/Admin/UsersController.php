<?php

declare(strict_types=1);

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Admin;
use App\Models\Role;
use App\Services\Admin\UsersAdminsService;
use Auth;
use Hash;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;

class UsersController extends Controller
{
    protected $usersAdminsService;

    public function __construct(
        UsersAdminsService $usersAdminsService
    ) {
        $this->usersAdminsService = $usersAdminsService;
    }

    public function index()
    {
        return view('admin.users.index');
    }

    public function importFromSalesforce()
    {
        $this->usersAdminsService->refreshAll();

        return redirect()->route('admin.users.index')->with('success', 'Importação do Salesforce não implementada ainda.');
    }

    public function store(Request $request)
    {
        $validatedData = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:admins',
            'password' => 'required|string|min:8|confirmed',
            'CompanyName' => 'nullable|string|max:255',
            'Division' => 'nullable|string|max:255',
            'Department' => 'nullable|string|max:255',
            'UserId' => 'nullable|string|max:255',
            'role_id' => 'required|exists:roles,id',
        ]);

        $validatedData['password'] = bcrypt($validatedData['password']);

        $this->usersAdminsService->search($validatedData);

        return redirect()->route('admin.users.index')->with('success', 'Usuário criado com sucesso!');
    }

    public function search(Request $request): JsonResponse
    {
        $inputName = $request->input('name');
        $users = $this->usersAdminsService->search($inputName);

        return response()->json($users);
    }

    public function create()
    {
        $authUser = Auth::user();

        if ($authUser->role->slug === 'admin') {
            $roles = Role::where('slug', '!=', 'super-admin')->get();
        } else {
            $roles = Role::all();
        }

        return view('admin.users.create', compact('roles'));
    }

    public function edit(Admin $user)
    {
        $authUser = Auth::user();
        if ($authUser->role->slug === 'admin') {
            $roles = Role::where('slug', '!=', 'super-admin')->get();
        } else {
            $roles = Role::all();
        }

        return view('admin.users.edit', compact('user', 'roles'));
    }

    public function update(Request $request, Admin $user)
    {
        $validatedData = $request->validate([
            'name' => 'required|string|max:255',
            'email' => ['required', 'string', 'email', 'max:255', Rule::unique('admins')->ignore($user->id)],
            'CompanyName' => 'nullable|string|max:255',
            'Division' => 'nullable|string|max:255',
            'Department' => 'nullable|string|max:255',
            'UserId' => 'nullable|string|max:255',
            'role_id' => 'required|exists:roles,id',
        ]);

        if ($request->filled('password')) {
            // $validatedData['password'] = bcrypt($request->password);
            $validatedData['password'] = Hash::make($request->password);
        }

        $user->update($validatedData);

        return redirect()->route('admin.users.index')->with('success', 'Usuário atualizado com sucesso!');
    }

    public function destroy(Request $request, Admin $user)
    {
        $user->delete();

        return redirect()->route('admin.users.index')->with('success', 'Usuário apagado com sucesso!');
    }
}
