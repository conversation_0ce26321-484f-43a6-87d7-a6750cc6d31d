<?php

declare(strict_types=1);

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Services\Admin\DashboardService;
use Illuminate\Support\Facades\Log;

class DashboardController extends Controller
{
    protected DashboardService $dashboardService;

    public function __construct(DashboardService $dashboardService)
    {
        $this->dashboardService = $dashboardService;
    }

    public function index($success = null)
    {
        $totalStats = $this->dashboardService->totalStats();
        $dataAntecipacaoDesconto = $this->dashboardService->dataAntecipacaoDesconto()[0];
        $dataAntecipacaoDesconto_update = $this->dashboardService->dataAntecipacaoDesconto()[1];

        return view('admin.dashboard', [
            'success' => $success,
            'stats' => $totalStats,
            'dataAntecipacaoDesconto' => $dataAntecipacaoDesconto,
            'dataAntecipacaoDesconto_update' => $dataAntecipacaoDesconto_update,
        ]);
    }
}
