<?php

declare(strict_types=1);

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Empreendimento;
use App\Services\EmpreendimentoService;
use App\Services\Salesforce\EmpreendimentoSalesService;
use App\Services\Salesforce\Utils\Processors\EmpreendimentoProcessor;
use App\Services\Salesforce\Utils\Queries\EmpreendimentoQuery;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\View\View;

class EmpreendimentosController extends Controller
{
    protected $empreendimentoService;

    protected $empreendimentoSalesService;

    protected $empreendimentoProcessor;

    protected $empreendimentoQuery;

    public function __construct(
        EmpreendimentoService $empreendimentoService,
        EmpreendimentoSalesService $empreendimentoSalesService,
        EmpreendimentoProcessor $empreendimentoProcessor,
        EmpreendimentoQuery $empreendimentoQuery
    ) {
        $this->empreendimentoService = $empreendimentoService;
        $this->empreendimentoSalesService = $empreendimentoSalesService;
        $this->empreendimentoProcessor = $empreendimentoProcessor;
        $this->empreendimentoQuery = $empreendimentoQuery;
    }

    public function index(): View
    {
        $empreendimentos = Empreendimento::orderBy('name', 'ASC')->paginate(20);

        return view('admin.empreendimentos.index', compact('empreendimentos'));
    }

    public function search(Request $request): JsonResponse
    {
        $name = $request->input('name');
        $query = (! isset($name)) ? '' : strtolower($name);
        if (strlen($query) < 1) {
            $empreendimentos = Empreendimento::orderBy('name', 'ASC')->paginate(20);

            return response()->json($empreendimentos);
        }

        $empreendimentos = Empreendimento::whereRaw('LOWER(name) LIKE ?', ["%{$query}%"])->orderBy('name', 'ASC')->paginate(20);

        return response()->json($empreendimentos);
    }

    public function refresh(Request $request)
    {
        $id = $request->input('Id');
        if (! $id) {
            return response()->json(['error' => 'ID not provided'], 400);
        }

        $queryEmpreendimentos = $this->empreendimentoQuery->getQuery($id);

        $empreendimentoData = $this->empreendimentoSalesService->executeQuery($queryEmpreendimentos);

        $empreendimento = $empreendimentoData['records'][0] ?? null;

        if ($empreendimento) {
            $this->empreendimentoProcessor->process($empreendimento);
            $this->empreendimentoService->create($empreendimento);
        }

        $empreendimentos = $this->empreendimentoService->findOneByField('EmpreendimentoId', $id);

        return response()->json($empreendimentos);
    }

    public function show($id)
    {
        return view('admin.empreendimentos.edit', compact('id'));
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @return JsonResponse
     */
    public function edit($id)
    {
        $empreendimento = Empreendimento::where('EmpreendimentoId', $id)
            ->with([
                'Schedule',
                'ScheduleServicesOptions',
                'sindico',
            ])
            ->firstOrFail();

        return response()->json($empreendimento);
    }

    /**
     * Update the specified resource in storage.
     *
     * @return Response
     */
    public function update(Request $request, $id)
    {
        $empreendimento = Empreendimento::where('EmpreendimentoId', $id)->firstOrFail();

        $validatedData = $request->validate([
            'name' => 'sometimes|string',
            'Regional__c' => 'sometimes|string',
            'StatusMacro__c' => 'sometimes|string',
            'CodigoSienge__c' => 'sometimes|string',
            'Sigma__c' => 'sometimes|boolean',
            'Manual__c' => 'sometimes|boolean',
            'CodigoSiengeSPE' => 'sometimes|string',
            'LogoEmpreendimento__c' => 'sometimes|string',
            'EstagioComercializacao__c' => 'sometimes|string',
            'GOResponsavel__c' => 'sometimes|string',
            'DataUltimaAtualizacao__c' => 'sometimes|string',
            'Fundacao__c' => 'sometimes|string',
            'Estrutura__c' => 'sometimes|string',
            'Alvenaria__c' => 'sometimes|string',
            'InstalacoesEletricas__c' => 'sometimes|string',
            'InstalacoesHidraulicas__c' => 'sometimes|string',
            'AcabamentoInterno__c' => 'sometimes|string',
            'AcabamentoExterno__c' => 'sometimes|string',
            'ServicosComplementares__c' => 'sometimes|string',
            'Pintura__c' => 'sometimes|string',
            'MobilizacaoCanteiro__c' => 'sometimes|string',
            'PorcentagemFisicoAcumulado__c' => 'sometimes|string',
            'Sindico__c' => 'sometimes|string',
            'StatusMacro__c' => 'sometimes|string',
            'DataRealizadaHabitese__c' => 'sometimes|string',
            'EstagioComercializacao__c' => 'sometimes|string',
            'DataAGIRealizada__c' => 'sometimes|string',
            'imgsCarrossel' => 'sometimes|string',
            'Fotos__c' => 'sometimes',
            'Videos__c' => 'sometimes',
            'UltimaAtualizacaoVideoDrone__c' => 'sometimes|string',
            'DataUltimaAtualizacaoMidia__c' => 'sometimes|string',
            'DataRealMatriculaIndividualizada__c' => 'sometimes|string',
            'OperatingHoursId' => 'sometimes|string',
            'TerritorioServico' => 'sometimes|string',
            'TerritorioServicoIsActive' => 'sometimes|string',
            'documents' => 'sometimes',
            'DataEntregaContratualCury__c' => 'sometimes|string',
            'Video_Tour__c' => 'sometimes',
            'Last_Data_Tour__c' => 'sometimes',
        ]);

        $empreendimento->update($validatedData);

        return response()->json($empreendimento);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @return Response
     */
    public function destroy($id)
    {
    }
}
