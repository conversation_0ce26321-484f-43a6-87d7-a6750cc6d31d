<?php

declare(strict_types=1);

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Api\Flows\FlowUserController;
use App\Http\Controllers\Controller;
use App\Models\Proposals;
use App\Services\ProposalService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Redirect;
use Illuminate\View\View;

class PropostasController extends Controller
{
    protected $flowUserController;

    protected $proposalService;

    public function __construct(
        FlowUserController $flowUserController,
        ProposalService $proposalService
    ) {
        $this->flowUserController = $flowUserController;
        $this->proposalService = $proposalService;
    }

    public function index(Request $request): View
    {
        $proposals = $this->proposalService->paginate(20);

        return view('admin.propostas.index', ['proposals' => $proposals]);
    }

    public function create(): View
    {
        return view('admin.propostas.create', []);
    }

    public function search(Request $request): JsonResponse
    {
        $name = $request->input('name');
        $query = (! isset($name)) ? '' : strtolower($name);
        if (strlen($query) < 1) {
            $proposals = Proposals::whereHas('user')
                ->with(['user', 'empreendimento'])
                ->join('users', 'proposals.AccountId', '=', 'users.AccountId')
                ->orderBy(DB::raw("NULLIF(regexp_replace(users.\"CPF__c\", '\D', '', 'g'), '')::numeric"), 'ASC')
                ->orderBy(DB::raw("NULLIF(regexp_replace(users.\"CNPJ__c\", '\D', '', 'g'), '')::numeric"), 'ASC')
                ->select('proposals.*')
                ->paginate(20);

            return response()->json($proposals);
        }

        $proposals = Proposals::whereHas('user')
            ->with(['user', 'empreendimento'])
            ->join('users', 'proposals.AccountId', '=', 'users.AccountId')
            ->orderBy(DB::raw("NULLIF(regexp_replace(users.\"CPF__c\", '\D', '', 'g'), '')::numeric"), 'ASC')
            ->orderBy(DB::raw("NULLIF(regexp_replace(users.\"CNPJ__c\", '\D', '', 'g'), '')::numeric"), 'ASC')
            ->whereRaw('LOWER(name) LIKE ?', ["%{$query}%"])
            ->select('proposals.*')
            ->paginate(20);

        return response()->json($proposals);
    }

    public function store(Request $request)
    {
        $this->flowUserController->createByPv(new Request(['pv' => $request->input('pv')]));

        return Redirect::route('admin.propostas.index');
    }

    public function refresh(Request $request)
    {
        $this->flowUserController->createByPv(new Request(['pv' => $request->input('pv')]));

        return Redirect::route('admin.propostas.index');
    }
}
