<?php

declare(strict_types=1);

namespace App\Http\Controllers\Admin;

use App\DDD\Application\User\Services\UserService;
use App\Http\Controllers\Api\Flows\FlowUserController;
use App\Http\Controllers\Controller;
use App\Services\AssetService;
use App\Services\ContractsService;
use App\Services\CreateUnitService;
use App\Services\ProposalService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Str;
use Illuminate\View\View;

class ClientsCuryController extends Controller
{

    public function __construct(
        private readonly FlowUserController $flowUserController,
        private readonly ProposalService $proposalService,
        private readonly ContractsService $contractsService,
        private readonly UserService $userService,
        private readonly AssetService $assetService,
        private readonly CreateUnitService $createUnitService,
    ) {
    }

    public function index(): View
    {
        return view('admin.clientcury.index');
    }

    public function create(Request $request): View
    {
        $proposals = $this->proposalService->paginate(20);

        return view('admin.clientcury.create', ['proposals' => $proposals]);
    }

    public function store(Request $request): RedirectResponse
    {
        $this->flowUserController->createByPv(new Request(['pv' => $request->input('pv')]));

        return Redirect::route('admin.clientcury.create');
    }

    public function search(Request $request): JsonResponse
    {
        $input = $request->input('name');
        $state = $request->input('state');
        $salesforceUrl = config('services.salesforce.salesforce_url');

        $users = $this->userService->search($input, $state);

        return response()->json([
            'users' => $users,
            'salesforceUrl' => $salesforceUrl,
            'total' => $users->total(),
            'from' => $users->firstItem(),
            'to' => $users->lastItem(),
            'current_page' => $users->currentPage(),
            'last_page' => $users->lastPage(),
            'links' => $users->links(),

        ]);
    }

    public function show($AccountId)
    {
        $user = $this->userService->findOneByField('AccountId', $AccountId);

        if (!isset($user)) {
            return Redirect::route('admin.clientcury.index');
        }

        return response(view('admin.clientcury.show', [
            'user' => $user,
            'assets' => $user->assets
        ]));
    }

    public function update(Request $request)
    {
        $id = $request->input('id');
        $area = $request->input('area');

        $newData = null;
        switch ($area) {
            case 'units':
                $newData = $this->createUnitService->verifyUnits($id);

                break;
            case 'user':
                $newData = $this->userService->updateDataBySalesforce($id);

                break;
            case 'asset':
                $newData = $this->assetService->updateDataBySalesforce($id);

                break;
            case 'contract':
                $newData = $this->contractsService->updateDataBySalesforce($id);

                break;
            case 'proposal':
                $newData = $this->proposalService->updateDataBySalesforce($id);

                break;
            // case 'empreendimento':
            //     $newData = $this->empreendimentoService->updateDataBySalesforce($id);
            //     break;

            default:
                // code...
                break;
        }
        $data = [$newData, $id];

        return response()->json($data);
    }

    /*
    public function logarUser($AccountId)
    {
        $user = $this->userService->findOneByField('AccountId', $AccountId);

        if (! isset($user)) {
            return Redirect::route('admin.clientcury.index');
        }

        $password = $user->password;
        $device_info = 'admin-'.Auth::user()->id.'-'.$user->AccountId;
        if (! isset($password)) {
            // Criar um DTO para atualização do usuário
            $userDto = UserCreateDto::fromArray([
                'Id' => $AccountId,
                'AccountId' => $AccountId
            ])->generateRandomPassword();

            // Atualizar o usuário usando o método update da AbstractService
            $user = $this->userService->update($AccountId, $userDto);

            $password = $userDto->data['password_plaintext'];
        }

        $authResponse = $this->authService->login($user->CPF__c ?? $user->CNPJ__c, $password, $device_info, true);

        $auth = new AuthResource($authResponse->getData());
        $user = $auth->resource['user'];
        $user->isImpersonating = true;
        $token = $auth->resource['token']->getData();
        $access_token = $token['token']->plainTextToken;
        $expires_at = $token['token']->accessToken->expires_at;
        $account_id = $user->AccountId;
        $urlIFrame = 'https://cliente.cury.net/';
        switch (config('services.env.app_env')) {
            case 'local':
                $urlIFrame = 'http://localhost:3000';

                break;
            case 'homolog':
                $urlIFrame = 'https://homolog.cliente.cury.net';

                break;

            default:
                $urlIFrame = 'https://cliente.cury.net/';

                break;
        }
        // $urlIFrame = config('services.env.app_env') === 'local' ? 'https://homolog.cliente.cury.net/' : 'https://cliente.cury.net/';

        return response(view('admin.clientcury.experience', [
            'user' => $user,
            'device_info' => $device_info,
            'access_token' => $access_token,
            'expires_at' => $expires_at,
            'account_id' => $account_id,
            'urlIFrame' => $urlIFrame,
        ]));
    }

    public function resendWelcomeMail(Request $request)
    {
        // $id = $request->input('userId');
        // $user = $this->userService->findById($id);
        // $userLogin = $user->CPF__c ?? $user->CNPJ__c;
        // try {
        //     $emailSent = $this->emailService->resendWelcomeMail($userLogin);
        //     Log::debug("resendWelcomeMail: ", [$userLogin]);
        //     Log::debug('Email de boas-vindas reenviado com sucesso');
        //     return response()->json(['success' => true, 'message' => 'Email de boas-vindas reenviado com sucesso']);
        // } catch (\Exception $e) {
        //     Log::error('Erro ao reenviar email de boas-vindas: ' . $e->getMessage());
        //     return response()->json(['success' => false, 'message' => 'Erro ao reenviar email de boas-vindas'], 500);
        // }
    }*/
}
