<?php

declare(strict_types=1);

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class ResetSequences extends Command
{
    protected $signature = 'db:reset-sequences';

    protected $description = 'Reseta as sequências de auto incremento para todas as tabelas';

    protected $tables = [
        'assets', 'cases_sales', 'categoria_video_portals',
        'config_date_to_negotiations', 'contracts', 'empreendimentos',
        'failed_jobs', 'group_sales', 'indexers', 'jobs',
        'knowledges', 'messages',
        'permissions', 'personal_access_tokens', 'proposals', 'record_types',
        'roles', 'schedule', 'schedule_holiday',
        'schedulings_services_options', 'sessions', 'users',
        'video_portals', 'work_order',
    ];

    public function handle()
    {
        foreach ($this->tables as $table) {
            $sequence = DB::select("SELECT pg_get_serial_sequence('$table', 'id') as sequence")[0]->sequence;

            if ($sequence) {
                $maxId = DB::table($table)->max('id');

                if ($maxId !== null) {
                    DB::select("SELECT setval('$sequence', $maxId)");
                    $this->info("Sequência de $table resetada com sucesso.");
                } else {
                    $this->warn("Tabela $table está vazia, sequência não alterada.");
                }
            } else {
                $this->error("Não foi possível encontrar a sequência para a tabela $table.");
            }
        }

        $this->info('Todas as sequências foram resetadas com sucesso.');
    }
}
