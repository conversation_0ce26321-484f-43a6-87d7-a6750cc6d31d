<?php

declare(strict_types=1);

namespace App\Console\Commands;

use App\DDD\Infrastructure\Shared\Interfaces\Services\FilesServiceInterface;
use App\DDD\Infrastructure\Shared\Services\FilesService;
use Illuminate\Console\Command;
use ReflectionClass;

class TestFilesServiceCommand extends Command
{
    protected $signature = 'test:files-service';

    protected $description = 'Teste de diagnóstico para o FilesService';

    public function handle()
    {
        $this->info('Iniciando teste do FilesService');

        // Verificar se o serviço já foi resolvido
        $resolved = app()->resolved(FilesServiceInterface::class);
        $this->info('FilesService já resolvido? '.($resolved ? 'Sim' : 'Não'));

        // Ver todas as suas dependências
        $reflector = new ReflectionClass(FilesService::class);
        $constructor = $reflector->getConstructor();
        $params = $constructor->getParameters();

        $this->info('Dependências do FilesService:');
        foreach ($params as $param) {
            $this->info($param->getName().': '.$param->getType()->getName());
        }

        // Testar criação manual do serviço
        $this->info('Tentando criar manualmente...');

        try {
            $imageCompress = new \App\DDD\Infrastructure\Shared\Image\ImageCompress();
            $this->info('ImageCompress criado com sucesso');

            $filesService = new FilesService($imageCompress);
            $this->info('FilesService criado com sucesso');
        } catch (\Exception $e) {
            $this->error('Erro ao criar: '.$e->getMessage());
        }

        $this->info('Teste concluído');
    }
}
