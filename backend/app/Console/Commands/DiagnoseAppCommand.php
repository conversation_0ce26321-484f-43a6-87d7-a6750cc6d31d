<?php

declare(strict_types=1);

namespace App\Console\Commands;

use Illuminate\Console\Command;

class DiagnoseAppCommand extends Command
{
    protected $signature = 'app:diagnose';

    protected $description = 'Diagnostica problemas na aplicação';

    public function handle()
    {
        $this->info('Iniciando diagnóstico da aplicação');

        // Lista todos os service providers carregados
        $providers = $this->laravel->getLoadedProviders();
        $this->info('Service Providers carregados: '.count($providers));
        foreach ($providers as $provider => $loaded) {
            $this->info("- $provider: ".($loaded ? 'Carregado' : 'Não carregado'));
        }

        // Verifica configurações do S3
        $this->info('Verificando configuração S3:');
        $s3Config = config('filesystems.disks.s3');
        $this->info('- Região: '.($s3Config['region'] ?? 'Não configurado'));
        $this->info('- Bucket: '.($s3Config['bucket'] ?? 'Não configurado'));
        $this->info('- Chave de acesso: '.(isset($s3Config['key']) ? 'Configurada' : 'Não configurada'));
        $this->info('- Chave secreta: '.(isset($s3Config['secret']) ? 'Configurada' : 'Não configurada'));

        // Verifica a extensão Imagick
        $this->info('Verificando extensão Imagick:');
        if (extension_loaded('imagick')) {
            $this->info('- Instalada: Sim');
            $imagick = new \Imagick();
            $this->info('- Versão: '.$imagick->getVersion()['versionString']);
        } else {
            $this->error('- Instalada: Não');
        }

        $this->info('Diagnóstico concluído');
    }
}
