<?php

declare(strict_types=1);

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\File;
use ZipArchive;

class UpdateUserExportCSV extends Command
{
    protected $signature = 'users:update-export';

    protected $description = 'Update user export CSV with CPF and CNPJ, and create a password-protected ZIP';

    public function handle()
    {
        $csvPath = database_path('seeders/exports/users_export.csv');
        $tempPath = database_path('seeders/exports/users_export_temp.csv');
        $zipPath = database_path('seeders/exports/users_export_updated.zip');

        if (! File::exists($csvPath)) {
            $this->error('Arquivo CSV original não encontrado.');

            return;
        }

        $csvFile = fopen($csvPath, 'r');
        $tempFile = fopen($tempPath, 'w');

        // Atualizar o cabeçalho
        $header = fgetcsv($csvFile);
        fputcsv($tempFile, array_merge($header, ['CPF', 'CNPJ']));

        $bar = $this->output->createProgressBar(count(file($csvPath)));
        $bar->start();

        while (($data = fgetcsv($csvFile)) !== false) {
            $email = $data[1]; // Assumindo que o email está na segunda coluna

            $user = DB::table('users')
                ->select('CPF__c', 'CNPJ__c')
                ->where('Email__c', $email)
                ->first();

            if ($user) {
                $data[] = $user->CPF__c ?? '';
                $data[] = $user->CNPJ__c ?? '';
            } else {
                $data[] = '';
                $data[] = '';
            }

            fputcsv($tempFile, $data);
            $bar->advance();
        }

        fclose($csvFile);
        fclose($tempFile);

        $bar->finish();
        $this->info("\nArquivo CSV atualizado.");

        // Criar ZIP com senha
        $zip = new ZipArchive();
        if ($zip->open($zipPath, ZipArchive::CREATE) !== true) {
            $this->error('Falha ao criar o arquivo ZIP.');

            return;
        }

        $password = bin2hex(random_bytes(8)); // Gera uma senha aleatória
        $zip->setPassword($password);

        $zip->addFile($tempPath, 'users_export_updated.csv');
        $zip->setEncryptionName('users_export_updated.csv', ZipArchive::EM_AES_256);

        $zip->close();

        // Remover o arquivo temporário
        File::delete($tempPath);

        $this->info('Arquivo CSV atualizado e salvo em ZIP com senha.');
        $this->info("Caminho do ZIP: $zipPath");
        $this->info("Senha do ZIP: $password");

        // Salvar a senha em um arquivo separado
        $passwordFile = database_path('seeders/exports/password_updated.txt');
        File::put($passwordFile, $password);
        $this->info("Senha salva em: $passwordFile");
    }
}
