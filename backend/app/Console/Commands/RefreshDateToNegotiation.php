<?php

declare(strict_types=1);

namespace App\Console\Commands;

use App\Http\Controllers\Api\ConfigSalesforceDataController;
use Illuminate\Console\Command;

class RefreshDateToNegotiation extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:refresh-date-to-negotiation';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Pega do Salesforce Data de Negociação de desconto';

    protected $configSalesforceDataController;

    public function __construct(ConfigSalesforceDataController $configSalesforceDataController)
    {
        parent::__construct();
        $this->configSalesforceDataController = $configSalesforceDataController;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        try {
            $result = $this->configSalesforceDataController->refreshDateToNegotiation();

            if ($result) {
                $this->info('Data de negociação atualizada com sucesso.');
            } else {
                $this->warn('A atualização da data de negociação não retornou um resultado positivo.');
            }
        } catch (\Exception $e) {
            $this->error('Erro ao atualizar data de negociação: '.$e->getMessage());
        }
    }
}
