<?php

declare(strict_types=1);

namespace App\Console\Commands;

use App\Http\Controllers\Api\ConfigSalesforceDataController;
use Illuminate\Console\Command;

class RefreshDailyBySalesforce extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:refresh-daily-by-salesforce';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Atualização dos dados do Salesforce';

    protected $configSalesforceDataController;

    public function __construct(ConfigSalesforceDataController $configSalesforceDataController)
    {
        parent::__construct();
        $this->configSalesforceDataController = $configSalesforceDataController;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        try {
            $result = $this->configSalesforceDataController->refreshDateToNegotiation();
            $result = $this->configSalesforceDataController->refreshEmpreendimentosScheduleServicesOptions();

            if ($result) {
                $this->info('Dados tualizados com sucesso.');
            } else {
                $this->warn('A atualização não retornou um resultado positivo.');
            }
        } catch (\Exception $e) {
            $this->error('Erro ao atualizar: '.$e->getMessage());
        }
    }
}
