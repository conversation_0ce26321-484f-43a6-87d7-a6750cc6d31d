<?php

declare(strict_types=1);

namespace App\Console\Commands;

use App\Http\Controllers\Api\ConfigSalesforceDataController;
use Illuminate\Console\Command;

class CustomMigrateCommand extends Command
{
    protected $signature = 'custom:migrate';

    protected $description = 'Executes migrations and then runs specific controller actions';

    protected $configSalesforceDataController;

    public function __construct(ConfigSalesforceDataController $configSalesforceDataController)
    {
        $this->configSalesforceDataController = $configSalesforceDataController;
        parent::__construct();
    }

    public function handle()
    {
        $this->call('optimize');
        $this->call('optimize:clear');
        $this->call('cache:clear');
        $this->call('config:clear');
        $this->call('route:clear');
        $this->call('event:clear');
        $this->call('view:clear');

        $this->call('migrate:rollback');
        $this->call('migrate');

        $this->configSalesforceDataController->store();

        $this->call('db:seed');

        $this->info('Todos os comandos foram executados com sucesso.');
    }
}
