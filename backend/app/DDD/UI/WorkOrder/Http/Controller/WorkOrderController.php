<?php

declare(strict_types=1);

namespace App\DDD\UI\WorkOrder\Http\Controller;

use App\DDD\Application\WorkOrder\Services\WorkOrderService;
use App\DDD\Infrastructure\Shared\Abstracts\Controllers\AbstractController;
use App\DDD\Infrastructure\Shared\Abstracts\Exception\AbstractValidationException;
use App\DDD\Infrastructure\Shared\Http\Responses\DefaultResponse;
use App\DDD\Infrastructure\Shared\Services\NotificationService;
use App\DDD\Infrastructure\SyncData\Exceptions\NoDataFoundException;
use App\DDD\Infrastructure\SyncData\Exceptions\QueryExecutionException;
use App\DDD\UI\WorkOrder\Http\Requests\GetWorkOrdersRequest;
use Exception;
use Illuminate\Http\JsonResponse;
use InvalidArgumentException;

class WorkOrderController extends AbstractController // implements WorkOrderControllerInterface
{
    public function __construct(
        ?NotificationService $notificationService,
        private readonly WorkOrderService $workOrderService,
    ) {
        parent::__construct($notificationService, $workOrderService);
    }
    public function getWorkOrders(GetWorkOrdersRequest $request): JsonResponse
    {
        try {
            $params = ['request' => $request];
            $result = $this->workOrderService->getWorkOrders($params);

            return $this->response(response: new DefaultResponse($result));
        } catch (AbstractValidationException $e) {
            return $this->handleError($e, $request, 422);
        } catch (InvalidArgumentException $e) {
            return $this->handleError($e, $request, 400);
        } catch (NoDataFoundException $e) {
            return $this->handleError($e, $request, 404);
        } catch (QueryExecutionException $e) {
            return $this->handleError($e, $request, 500);
        } catch (Exception $e) {
            return $this->handleError($e, $request, 500, 'Internal server error');
        }
    }
}
