<?php

declare(strict_types=1);

namespace App\DDD\UI\WorkOrder\Http\Requests;

use App\DDD\Infrastructure\Shared\Interfaces\Requests\RequestInterface;
use Illuminate\Foundation\Http\FormRequest;

class GetWorkOrdersRequest extends FormRequest implements RequestInterface
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [];
    }

    /**
     * Get the signature from the request.
     */
    public function getData(): array
    {
        // Se o payload vier como JSON no corpo da requisição
        if ($this->isJson()) {
            $payload = $this->json()->all();
        } else {
            // Se vier como parâmetros no request
            $payload = $this->all();
        }

        // Retorna apenas o array da proposta
        return $payload ?? [];
    }
}
