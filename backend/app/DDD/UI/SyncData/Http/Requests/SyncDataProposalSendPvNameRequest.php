<?php

declare(strict_types=1);

namespace App\DDD\UI\SyncData\Http\Requests;

use App\DDD\Infrastructure\Shared\Interfaces\Requests\RequestInterface;
use Illuminate\Foundation\Http\FormRequest;

class SyncDataProposalSendPvNameRequest extends FormRequest implements RequestInterface
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'pv' => 'required|string',
        ];
    }

    /**
     * Get the signature from the request.
     */
    public function getData(): array | string
    {
        return $this->input('pv');
    }
}
