<?php

declare(strict_types=1);

namespace App\DDD\UI\SyncData\Http\Controller;

use App\DDD\Application\SyncData\Services\SyncDataProposalBySignatureService;
use App\DDD\Application\SyncData\Services\WebhookSyncDataProposalBySignatureService;
use App\DDD\Infrastructure\Shared\Abstracts\Controllers\AbstractController;
use App\DDD\Infrastructure\Shared\Abstracts\Exception\AbstractValidationException;
use App\DDD\Infrastructure\Shared\Http\Responses\DefaultResponse;
use App\DDD\Infrastructure\Shared\Services\NotificationService;
use App\DDD\Infrastructure\SyncData\Exceptions\NoDataFoundException;
use App\DDD\Infrastructure\SyncData\Exceptions\QueryExecutionException;
use App\DDD\UI\SyncData\Http\Requests\SyncDataProposalBySignatureRequest;
use App\DDD\UI\SyncData\Interfaces\WebhookSyncDataControllerInterface;
use Exception;
use Illuminate\Http\JsonResponse;
use InvalidArgumentException;

class WebhookSyncDataController extends AbstractController implements WebhookSyncDataControllerInterface
{
    public function __construct(
        ?NotificationService $notificationService,
        private readonly WebhookSyncDataProposalBySignatureService $syncDataService,
    ) {
        parent::__construct($notificationService, $syncDataService);
    }

    public function syncDataProposalBySignature(SyncDataProposalBySignatureRequest $request): JsonResponse
    {
        try {
            $params = ['request' => $request];
            $result = $this->syncDataService->execute($params);

            return $this->response(response: new DefaultResponse($result));
        } catch (AbstractValidationException $e) {
            return $this->handleError($e, $request, 422);
        } catch (InvalidArgumentException $e) {
            return $this->handleError($e, $request, 400);
        } catch (NoDataFoundException $e) {
            return $this->handleError($e, $request, 404);
        } catch (QueryExecutionException $e) {
            return $this->handleError($e, $request, 500);
        } catch (Exception $e) {
            return $this->handleError($e, $request, 500, 'Internal server error');
        }
    }
}
