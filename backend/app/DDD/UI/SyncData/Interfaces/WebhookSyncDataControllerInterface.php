<?php

declare(strict_types=1);

namespace App\DDD\UI\SyncData\Interfaces;

use App\DDD\Infrastructure\Shared\Interfaces\Controllers\ControllerInterface;
use App\DDD\UI\SyncData\Http\Requests\SyncDataProposalBySignatureRequest;
use Illuminate\Http\JsonResponse;

interface WebhookSyncDataControllerInterface extends ControllerInterface
{

    public function syncDataProposalBySignature(SyncDataProposalBySignatureRequest $request): JsonResponse;
}
