<?php

declare(strict_types=1);

namespace App\DDD\UI\SyncData\Interfaces;

use App\DDD\Infrastructure\Shared\Interfaces\Controllers\ControllerInterface;
use App\DDD\UI\SyncData\Http\Requests\SyncDataProposalBySignatureRequest;
use App\DDD\UI\SyncData\Http\Requests\SyncDataProposalSendPvNameRequest;
use Illuminate\Http\JsonResponse;

interface SyncDataControllerInterface extends ControllerInterface
{

    public function syncDataProposalSendPvName(SyncDataProposalSendPvNameRequest $request): JsonResponse;
}
