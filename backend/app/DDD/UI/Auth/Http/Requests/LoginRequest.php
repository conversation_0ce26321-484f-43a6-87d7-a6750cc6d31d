<?php

declare(strict_types=1);

namespace App\DDD\UI\Auth\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class LoginRequest extends FormRequest
{
    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        return [
            'userLogin' => 'required|string',
            'password' => 'required|string',
            'deviceinfo' => 'sometimes|string',
        ];
    }

    public function messages()
    {
        return [
            'userLogin.required' => 'O CPF/CNPJ é obrigatório',
            'password.required' => 'A senha é obrigatória',
        ];
    }
}
