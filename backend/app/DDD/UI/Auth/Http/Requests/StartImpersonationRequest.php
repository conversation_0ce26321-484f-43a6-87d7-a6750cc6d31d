<?php

declare(strict_types=1);

namespace App\DDD\UI\Auth\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StartImpersonationRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true; // A autorização será feita no serviço
    }

    public function rules(): array
    {
        return [
            'userLogin' => ['required'],
        ];
    }

    public function messages(): array
    {
        return [
            // 'target_user_login.required' => 'O CPF/CNPJ do cliente é obrigatório',
            // 'target_user_login.string' => 'O CPF/CNPJ deve ser uma string',
            // 'target_user_login.min' => 'O CPF/CNPJ deve ter no mínimo 11 caracteres',
            // 'target_user_login.max' => 'O CPF/CNPJ deve ter no máximo 18 caracteres',
        ];
    }
}
