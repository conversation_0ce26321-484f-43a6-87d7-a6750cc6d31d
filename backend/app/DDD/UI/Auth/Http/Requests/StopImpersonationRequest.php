<?php

declare(strict_types=1);

namespace App\DDD\UI\Auth\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StopImpersonationRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true; // A autorização será feita no serviço
    }

    public function rules(): array
    {
        return [
            'impersonation_token' => ['required', 'string'],
        ];
    }

    public function messages(): array
    {
        return [
            'impersonation_token.required' => 'O token de acesso como cliente é obrigatório',
            'impersonation_token.string' => 'O token deve ser uma string',
        ];
    }
}
