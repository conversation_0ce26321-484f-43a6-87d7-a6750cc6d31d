<?php

declare(strict_types=1);

namespace App\DDD\UI\Auth\Http\Controllers;

use App\DDD\Application\Auth\DTOs\ImpersonateDto;
use App\DDD\Application\Auth\Interfaces\AuthServiceInterface;
use App\DDD\Application\Auth\Interfaces\ImpersonateApplicationServiceInterface;
use App\DDD\Application\User\Interfaces\UserServiceInterface;
use App\DDD\Domain\User\Entities\User;
use App\DDD\UI\Auth\Http\Requests\StartImpersonationRequest;
use App\DDD\UI\Auth\Http\Requests\StopImpersonationRequest;
use App\DDD\UI\Auth\Interfaces\ImpersonationControllerInterface;
use App\Jobs\ClearTemporaryPasswordJob;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class ImpersonationController implements ImpersonationControllerInterface
{
    public function __construct(
        private ImpersonateApplicationServiceInterface $impersonateService,
        private UserServiceInterface $userService
    ) {
    }

    /**
     * Inicia uma sessão de impersonificação
     */
    public function start(StartImpersonationRequest $request): JsonResponse
    {
        try {
            // Buscar usuário pelo CPF/CNPJ
            $user = $this->userService->getUserByCpfOrCnpj($request->input('userLogin'));
            if (!$user) {
                return response()->json([
                    'success' => false,
                    'message' => 'Usuário não encontrado'
                ], 404);
            }
            $userId = $user->id;
            $dto = new ImpersonateDto(
                auth()->id(),
                $userId,
                $request->input('redirect_url', '/')
            );

            $result = $this->impersonateService->impersonate($dto);

            return response()->json([
                'success' => true,
                'token' => $result->token,
                'token_admin' => $request->bearerToken(),
                'redirect_url' => $result->getFullRedirectUrl(),
                'expires_at' => $result->expiresAt
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 400);
        }
    }

    /**
     * Valida um token de impersonificação e retorna informações do usuário
     */
    public function validateToken(string $token): JsonResponse
{
    try {
        $result = $this->impersonateService->validateImpersonation($token);

        if (!$result) {
            return response()->json([
                'success' => false,
                'message' => 'Token de impersonação inválido ou expirado'
            ], 401);
        }

        $user = User::where('id', $result->userId)->first();

        $tempToken = $user->createToken('temp_token_'.$user->AccountId)->plainTextToken;

        return response()->json([
            'success' => true,
            'token' => $result->token,
            'temp_token' => $tempToken,
            'account_id' => $user->AccountId,
            'expires_at' => $result->expiresAt,
            'user_id' => $result->userId,
            'is_impersonating' => true,
            'admin_id' => $result->adminId,
            'user' => $user // dados do usuário necessários para o frontend
        ]);
    } catch (\Exception $e) {
        return response()->json([
            'success' => false,
            'message' => 'Erro ao validar token de impersonação'
        ], 401);
    }
}


    /**
     * Encerra uma sessão de impersonificação
     */

    public function stop(Request $request): JsonResponse
    {

        $token = $request->input('impersonation_token');
        $device_info = $request->input('device_info') ?? 'unknown';
        $success = $this->impersonateService->stopImpersonation($token, $device_info);

        return response()->json([
            'success' => $success,
            'message' => $success ? 'Impersonificação finalizada com sucesso' : 'Falha ao finalizar impersonificação'
        ]);
    }

    public function tempPassword(Request $request): JsonResponse
    {
        $user = $this->userService->getUserByCpfOrCnpj($request->input('userLogin'));
        if (!$user) {
            return response()->json([
                'success' => false,
                'message' => 'Usuário não encontrado'
            ], 404);
        }

        $password = Str::random(10);
        $user->password_plaintext = Hash::make($password);
        $user->save();

        ClearTemporaryPasswordJob::dispatch($user->id)->delay(now()->addMinutes(10));

        return response()->json([
            'success' => true,
            'password' => $password
        ]);
    }
}
