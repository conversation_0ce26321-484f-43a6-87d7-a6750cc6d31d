<?php

declare(strict_types=1);

namespace App\DDD\UI\Auth\Http\Controllers;

use App\DDD\Application\Auth\Interfaces\AuthServiceInterface;
use App\DDD\Infrastructure\Shared\Abstracts\Controllers\AbstractController;
use App\DDD\Infrastructure\Shared\Http\Responses\DefaultResponse;
use App\DDD\UI\Auth\Http\Requests\LoginRequest;
use App\DDD\UI\Auth\Http\Requests\LogoutRequest;
use App\DDD\UI\Auth\Http\Requests\RefreshTokenRequest;
use App\DDD\UI\Auth\Http\Resources\AuthResource;
use App\DDD\UI\Auth\Interfaces\AuthControllerInterface;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;

class AuthControllerV2 extends AbstractController implements AuthControllerInterface
{
    public function __construct(
        private AuthServiceInterface $authService
    ) {
    }

    public function login(LoginRequest $request): JsonResponse
    {
        $response = $this->authService->login($request);
        if (! $response) {
            return $this->errorResponse('Credenciais inválidas');
        }
        return $this->response(
            new DefaultResponse(
                (new AuthResource($response->toArray()))->resolve()
            )
        );

    }

    public function refresh(RefreshTokenRequest $request): JsonResponse
    {
        $refreshToken = $request->input('refresh_token');

        $tokenDto = $this->authService->refreshToken($refreshToken);

        if (! $tokenDto) {
            return response()->json([
                'message' => 'Refresh token inválido ou expirado',
            ], 401);
        }

        return response()->json($tokenDto->toArray());
    }

    public function logout(LogoutRequest $request): JsonResponse
    {
        $user = $request->user();
        $device_info = $request->device_info;
        $this->authService->logout($user, $device_info);

        return response()->json([
            'message' => 'Logout realizado com sucesso',
        ]);
    }

    public function me(LogoutRequest $request): JsonResponse
    {
        $user = $request->user();
        // $roles = $this->authService->getUserRoles($user);

        return response()->json([
            'user' => $user,
            // 'roles' => $roles,
        ]);
    }
}
