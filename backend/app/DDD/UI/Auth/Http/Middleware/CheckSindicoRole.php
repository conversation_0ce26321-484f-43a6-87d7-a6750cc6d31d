<?php

declare(strict_types=1);

namespace App\DDD\UI\Auth\Http\Middleware;

use App\DDD\Domain\Auth\ValueObjects\UserType;
use App\DDD\Infrastructure\Auth\Interfaces\AuthRepositoryInterface;
use Closure;
use Illuminate\Http\Request;

class CheckSindicoRole
{
    public function __construct(
        private AuthRepositoryInterface $authRepository
    ) {
    }

    public function handle(Request $request, Closure $next)
    {
        $user = $request->user();

        if (! $user) {
            return response()->json(['error' => 'Unauthorized'], 401);
        }

        $roles = $this->authRepository->getUserRoles($user->id);

        // Verifica se o usuário é qualquer tipo de Síndico
        $allowedTypes = [
            UserType::SINDICO_CLIENTE->value,
            UserType::SINDICO_PROFISSIONAL->value,
            UserType::MEMBRO_PATRIMONIO->value,
        ];

        $hasAccess = collect($roles)
            ->contains(fn ($role) => in_array($role->getUserType()->value, $allowedTypes));

        if (! $hasAccess) {
            return response()->json(['error' => 'Forbidden'], 403);
        }

        return $next($request);
    }
}
