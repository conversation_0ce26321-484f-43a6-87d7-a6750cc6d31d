<?php

declare(strict_types=1);

namespace App\DDD\UI\Auth\Http\Middleware;

use App\DDD\Domain\Auth\ValueObjects\UserType;
use App\DDD\Infrastructure\Auth\Interfaces\AuthRepositoryInterface;
use Closure;
use Illuminate\Http\Request;

class CheckPermutanteRole
{
    public function __construct(
        private AuthRepositoryInterface $authRepository
    ) {
    }

    public function handle(Request $request, Closure $next)
    {
        $user = $request->user();

        if (! $user) {
            return response()->json(['error' => 'Unauthorized'], 401);
        }

        $roles = $this->authRepository->getUserRoles($user->id);

        // Verifica se o usuário é um PERMUTANTE
        $hasAccess = collect($roles)
            ->contains(fn ($role) => $role->getUserType()->value === UserType::PERMUTANTE->value);

        if (! $hasAccess) {
            return response()->json(['error' => 'Forbidden'], 403);
        }

        return $next($request);
    }
}
