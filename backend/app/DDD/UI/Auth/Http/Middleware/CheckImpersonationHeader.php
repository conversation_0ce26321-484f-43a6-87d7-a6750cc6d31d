<?php

namespace App\DDD\UI\Auth\Http\Middleware;

use App\DDD\Infrastructure\Auth\Interfaces\ImpersonateTokenServiceInterface;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class CheckImpersonationHeader
{
    private $tokenService;

    public function __construct(ImpersonateTokenServiceInterface $tokenService)
    {
        $this->tokenService = $tokenService;
    }

    // public function handle(Request $request, Closure $next)
    // {
    //     if ($request->hasHeader('X-Impersonation-Token')) {
    //         $token = $request->header('X-Impersonation-Token');
    //         $impersonationToken = $this->tokenService->validateToken($token);

    //         if (!$impersonationToken || $impersonationToken->isExpired()) {
    //             return response()->json([
    //                 'success' => false,
    //                 'message' => 'Token de impersonação inválido ou expirado'
    //             ], 401);
    //         }

    //         $request->attributes->add([
    //             'impersonating' => true,
    //             'admin_id' => $impersonationToken->getAdminId(),
    //             'impersonated_user_id' => $impersonationToken->getUserId()
    //         ]);

    //         // Autenticar como usuário impersonado
    //         // auth('api')->loginUsingId($impersonationToken->getUserId());

    //         // Adicionar headers de auditoria
    //         $response = $next($request);
    //         $response->headers->set('X-Impersonated-By', $impersonationToken->getAdminId());

    //         return $response;
    //     }

    //     return $next($request);
    // }

    public function handle(Request $request, Closure $next)
    {
        if ($request->hasHeader('X-Impersonation-Token')) {
            $token = $request->header('X-Impersonation-Token');
            $impersonationToken = $this->tokenService->validateToken($token);

            if (!$impersonationToken || $impersonationToken->isExpired()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Token de impersonação inválido ou expirado'
                ], 401);
            }

            // Apenas adiciona as informações de impersonação ao request
            $request->attributes->add([
                'impersonating' => true,
                'admin_id' => $impersonationToken->getAdminId(),
                'impersonated_user_id' => $impersonationToken->getUserId()
            ]);

            // Adicionar headers de auditoria
            $response = $next($request);
            $response->headers->set('X-Impersonated-By', $impersonationToken->getAdminId());

            return $response;
        }

        return $next($request);
    }
}