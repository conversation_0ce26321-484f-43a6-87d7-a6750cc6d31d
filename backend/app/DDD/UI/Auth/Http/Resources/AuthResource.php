<?php

declare(strict_types=1);

namespace App\DDD\UI\Auth\Http\Resources;

use App\DDD\UI\User\Http\Resources\AuthUserResource;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\Log;

class AuthResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  Request  $request
     * @return array
     */
    public function toArray($request): array
    {
        return [
            'token' => new TokenResource($this['token']),
            'user' => new AuthUserResource($this['user']),
        ];
    }
}