<?php

declare(strict_types=1);

namespace App\DDD\UI\Auth\Interfaces;

use App\DDD\UI\Auth\Http\Requests\LoginRequest;
use App\DDD\UI\Auth\Http\Requests\LogoutRequest;
use App\DDD\UI\Auth\Http\Requests\RefreshTokenRequest;
use Illuminate\Http\JsonResponse;

interface AuthControllerInterface
{
    /**
     * Realiza login e retorna token de acesso.
     */
    public function login(LoginRequest $request): JsonResponse;

    /**
     * Atualiza o token de acesso usando o refresh token.
     */
    public function refresh(RefreshTokenRequest $request): JsonResponse;

    /**
     * Realiza logout e revoga tokens.
     */
    public function logout(LogoutRequest $request): JsonResponse;

    /**
     * Retorna informações do usuário autenticado.
     */
    public function me(LogoutRequest $request): JsonResponse;
}
