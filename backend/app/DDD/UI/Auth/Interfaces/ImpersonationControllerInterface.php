<?php

namespace App\DDD\UI\Auth\Interfaces;

use App\DDD\UI\Auth\Http\Requests\ImpersonateRequest;
use App\DDD\UI\Auth\Http\Requests\StartImpersonationRequest;
use App\DDD\UI\Auth\Http\Requests\StopImpersonationRequest;
use Illuminate\Http\JsonResponse;

interface ImpersonationControllerInterface
{
    public function start(StartImpersonationRequest $request): JsonResponse;
    public function validateToken(string $token): JsonResponse;
    public function stop(StopImpersonationRequest $request): JsonResponse;
}

