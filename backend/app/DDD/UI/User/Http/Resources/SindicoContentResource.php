<?php

namespace App\DDD\UI\User\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\Log;

class SindicoContentResource extends JsonResource
{
    public function toArray($request)
    {

        $content = (array) $this->resource;
        $documents = $this->resource['documents'];

        return [
            "ProposalId" => $content['ProposalId'] ?? null,
            "ContractId" => $content['ContractId'] ?? null,
            "EmpreendimentoId" => $content['EmpreendimentoId'] ?? null,
            "type" => $content['type'] ?? null,
            'Empreendimento' => [
                'documents' => $documents,
                "EmpreendimentoId" => $content['realEstateProject']['EmpreendimentoId'],
                "name" => $content['realEstateProject']['name'],
                "LogoEmpreendimento__c" => $content['realEstateProject']['LogoEmpreendimento__c'],
                "Sindico__c" => $content['realEstateProject']['Sindico__c'],
                "StatusMacro__c" => $content['realEstateProject']['StatusMacro__c'],
                "imgsCarrossel" => $content['realEstateProject']['imgsCarrossel'],
                "Fotos__c" => $content['realEstateProject']['Fotos__c'],
                "Videos__c" => $content['realEstateProject']['Videos__c'],
            ],
        ];
    }
}
