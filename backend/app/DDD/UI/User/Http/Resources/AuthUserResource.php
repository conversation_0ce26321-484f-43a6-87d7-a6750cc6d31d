<?php

declare(strict_types=1);

namespace App\DDD\UI\User\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class AuthUserResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  Request  $request
     * @return array
     */
    public function toArray($request): array
    {
        return [
            'AccountId' => $this['AccountId'],
            // 'PersonContactId' => $this['PersonContactId'],
            // 'CodigoSienge__c' => $this['CodigoSienge__c'],
            // 'avatar' => $this['avatar'],
            'Email__c' => $this['Email__c'],
            // 'Name' => $this['Name'],
            // 'FirstName' => $this['FirstName'],
            // 'LastName' => $this['LastName'],
            'CPF__c' => $this['CPF__c'],
            'CNPJ__c' => $this['CNPJ__c'],
            // 'EmailAlternativo__c' => $this['EmailAlternativo__c'],
            'email_verified_at' => $this['email_verified_at'],
            // 'alternative_name' => $this['alternative_name'],
            // 'contents' => $this->when(isset($this['contents']), $this['contents']),
        ];
    }
}