<?php

declare(strict_types=1);

namespace App\DDD\UI\User\Http\Handlers;

use App\DDD\Application\User\Services\UserService;
use App\DDD\Infrastructure\Shared\Interfaces\Handlers\HandlerInterface;

class UserControllerHandler implements HandlerInterface
{
    public function __construct(
        private readonly UserService $userService
    ) {
    }

    public function handleCreate(array|object|string|int $data): void
    {
        // $this->createByPropostaName($data);
    }

    public function handleUpdate(array|object|string|int $data): void
    {
        // $this->updateByPropostaName($data);
    }

    public function handleDelete(array|object|string|int $data): void
    {
        // $this->deleteByPropostaName($data);
    }
}
