<?php

declare(strict_types=1);

namespace App\DDD\UI\User\Http\Controllers;

use App\DDD\Domain\User\Exceptions\UserNotFoundException;
use App\DDD\Infrastructure\Shared\Abstracts\Controllers\AbstractController;
use App\DDD\Infrastructure\Shared\Controllers\Http\Responses\DefaultResponse;
use App\DDD\UI\Knowledge\Http\Resources\KnowledgeResource;
use App\DDD\UI\User\Http\Handlers\UserControllerHandler;
use App\DDD\UI\User\Http\Requests\CreateUserRequest;
use Illuminate\Http\JsonResponse;

class UserUIController extends AbstractController
{
    public function __construct(
        private readonly UserControllerHandler $handler
    ) {
    }

    public function createByPropostaName(CreateUserRequest $request): JsonResponse
    {
        try {
            $user = $this->handler->createByPropostaName($request->pv);

            return $this->response(new DefaultResponse(new KnowledgeResource($user)));
        } catch (\ValueError $e) {
            return $this->errorResponse('ValueError', $e->getMessage(), 404);
        } catch (UserNotFoundException $e) {
            return $this->errorResponse('UserNotFoundException', $e->getMessage(), $e->getCode());
        } catch (\Exception $e) {
            return $this->errorResponse('Exception', $e->getMessage(), 500);
        }
    }
}
