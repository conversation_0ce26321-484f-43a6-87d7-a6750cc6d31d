<?php

declare(strict_types=1);

    namespace App\DDD\UI\Assets\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class GetUserAssetRequest extends FormRequest
{
    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        return [
            // 'AccountId' => 'required|string',
            'page' => 'required|integer',
            'perPage' => 'required|integer',
        ];
    }

    public function messages()
    {
        return [
            'page.required' => 'A página é obrigatória',
            'perPage.required' => 'O número de itens por página é obrigatório',
        ];
    }
}
