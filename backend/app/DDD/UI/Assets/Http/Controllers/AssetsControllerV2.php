<?php

namespace App\DDD\UI\Assets\Http\Controllers;

use App\DDD\Infrastructure\Asset\Persistence\Repositories\AssetRepository;
use App\DDD\Infrastructure\Shared\Abstracts\Controllers\AbstractController;
use App\DDD\Infrastructure\Shared\Http\Responses\DefaultResponse;
use App\DDD\UI\Assets\Http\Resources\UserAssetsResource;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Log;

class AssetsControllerV2 extends AbstractController
{
    public function __construct(
        private AssetRepository $assetRepository
    ) {
    }

    public function getUserAssets(Request $request)
    {
        $user = $request->user();
        $AccountId = $user->AccountId;
        Log::info('request->all()');
        Log::info($request->all());
        Log::info($request->input('perPage'));

        // Obter parâmetros de paginação da requisição
        $page = max(1, intval($request->input('page', 1)));
        $perPage = $request->input('perPage') ?? 20;

        // Usar o método paginationWithFilter para obter dados paginados
        $asset = $this->assetRepository->paginationWithFilter(
            ['AccountId' => $AccountId], // filtro
            [
                'id',
                'AccountId',
                'AssetId',
                'Name',
                'ProposalId',
                'ContractId',
                'EmpreendimentoId',
            ], // colunas a selecionar
            $page, // página atual
            $perPage, // itens por página
            [
                'realEstateProject' => function ($query) {
                    $query->select('EmpreendimentoId', 'name', 'LogoEmpreendimento__c');
                }
            ], // relacionamentos
            'created_at', // ordenar por
            'desc' // direção da ordenação
        );

        // Obter os itens da página atual
        $assetItems = $asset->items();

        if (count($user->sindico) === 0) {
            return $this->response(
                new DefaultResponse([
                    'contents' => new UserAssetsResource($assetItems),
                    'pagination' => [
                        'total' => $asset->total(),
                        'per_page' => $asset->perPage(),
                        'current_page' => $asset->currentPage(),
                        'last_page' => $asset->lastPage(),
                        'from' => $asset->firstItem(),
                        'to' => $asset->lastItem(),
                    ]
                ])
            );
        }

        $sindicos = $user->sindico->map(function ($sindico) use ($user) {
            return [
                // 'id' => $sindico->id,
                'AccountId' => $user->AccountId,
                'ProposalId' => "p{$user->AccountId}",
                'ContractId' => "c{$sindico->EmpreendimentoId}",
                // 'Name' => $user->getSindicoName($sindico),
                'Name' => 'Síndico',
                'documents' => $sindico->documents()->get()->toArray(),
                'realEstateProject' => $sindico->toArray(),
                'EmpreendimentoId' => $sindico->EmpreendimentoId,
                'type' => $sindico->StatusMacro__c !== 'Entregue' ? 'membro' : 'sindico',
            ];
        });

        // Combinar os itens da página atual com os dados de síndico
        $data = collect($assetItems)->concat($sindicos);

        return $this->response(
            new DefaultResponse([
                'contents' => new UserAssetsResource($data),
                'pagination' => [
                    'total' => $asset->total() + count($sindicos), // Adicionar o número de síndicos ao total
                    'per_page' => $asset->perPage(),
                    'current_page' => $asset->currentPage(),
                    'last_page' => ceil(($asset->total() + count($sindicos)) / $asset->perPage()), // Recalcular última página
                    'from' => $asset->firstItem(),
                    'to' => $asset->lastItem(),
                ]
            ])
        );

        // });
    }
}
