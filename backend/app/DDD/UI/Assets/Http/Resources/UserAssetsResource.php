<?php

namespace App\DDD\UI\Assets\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\Log;

class UserAssetsResource extends JsonResource
{
    public function toArray($request)
    {
        $data = [];

        foreach ($this->resource as $key => $value) {
            // Log::info('UserAssetsResource');
            // Log::info($value);
            // $documents = $value['documents']->toArray();
            $data[] = [
                "AccountId" => $value['AccountId'] ?? null,
                "AssetId" => $value['AssetId'] ?? null,
                "UnidadeAtivoName" => $value['Name'] ?? null,
                "ProposalId" => $value['ProposalId'] ?? null,
                "ContractId" => $value['ContractId'] ?? null,
                "EmpreendimentoId" => $value['EmpreendimentoId'] ?? null,
                // 'documents' => $documents,
                "type" => $value['type'] ?? 'morador',
                "Empreendimento" => [
                    "EmpreendimentoId" => $value['realEstateProject']['EmpreendimentoId'] ?? null,
                    "name" => $value['realEstateProject']['name'] ?? null,
                    "LogoEmpreendimento__c" => $value['realEstateProject']['LogoEmpreendimento__c'] ?? null,
                    "imgsCarrossel" => $value['realEstateProject']['imgsCarrossel'] ?? [],
                ],

            ];
        }
        return $data;
    }
}