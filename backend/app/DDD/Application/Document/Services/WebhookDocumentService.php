<?php

declare(strict_types=1);

namespace App\DDD\Application\Document\Services;

use App\DDD\Application\Document\Interfaces\DocumentServiceInterface;
use App\DDD\Application\Document\Services\Dependencies\DocumentDependencies;
use App\DDD\Domain\Document\Interfaces\DocumentValidationInterface;
use App\DDD\Infrastructure\Document\Factory\DocumentQueryFactory;
use App\DDD\Infrastructure\Document\Interfaces\DocumentProcessorInterface;
use App\DDD\Infrastructure\Document\Persistence\Interfaces\DocumentRepositoryInterface;
use App\DDD\Infrastructure\Shared\Abstracts\DTO\AbstractDto;
use App\DDD\Infrastructure\Shared\Abstracts\Services\AbstractService;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;

//IMPLEMENTAR - SERVICE DO ENDPOINT SALESFORCE
class WebhookDocumentService extends AbstractService implements DocumentServiceInterface
{
    use DocumentDependencies;

    private $queryFactory;

    public function __construct(
        private readonly DocumentRepositoryInterface $documentRepository,
        private readonly DocumentValidationInterface $documentValidator,
        private readonly DocumentProcessorInterface $processorService
    ) {
        parent::__construct(
            $documentRepository,
            $documentValidator,
            $processorService
        );
        $this->queryFactory = new DocumentQueryFactory();
    }

    /**
     * Transform data using the transform component.
     */
    public function processWebhookData(array $data, AbstractService $service): Model|Collection|array|null|AbstractDto
    {
        $data['proposta']['documents']['attributes']['type'] = 'proposta';
        dd($data['proposta']['documents']);
        return $this->processorService->process($data['proposta']['documents']);

    }
}
