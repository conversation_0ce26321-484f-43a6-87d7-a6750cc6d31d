<?php

declare(strict_types=1);

namespace App\DDD\Application\Document\Services\Dependencies;

use App\DDD\Domain\Document\Interfaces\DocumentValidationInterface;
use App\DDD\Infrastructure\Document\Interfaces\DocumentProcessorInterface;
use App\DDD\Infrastructure\Document\Persistence\Interfaces\DocumentRepositoryInterface;

trait DocumentDependencies
{
    public function repository(): string
    {
        return DocumentRepositoryInterface::class;
    }

    public function validator(): string
    {
        return DocumentValidationInterface::class;
    }

    public function processor(): string
    {
        return DocumentProcessorInterface::class;
    }
}
