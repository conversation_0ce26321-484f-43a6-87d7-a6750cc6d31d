<?php

declare(strict_types=1);

namespace App\DDD\Application\Document\Services;

use App\DDD\Application\Document\Interfaces\DocumentServiceInterface;
use App\DDD\Application\Document\Services\Dependencies\DocumentDependencies;
use App\DDD\Domain\Document\Interfaces\DocumentValidationInterface;
use App\DDD\Infrastructure\Document\Factory\DocumentQueryFactory;
use App\DDD\Infrastructure\Document\Interfaces\DocumentProcessorInterface;
use App\DDD\Infrastructure\Document\Persistence\Interfaces\DocumentRepositoryInterface;
use App\DDD\Infrastructure\Shared\Abstracts\Services\AbstractService;

class DocumentService extends AbstractService implements DocumentServiceInterface
{
    use DocumentDependencies;

    private $queryFactory;

    public function __construct(
        private readonly DocumentRepositoryInterface $documentRepository,
        private readonly DocumentValidationInterface $documentValidator,
        private readonly DocumentProcessorInterface $processorService
    ) {
        parent::__construct(
            $documentRepository,
            $documentValidator,
            $processorService
        );
        $this->queryFactory = new DocumentQueryFactory();
    }

}
