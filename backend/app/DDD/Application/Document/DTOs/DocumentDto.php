<?php

declare(strict_types=1);

namespace App\DDD\Application\Document\DTOs;

use App\DDD\Application\Document\Schema\DocumentSchema;
use App\DDD\Infrastructure\Shared\Abstracts\DTO\AbstractDto;
use App\DDD\Infrastructure\Shared\Interfaces\DTO\DtoInterface;

/**
 * DTO base para operações com documentos
 */
class DocumentDto extends AbstractDto implements DtoInterface
{
    public static function getSchemaClass(): string
    {
        return DocumentSchema::class;
    }

    public static function getClass(array $data = []): self
    {
        return new self($data);
    }
}
