<?php

declare(strict_types=1);

namespace App\DDD\Application\Document\Schema;

use App\DDD\Infrastructure\Shared\Abstracts\Entities\Schema\AbstractSchema;

class DocumentSchema extends AbstractSchema
{
    protected static function getConcreteClass(): string
    {
        return self::class;
    }

    public const TABLE_NAME = 'documents';

    public const ID_NAME = 'DocumentId';

    public const FIELDS = [
        'DocumentId' => '',
        'AssetId' => '',
        'AccountId' => '',
        'EmpreendimentoId' => '',
        'ContractId' => '',
        'ProposalId' => '',
        'Subgrupo__c' => '',
        'TituloArquivo__c' => '',
        'IdInterno__c' => '',
        'Link__c' => '',
        'VisivelCliente__c' => 'boolean',
        'LinkS3' => '',
        'Name' => '',
    ];

    private const DEFAULT_SALESFORCE_FIELDS = [
        'Id',
        'Link__c',
        'Subgrupo__c',
        'TituloArquivo__c',
        'VisivelCliente__c',
        'IdInterno__c',
    ];

    public const SALESFORCE_FIELDS = [
        'Planta__c' => self::DEFAULT_SALESFORCE_FIELDS,
        'Proposta__c' => self::DEFAULT_SALESFORCE_FIELDS,
        'proposta' => self::DEFAULT_SALESFORCE_FIELDS,
        'Documento__c' => self::DEFAULT_SALESFORCE_FIELDS,
    ];

    public const MESSAGES_DATABASE = 'Document## Dados inválidos: ';

    public const MESSAGES_SALESFORCE = 'Document## Dados Salesforce inválidos: ';

    public const REQUIRED_FIELDS_SALESFORCE = [
        'Planta__c' => [
            'Id',
        ],
        'Proposta__c' => [
            'Id',
        ],
        'Documento__c' => [
            'Id',
            'IdInterno__c',
        ],
    ];

    public const REQUIRED_FIELDS_DATABASE = [
        'DocumentId',
        'IdInterno__c',
        'Link__c',
    ];

    /**
     * Define quais campos usar para retry quando a validação falha
     * A chave é o campo no banco de dados, o valor é o campo no Salesforce
     */
    public const RETRY_SALESFORCE = [];

    /**
     * Define qual tipo de objeto consultar no retry
     * A chave é o tipo original, o valor é o tipo a ser consultado
     */
    public const RETRY_OBJECT_TYPES = [];

    public const RELATIONSHIPS = [
        'user' => [
            'related_model' => 'App\DDD\Domain\User\Entities\User',
            'primary' => [
                'foreign_key' => 'AccountId',
                'local_key' => 'AccountId',
            ],
        ],
        'realEstateProject' => [
            'related_model' => 'App\DDD\Domain\RealEstateProject\Entities\RealEstateProject',
            'primary' => [
                'foreign_key' => 'EmpreendimentoId',
                'local_key' => 'EmpreendimentoId',
            ],
        ],
        'contracts' => [
            'related_model' => 'App\DDD\Domain\Contract\Entities\Contract',
            'primary' => [
                'foreign_key' => 'ContractId',
                'local_key' => 'ContractId',
            ],
            'conditions' => [
                [
                    'foreign_key' => 'AccountId',
                    'local_key' => 'AccountId',
                ],
            ],
        ],
        'proposal' => [
            'related_model' => 'App\DDD\Domain\Proposal\Entities\Proposal',
            'primary' => [
                'foreign_key' => 'ProposalId',
                'local_key' => 'ProposalId',
            ],
            'conditions' => [
                [
                    'foreign_key' => 'AccountId',
                    'local_key' => 'AccountId',
                ],
            ],
        ],
    ];
}
