<?php

declare(strict_types=1);

namespace App\DDD\Application\Auth\Assemblers;


use App\DDD\Application\Asset\Assemblers\AuthAssetAssembler;
use App\DDD\Application\User\DTOs\AuthUserDto;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\Log;

class AuthUserAssembler
{
    private AuthAssetAssembler $assetAssembler;

    public function __construct(AuthAssetAssembler $assetAssembler)
    {
        $this->assetAssembler = $assetAssembler;
    }

    /**
     * Transforma dados brutos de usuário em UserDto para contexto de autenticação
     */
    public function toDto(array $userData): AuthUserDto
    {

        $authUserData = [
            "AccountId" => $userData['AccountId'],
            // "PersonContactId" => $userData['PersonContactId'],
            // "CodigoSienge__c" => $userData['CodigoSienge__c'],
            // "avatar" => $userData['avatar'],
            "Email__c" => $userData['Email__c'],
            // "Name" => $userData['Name'],
            // "FirstName" => $userData['FirstName'],
            // "LastName" => $userData['LastName'],
            "CPF__c" => $userData['CPF__c'],
            "CNPJ__c" => $userData['CNPJ__c'],
            // "EmailAlternativo__c" => $userData['EmailAlternativo__c'],
            "email_verified_at" => $userData['email_verified_at'],
            // "contents" => $userData ['contents'],
            // "alternative_name" => $userData['alternative_name'],
        ];

        // $content = [];
        // if (isset($userData['assets']) && is_array($userData['assets'])) {
        //     // foreach ($userData['assets'] as $assetData) {
        //     // $userData['assets']->chunk(50, function ($assets) use (&$content) {
        //     //     $content[] = $this->assetAssembler->toDto($assets)->toArray();
        //     // });

        //     // $assetsCollection = new Collection($userData['assets']);
        //     // $assetsCollection->chunk(50, function ($assets) use (&$content) {
        //     //     $content[] = $this->assetAssembler->toDto($assets)->toArray();
        //     // });
        //     Log::debug('userData', ['userData' => $userData['assets']]);
        //     $chunks = array_chunk($userData['assets'], 50);
        //     foreach ($chunks as $chunk) {
        //         $content[] = $this->assetAssembler->toDto($chunk)->toArray();
        //     }
        //     // }
        // }

        // $authUserData['contents'] = $userData  ['contents'];

        return new AuthUserDto($authUserData);
    }
}