<?php

namespace App\DDD\Application\Auth\Interfaces;

use App\DDD\Application\Auth\DTOs\ImpersonateDto;
use App\DDD\Application\Auth\DTOs\ImpersonationTokenDto;

interface ImpersonateApplicationServiceInterface
{
    public function impersonate(ImpersonateDto $dto): ImpersonationTokenDto;
    public function validateImpersonation(string $token): ?ImpersonationTokenDto;
    public function stopImpersonation(string $token, string $device_info): bool;
}