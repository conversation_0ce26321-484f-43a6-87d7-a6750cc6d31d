<?php

declare(strict_types=1);

namespace App\DDD\Application\Auth\Interfaces;

use App\DDD\Application\Auth\DTOs\AuthDto;
use App\DDD\Application\Auth\DTOs\LoginDto;
use App\DDD\Application\Auth\DTOs\TokenDto;
use App\DDD\Domain\User\Entities\User;
use App\DDD\UI\Auth\Http\Requests\LoginRequest;
use Illuminate\Database\Eloquent\Model;

interface AuthServiceInterface
{
    /**
     * Realiza o login de um usuário e gera um token de acesso.
     *
     * @param  LoginDto  $loginDto
     */
    public function login(LoginRequest $request): ?AuthDto;

    /**
     * Atualiza o token de acesso usando o refresh token.
     */
    public function refreshToken(string $refreshToken): ?TokenDto;

    /**
     * Encerra a sessão do usuário revogando seus tokens.
     */
    public function logout(User $user, $device_info): bool;

    /**
     * Obtém os roles de um usuário.
     */
    // public function getUserRoles(User $user): array;

    // /**
    //  * Verifica se um usuário tem permissão para executar uma ação.
    //  */
    // public function hasPermission(User $user, string $permission, ?int $entityId = null): bool;
}
