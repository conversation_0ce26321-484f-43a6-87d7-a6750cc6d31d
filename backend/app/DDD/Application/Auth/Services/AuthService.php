<?php

declare(strict_types=1);

namespace App\DDD\Application\Auth\Services;

use App\DDD\Application\Auth\Assemblers\AuthUserAssembler;
use App\DDD\Application\Auth\DTOs\AuthDto;
use App\DDD\Application\Auth\DTOs\LoginDto;
use App\DDD\Application\Auth\DTOs\TokenDto;
use App\DDD\Application\Auth\Interfaces\AuthServiceInterface;
use App\DDD\Domain\User\Entities\User;
use App\DDD\Infrastructure\Auth\Interfaces\AuthRepositoryInterface;
use App\DDD\Infrastructure\Auth\Interfaces\TokenServiceInterface;
use App\DDD\UI\Auth\Http\Requests\LoginRequest;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Cookie;
use Throwable;

class AuthService implements AuthServiceInterface
{
    public function __construct(
        private AuthRepositoryInterface $authRepository,
        private TokenServiceInterface $tokenService,
        private AuthUserAssembler $authUserAssembler
    ) {
    }

    public function login(LoginRequest $request): ?AuthDto
    {
        $loginDto = new LoginDto(
            $request->userLogin,
            $request->password,
            $request->deviceinfo
        );

        $userData = $this->authRepository->find(
            $loginDto->getUserLogin()
        );
        // Verificar se usuário existe
        if (!$userData) {
            return null;
        }

        if (!$this->authRepository->validateCredentials($userData, $loginDto->getPassword())) {
            return null;
        }
        $tokenData = $this->tokenService->createToken($userData, $loginDto->getDeviceInfo());

        // Criar TokenDto
        $tokenDto = new TokenDto(
            $tokenData
        );

        // Usar o assembler para criar UserDto para autenticação
        $userDto = $this->authUserAssembler->toDto($userData->toArray());

        // Retornar AuthDto com token e usuário
        return new AuthDto($tokenDto, $userDto);

    }

    public function refreshToken(string $refreshToken): ?TokenDto
    {

        $tokens = $this->tokenService->refreshToken($refreshToken);

        if (!$tokens) {
            return null;
        }

        // Precisamos obter o usuário a partir do refresh token
        $refreshTokenEntity = $this->tokenService->validateRefreshToken($tokens['refreshToken']);

        if (!$refreshTokenEntity) {
            return null;
        }

        $userId = $refreshTokenEntity->getUserId();

        // Idealmente, isso deveria ser injetado ou obtido de alguma forma mais limpa
        $userModel = app()->make(User::class)->find($userId);

        if (!$userModel) {
            return null;
        }


        return new TokenDto(
            $tokens['token'],
            $tokens['refresh_token'],
            $userModel,
            // $roles
        );
    }

    public function logout(User $user, $device_info): bool
    {
        // return $this->tokenService->revokeTokens($user);
        return $this->tokenService->revokeToken($user, $device_info);
    }



}
