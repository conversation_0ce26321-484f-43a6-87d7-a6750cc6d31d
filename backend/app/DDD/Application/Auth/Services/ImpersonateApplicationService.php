<?php

namespace App\DDD\Application\Auth\Services;

use App\DDD\Application\Auth\DTOs\ImpersonateDto;
use App\DDD\Application\Auth\DTOs\ImpersonationTokenDto;
use App\DDD\Application\Auth\Interfaces\ImpersonateApplicationServiceInterface;
use App\DDD\Domain\Auth\Interfaces\ImpersonationServiceInterface;
use App\DDD\Infrastructure\Auth\Interfaces\ImpersonateTokenServiceInterface;
use App\DDD\Infrastructure\User\Interfaces\UserValidationInterface;
use Illuminate\Support\Facades\Log;

class ImpersonateApplicationService implements ImpersonateApplicationServiceInterface
{
    private ImpersonationServiceInterface $impersonationService;
    private ImpersonateTokenServiceInterface $tokenService;
    private UserValidationInterface $userValidation;

    public function __construct(
        ImpersonationServiceInterface $impersonationService,
        ImpersonateTokenServiceInterface $tokenService,
        UserValidationInterface $userValidation
    ) {
        $this->impersonationService = $impersonationService;
        $this->tokenService = $tokenService;
        $this->userValidation = $userValidation;
    }

    public function impersonate(ImpersonateDto $dto): ImpersonationTokenDto
    {

        // Validar se o usuário admin tem permissão
        if (!$this->userValidation->isAdmin($dto->adminId)) {
            throw new \Exception('User does not have admin permissions');
        }

        // Validar se o usuário a ser impersonificado existe
        if (!$this->userValidation->exists($dto->userId)) {
            throw new \Exception('Target user does not exist');
        }

        // Criar token de impersonificação
        $domainToken = $this->impersonationService->createImpersonationToken(
            $dto->adminId,
            $dto->userId
        );

        // Persistir token
        $this->tokenService->storeToken($domainToken);

        // Converter para DTO da aplicação
        return new ImpersonationTokenDto(
            $domainToken->getToken(),
            $domainToken->getAdminId(),
            $domainToken->getUserId(),
            $dto->redirectUrl,
            $domainToken->getExpiresAt()->format('c'),
            true
        );
    }

    public function validateImpersonation(string $token): ?ImpersonationTokenDto
    {
        $domainToken = $this->tokenService->validateToken($token);

        if (!$domainToken || $domainToken->isExpired()) {
            return null;
        }

        // Converter para DTO da aplicação
        return new ImpersonationTokenDto(
            $domainToken->getToken(),
            $domainToken->getAdminId(),
            $domainToken->getUserId(),
            '',
            $domainToken->getExpiresAt()->format('c'),
            true
        );
    }



    public function stopImpersonation(string $token, string $device_info): bool
    {
        return $this->tokenService->revokeImpersonateToken($token, $device_info);
    }
}