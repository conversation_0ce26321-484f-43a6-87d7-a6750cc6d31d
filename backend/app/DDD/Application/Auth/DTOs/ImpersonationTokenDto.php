<?php

declare(strict_types=1);

namespace App\DDD\Application\Auth\DTOs;

use App\DDD\Infrastructure\Shared\Abstracts\DTO\AbstractDto;
use DateTime;

class ImpersonationTokenDto extends AbstractDto
{
    public function __construct(
        string $token,
        int $adminId,
        int $userId,
        string $redirectUrl = '/',
        ?string $expiresAt = null,
        bool $isImpersonating = true
    ) {
        $data = [
            'token' => $token,
            'adminId' => $adminId,
            'userId' => $userId,
            'redirectUrl' => $redirectUrl,
            'expiresAt' => $expiresAt ?? (new DateTime('+2 hours'))->format('c'),
            'isImpersonating' => $isImpersonating
        ];

        parent::__construct($data);
    }

    public static function getSchemaClass(): string
    {
        return ''; // Sem schema específico
    }

    protected static function getClass(array $data = []): self
    {
        return new self(
            $data['token'] ?? '',
            $data['adminId'] ?? 0,
            $data['userId'] ?? 0,
            $data['redirectUrl'] ?? '/',
            $data['expiresAt'] ?? null,
            $data['isImpersonating'] ?? true
        );
    }

    /**
     * Verifica se o token expirou
     */
    public function isExpired(): bool
    {
        $expiryDate = new DateTime($this->expiresAt);
        $now = new DateTime();

        return $now > $expiryDate;
    }

    /**
     * Gera a URL completa para redirecionamento com o token
     */
    public function getFullRedirectUrl(): string
    {
        $separator = parse_url($this->redirectUrl, PHP_URL_QUERY) ? '&' : '?';
        return $this->redirectUrl . $separator . 'impersonate_token=' . $this->token;
    }
}