<?php

declare(strict_types=1);

namespace App\DDD\Application\Auth\DTOs;

class LoginDto
{
    public string $userLogin;

    public string $password;

    public string $deviceinfo;

    public function __construct(
        string $userLogin,
        string $password,
        string $deviceinfo = 'web'
    ) {
        $this->userLogin = $userLogin;
        $this->password = $password;
        $this->deviceinfo = $deviceinfo;
    }

    public function getUserLogin(): string
    {
        return $this->userLogin;
    }

    public function getPassword(): string
    {
        return $this->password;
    }

    public function getDeviceinfo(): string
    {
        return $this->deviceinfo;
    }
}
