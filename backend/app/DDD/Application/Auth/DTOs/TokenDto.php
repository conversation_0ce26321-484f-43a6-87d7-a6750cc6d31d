<?php

declare(strict_types=1);

namespace App\DDD\Application\Auth\DTOs;

use App\DDD\Infrastructure\Shared\Abstracts\DTO\AbstractDto;

class TokenDto extends AbstractDto
{
    public static function getSchemaClass(): string
    {
        return ''; // LoginSchema::class;
    }

    protected static function getClass(array $data = []): self
    {
        return new self(
            $data['token'],
        );
    }
}
