<?php

declare(strict_types=1);

namespace App\DDD\Application\Auth\DTOs;

use App\DDD\Infrastructure\Shared\Abstracts\DTO\AbstractDto;

class ImpersonateDto extends AbstractDto
{
    public function __construct(
        int $adminId,
        int $userId,
        string $redirectUrl = '/'
    ) {
        $data = [
            'adminId' => $adminId,
            'userId' => $userId,
            'redirectUrl' => $redirectUrl
        ];

        parent::__construct($data);
    }

    public static function getSchemaClass(): string
    {
        return ''; // Sem schema específico
    }

    protected static function getClass(array $data = []): self
    {
        return new self(
            $data['adminId'] ?? 0,
            $data['userId'] ?? 0,
            $data['redirectUrl'] ?? '/'
        );
    }

    /**
     * Cria um DTO de impersonificação a partir de um array de dados
     */
    public static function fromRequest(array $data): self
    {
        return new self(
            $data['admin_id'] ?? auth()->id(),
            $data['user_id'] ?? 0,
            $data['redirect_url'] ?? '/'
        );
    }
}