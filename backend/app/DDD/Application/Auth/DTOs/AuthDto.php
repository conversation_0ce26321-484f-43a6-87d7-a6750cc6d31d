<?php

declare(strict_types=1);

namespace App\DDD\Application\Auth\DTOs;

use App\DDD\Application\User\DTOs\AuthUserDto;
use App\DDD\Infrastructure\Shared\Abstracts\DTO\AbstractDto;

class AuthDto extends AbstractDto
{

    public function __construct(
        public TokenDto $tokenDto,
        public AuthUserDto $authUserDto,
    ) {
    }

    public function toArray(): array
    {
        return [
            'token' => $this->tokenDto->toArray(),
            'user' => $this->authUserDto->toArray(),
        ];
    }

    public static function getSchemaClass(): string
    {
        return ''; // LoginSchema::class;
    }

    protected static function getClass(array $data = []): self
    {
        return new self(
            $data['tokenDto'],
            $data['authUserDto'],

        );
    }
}
