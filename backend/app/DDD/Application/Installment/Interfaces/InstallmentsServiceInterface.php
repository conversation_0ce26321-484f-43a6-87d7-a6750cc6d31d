<?php

declare(strict_types=1);

namespace App\DDD\Application\Installment\Interfaces;

interface InstallmentsServiceInterface
{
    public function getInstallments(string $customerId, string $unitNumber): array|string;

    public function checkBoletoAto(string $customerId, string $unitNumber): array|string;

    public function getBoleto(string $billReceivableId, string $installmentId, string $unidadeAtivoName): array|string;

    public function downloadAndSavePdf(string $pdfUrl, string $name, string $password = '08104526774'): array|string;
}
