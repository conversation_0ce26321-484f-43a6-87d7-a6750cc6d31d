<?php

declare(strict_types=1);

namespace App\DDD\Application\Installment\Services;

use App\DDD\Application\Installment\Interfaces\InstallmentsServiceInterface;
use App\DDD\Domain\Installment\Services\InstallmentQueriesService;
use App\DDD\Domain\Installment\ValueObjects\InstallmentQueryTypeEnum;
use App\DDD\Infrastructure\Installment\Factory\InstallmentQueryFactory;
use App\DDD\Infrastructure\Shared\Abstracts\Services\AbstractService;
use Exception;
use Illuminate\Support\Facades\Http;
use Smalot\PdfParser\Exception\SecurityException;
use Smalot\PdfParser\Parser;

class InstallmentsService extends AbstractService implements InstallmentsServiceInterface
{
    private InstallmentQueryFactory $queryFactory;

    public function __construct(

    ) {
        parent::__construct();
        $this->queryFactory = new InstallmentQueryFactory();
    }

    public function getInstallments(string $customerId, string $unitNumber): array|string
    {
        if (! isset($customerId) || ! isset($unitNumber)) {
            return 'Usuário não cadastrado no sienge ou sem unidade cadastradada.';
        }

        $paramsQuery = ['customerId' => $customerId, 'unitNumber' => $unitNumber];
        $this->siengeService->setExecutor('query');
        $queryType = InstallmentQueryTypeEnum::GET_INSTALLMENTS;
        $queryService = new InstallmentQueriesService($paramsQuery, $this->queryFactory, $queryType);
        $dataCheck = ['query' => $queryService->execute()];

        return $this->siengeService->execute($dataCheck);
    }

    public function checkBoletoAto(string|null $customerId, string|null $unitNumber): array|string
    {
        if (! isset($customerId) || ! isset($unitNumber)) {
            return 'boleto-nao-encontrado';
        }
        $paramsQuery = ['customerId' => $customerId, 'unitNumber' => $unitNumber];
        $this->siengeService->setExecutor('query');
        $queryType = InstallmentQueryTypeEnum::GET_BOLETO_ATO;
        $queryService = new InstallmentQueriesService($paramsQuery, $this->queryFactory, $queryType);
        $dataCheck = ['query' => $queryService->execute()];
        $data = $this->siengeService->execute($dataCheck);
        if (empty($data) || ! isset($data['data'])) {
            return 'boleto-nao-encontrado';
        }

        foreach ($data['data'] as $item) {
            foreach ($item['installments'] as $installment) {
                if ($installment['paymentTerms']['descrition'] === 'Permuta') {
                    return 'Recebimento';
                }
                if ($installment['currentBalance'] == 0.0 && count($installment['receipts']) > 0) {
                    return 'Recebimento';
                }
            }
        }

        foreach ($data['data'] as $item) {
            foreach ($item['installments'] as $installment) {
                if ($installment['generatedBillet']) {
                    return 'boleto-gerado';
                }
            }
        }

        return 'boleto-nao-encontrado';
    }

    public function getBoleto(string $billReceivableId, string $installmentId, string $unidadeAtivoName): array|string
    {
        if (! isset($billReceivableId) || ! isset($installmentId) || ! isset($unidadeAtivoName)) {
            return 'Usuário não cadastrado no sienge ou sem unidade cadastradada.';
        }

        // $response = $this->executeQuery("v1/payment-slip-notification/?billReceivableId={$billReceivableId}&installmentId={$installmentId}");

        $paramsQuery = ['billReceivableId' => $billReceivableId, 'installmentId' => $installmentId];
        $this->siengeService->setExecutor('query');
        $queryType = InstallmentQueryTypeEnum::GET_BOLETO;
        $queryService = new InstallmentQueriesService($paramsQuery, $this->queryFactory, $queryType);
        $dataCheck = ['query' => $queryService->execute()];
        $data = $this->siengeService->execute($dataCheck);

        $nameFile = "pdf/boleto-{$unidadeAtivoName}-{$installmentId}.pdf";
        $responseBoleto = [];
        if (! $data) {
            $responseBoleto['status'] = 'boleto-nao-encontrado';
        } else {
            $responseBoleto['nameFile'] = $nameFile;
            $responseBoleto['status'] = 'boleto-gerado';
            $responseBoleto['digitableNumber'] = $data['results'][0]['digitableNumber'];
            $this->downloadAndSavePdf($data['results'][0]['urlReport'], $nameFile);
        }

        return $responseBoleto;
    }

    public function downloadAndSavePdf(string $pdfUrl, string $name, string $password = '08104526774'): array|string
    {
        try {
            // Baixa o PDF
            $response = Http::get($pdfUrl);
            if (! $response->successful()) {
                return 'Erro ao baixar o PDF: '.$response->status();
            }
            $pdfContent = $response->body();

            // Salva o PDF temporariamente
            $tempFile = tempnam(sys_get_temp_dir(), 'pdf_');
            file_put_contents($tempFile, $pdfContent);

            // Inicializa o Parser
            $parser = new Parser();

            // Tenta analisar o PDF
            //            if ($password !== null) {
            $pdf = $parser->parseFile($tempFile, $password);
            //            } else {
            //                $pdf = $parser->parseFile($tempFile);
            //            }

            // Extrai o texto de todas as páginas
            $text = $pdf->getText();

            // Remove o arquivo temporário
            unlink($tempFile);

            return $text;
        } catch (Exception $e) {
            // Se o arquivo temporário ainda existir, remova-o
            if (isset($tempFile) && file_exists($tempFile)) {
                unlink($tempFile);
            }

            // Trata diferentes tipos de exceções
            if ($e instanceof SecurityException) {
                return 'O PDF está protegido e a senha fornecida é incorreta ou não foi fornecida.';
            } else {
                return 'Erro ao processar o PDF: '.$e->getMessage();
            }
        }
    }
}
