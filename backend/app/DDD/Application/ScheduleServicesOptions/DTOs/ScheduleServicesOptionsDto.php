<?php

declare(strict_types=1);

namespace App\DDD\Application\ScheduleServicesOptions\DTOs;

use App\DDD\Application\ScheduleServicesOptions\Schema\ScheduleServicesOptionsSchema;
use App\DDD\Infrastructure\Shared\Abstracts\DTO\AbstractDto;
use App\DDD\Infrastructure\Shared\Interfaces\DTO\DtoInterface;

/**
 * DTO base para operações com documentos
 */
class ScheduleServicesOptionsDto extends AbstractDto implements DtoInterface
{
    public static function getSchemaClass(): string
    {
        return ScheduleServicesOptionsSchema::class;
    }

    protected static function getClass(array $data = []): self
    {
        return new self($data);
    }
}
