<?php

declare(strict_types=1);

namespace App\DDD\Application\ScheduleServicesOptions\Schema;

use App\DDD\Infrastructure\Shared\Abstracts\Entities\Schema\AbstractSchema;

/**
 * Schema para entidade de Opções de Serviços de Agendamento
 */
class ScheduleServicesOptionsSchema extends AbstractSchema
{
    /**
     * Obtém a classe concreta para reflexão
     */
    protected static function getConcreteClass(): string
    {
        return self::class;
    }

    /**
     * Nome da tabela no banco de dados
     */
    public const TABLE_NAME = 'schedulings_services_options';

    /**
     * Nome do campo de ID na tabela
     */
    public const ID_NAME = 'ScheduleServicesOptionsId';

    /**
     * Mensagens de erro para validação
     */
    public const MESSAGES_DATABASE = 'ScheduleServicesOptions## Dados inválidos: ';

    public const MESSAGES_SALESFORCE = 'ScheduleServicesOptions## Dados Salesforce inválidos: ';

    /**
     * Defini<PERSON> dos campos e suas regras de validação
     */
    public const FIELDS = [
        'ScheduleServicesOptionsId' => 'required|string',
        'TimeSlotNumber' => 'required|string',
        'DayOfWeek' => 'required|string|max:20',
        'StartTime' => 'required|string',
        'EndTime' => 'required|string',
        'EmpreendimentoId' => 'required|string',
    ];

    /**
     * Mapeamento dos campos do Salesforce para os campos do banco de dados
     */
    private const DEFAULT_SALESFORCE_FIELDS = [
        'Id',
        'TimeSlotNumber',
        'DayOfWeek',
        'StartTime',
        'EndTime',
        'EmpreendimentoId',
    ];

    /**
     * Mapeamento por tipo de objeto Salesforce
     */
    public const SALESFORCE_FIELDS = [
        'TimeSlot' => self::DEFAULT_SALESFORCE_FIELDS,
        'Proposta__c' => self::DEFAULT_SALESFORCE_FIELDS,
        'Empreendimento__c' => self::DEFAULT_SALESFORCE_FIELDS,
    ];

    /**
     * Campos obrigatórios por tipo para validação do Salesforce
     */
    public const REQUIRED_FIELDS_SALESFORCE = [
        'TimeSlot' => [
            'Id',
            'TimeSlotNumber',
            'DayOfWeek',
            'StartTime',
            'EndTime',
        ],
        'Proposta__c' => [
            'Empreendimento__r',
        ],
        'Empreendimento__c' => [
            'TerritorioServico__r.OperatingHoursId',
        ],
    ];

    /**
     * Campos obrigatórios por tipo para validação do banco de dados
     */
    public const REQUIRED_FIELDS_DATABASE = [
        'TimeSlot' => [
            'ScheduleServicesOptionsId',
            'TimeSlotNumber',
            'DayOfWeek',
            'StartTime',
            'EndTime',
        ],
        'Proposta__c' => [
            'ScheduleServicesOptionsId',
            'EmpreendimentoId',
        ],
        'Empreendimento__c' => [
            'ScheduleServicesOptionsId',
            'EmpreendimentoId',
        ],
    ];

    /**
     * Define quais campos usar para retry quando a validação falha
     * A chave é o campo no banco de dados, o valor é o campo no Salesforce
     */
    public const RETRY_SALESFORCE = [];

    /**
     * Define qual tipo de objeto consultar no retry
     * A chave é o tipo original, o valor é o tipo a ser consultado
     */
    public const RETRY_OBJECT_TYPES = [];

    public const RETRY_OBJECT_TYPES_RELATIONSHIP = [];

    /**
     * Definição dos relacionamentos com outras entidades
     */
    public const RELATIONSHIPS = [
        'realEstateProject' => [
            'related_model' => 'App\DDD\Domain\RealEstateProject\Entities\RealEstateProject',
            'foreign_key' => 'EmpreendimentoId',
            'local_key' => 'EmpreendimentoId',
        ],
    ];
}
