<?php

declare(strict_types=1);

namespace App\DDD\Application\ScheduleServicesOptions\Services\Dependencies;

use App\DDD\Domain\ScheduleServicesOptions\Interfaces\ScheduleServicesOptionsValidationInterface;
use App\DDD\Infrastructure\ScheduleServicesOptions\Interfaces\ScheduleServicesOptionsProcessorInterface;
use App\DDD\Infrastructure\ScheduleServicesOptions\Persistence\Interfaces\ScheduleServicesOptionsRepositoryInterface;

trait ScheduleServicesOptionsDependencies
{
    public function repository(): string
    {
        return ScheduleServicesOptionsRepositoryInterface::class;
    }

    public function validator(): string
    {
        return ScheduleServicesOptionsValidationInterface::class;
    }

    public function processor(): string
    {
        return ScheduleServicesOptionsProcessorInterface::class;
    }
}
