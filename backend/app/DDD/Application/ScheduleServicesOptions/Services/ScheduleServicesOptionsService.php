<?php

declare(strict_types=1);

namespace App\DDD\Application\ScheduleServicesOptions\Services;

use App\DDD\Application\ScheduleServicesOptions\Interfaces\ScheduleServicesOptionsServiceInterface;
use App\DDD\Application\ScheduleServicesOptions\Services\Dependencies\ScheduleServicesOptionsDependencies;
use App\DDD\Domain\ScheduleServicesOptions\Interfaces\ScheduleServicesOptionsValidationInterface;
use App\DDD\Infrastructure\ScheduleServicesOptions\Interfaces\ScheduleServicesOptionsProcessorInterface;
use App\DDD\Infrastructure\ScheduleServicesOptions\Persistence\Interfaces\ScheduleServicesOptionsRepositoryInterface;
use App\DDD\Infrastructure\ScheduleServicesOptions\Persistence\Queries\GetScheduleServicesOptionsQueries;
use App\DDD\Infrastructure\Shared\Abstracts\Services\AbstractService;

class ScheduleServicesOptionsService extends AbstractService implements ScheduleServicesOptionsServiceInterface
{
    use ScheduleServicesOptionsDependencies;

    public function __construct(
        private readonly ScheduleServicesOptionsRepositoryInterface $ScheduleServicesOptionsRepository,
        private readonly ScheduleServicesOptionsValidationInterface $ScheduleServicesOptionsValidator,
        private readonly ScheduleServicesOptionsProcessorInterface $processorService
    ) {
        parent::__construct(
            $ScheduleServicesOptionsRepository,
            $ScheduleServicesOptionsValidator,
            $processorService
        );
    }

    public function getScheduleServicesOptions(array $params): array|string|null
    {

        $paramsQuery = [
            'params' => ['OperatingHoursId' => $params['OperatingHoursId']],
            'queryType' => new GetScheduleServicesOptionsQueries(),
        ];
        $salesforceData = $this->salesforceService->queryObject('query', $paramsQuery);
        return data_get($salesforceData, 'records', []);
        // $result = $this->execute(['salesforceData' => $salesforceData['records']]);

        // $result = data_get($result, 'models', $result);
        // $result = data_get($result, 'model', $result);
        // return $result??[];
    }
}



