<?php

declare(strict_types=1);

namespace App\DDD\Application\RealEstateProject\DTOs;

use App\DDD\Application\RealEstateProject\Schema\RealEstateProjectSchema;
use App\DDD\Infrastructure\Shared\Abstracts\DTO\AbstractDto;
use App\DDD\Infrastructure\Shared\Interfaces\DTO\DtoInterface;

/**
 * DTO base para operações com empreendimentos
 */
class RealEstateProjectDto extends AbstractDto implements DtoInterface
{
    public static function getSchemaClass(): string
    {
        return RealEstateProjectSchema::class;
    }

    public static function getClass(array $data = []): self
    {
        return new self($data);
    }
}
