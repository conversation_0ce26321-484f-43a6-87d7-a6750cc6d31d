<?php

declare(strict_types=1);

namespace App\DDD\Application\RealEstateProject\Services\Dependencies;

use App\DDD\Domain\RealEstateProject\Interfaces\RealEstateProjectValidationInterface;
use App\DDD\Infrastructure\RealEstateProject\Interfaces\RealEstateProjectProcessorInterface;
use App\DDD\Infrastructure\RealEstateProject\Interfaces\RealEstateProjectRepositoryInterface;

trait RealEstateProjectDependencies
{
    public function repository(): string
    {
        return RealEstateProjectRepositoryInterface::class;
    }

    public function validator(): string
    {
        return RealEstateProjectValidationInterface::class;
    }

    public function processor(): string
    {
        return RealEstateProjectProcessorInterface::class;
    }
}
