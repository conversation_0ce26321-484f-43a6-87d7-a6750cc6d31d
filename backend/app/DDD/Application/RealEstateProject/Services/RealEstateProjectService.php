<?php

declare(strict_types=1);

namespace App\DDD\Application\RealEstateProject\Services;

use App\DDD\Application\RealEstateProject\Interfaces\RealEstateProjectServiceInterface;
use App\DDD\Application\RealEstateProject\Services\Dependencies\RealEstateProjectDependencies;
use App\DDD\Domain\RealEstateProject\Interfaces\RealEstateProjectValidationInterface;
use App\DDD\Infrastructure\RealEstateProject\Interfaces\RealEstateProjectProcessorInterface;
use App\DDD\Infrastructure\RealEstateProject\Interfaces\RealEstateProjectRepositoryInterface;
use App\DDD\Infrastructure\Shared\Abstracts\Services\AbstractService;

class RealEstateProjectService extends AbstractService implements RealEstateProjectServiceInterface
{
    use RealEstateProjectDependencies;

    public function __construct(
        private readonly RealEstateProjectRepositoryInterface $realEstateProjectRepository,
        private readonly RealEstateProjectValidationInterface $realEstateProjectValidation,
        private readonly RealEstateProjectProcessorInterface $processorService
    ) {
        parent::__construct(
            $realEstateProjectRepository,
            $realEstateProjectValidation,
            $processorService
        );
    }
}
