<?php

declare(strict_types=1);

namespace App\DDD\Application\RealEstateProject\Schema;

use App\DDD\Infrastructure\Shared\Abstracts\Entities\Schema\AbstractSchema;

/**
 * Schema para entidade de Empreendimento Imobiliário
 */
class RealEstateProjectSchema extends AbstractSchema
{
    /**
     * Obtém a classe concreta para reflexão
     */
    protected static function getConcreteClass(): string
    {
        return self::class;
    }

    /**
     * Nome da tabela no banco de dados
     */
    public const TABLE_NAME = 'empreendimentos';

    /**
     * Nome do campo de ID na tabela
     */
    public const ID_NAME = 'EmpreendimentoId';

    /**
     * Mensagens de erro para validação
     */
    public const MESSAGES_DATABASE = 'Empreendimento## Dados inválidos: ';

    public const MESSAGES_SALESFORCE = 'Empreendimento## Dados Salesforce inválidos: ';

    /**
     * Definição dos campos e suas regras de validação
     */
    public const FIELDS = [
        'EmpreendimentoId' => 'required|string',
        'CodigoSienge__c' => '',
        'Sigma__c' => 'required|boolean',
        'Manual__c' => 'required|boolean',
        'CodigoSiengeSPE' => '',
        'name' => 'required|string',
        'LogoEmpreendimento__c' => 'nullable|string',
        'StatusMacro__c' => 'nullable|string',
        'DataRealizadaHabitese__c' => 'nullable',
        'EstagioComercializacao__c' => 'nullable|string',
        'GOResponsavel__c' => 'nullable|string',
        'DataUltimaAtualizacao__c' => 'nullable',
        'Fundacao__c' => 'nullable|numeric',
        'Estrutura__c' => 'nullable|numeric',
        'Alvenaria__c' => 'nullable|numeric',
        'InstalacoesEletricas__c' => 'nullable|numeric',
        'InstalacoesHidraulicas__c' => 'nullable|numeric',
        'AcabamentoInterno__c' => 'nullable|numeric',
        'AcabamentoExterno__c' => 'nullable|numeric',
        'ServicosComplementares__c' => 'nullable|numeric',
        'Pintura__c' => 'nullable|numeric',
        'MobilizacaoCanteiro__c' => 'nullable|numeric',
        'PorcentagemFisicoAcumulado__c' => 'nullable|numeric',
        'Sindico__c' => 'nullable|string',
        'DataAGIRealizada__c' => 'nullable',
        'UltimaAtualizacaoVideoDrone__c' => 'nullable',
        'DataUltimaAtualizacaoMidia__c' => 'nullable',
        'DataRealMatriculaIndividualizada__c' => 'nullable',
        'OperatingHoursId' => 'nullable|string',
        'TerritorioServico' => 'nullable|string',
        'TerritorioServicoIsActive' => 'nullable|boolean',
        'imgsCarrossel' => 'nullable|json',
        'Fotos__c' => 'nullable|json',
        'Videos__c' => 'nullable|json',
        'documents' => 'nullable',
        'DataEntregaContratualCury__c' => 'nullable',
        'Video_Tour__c' => 'nullable|json',
        'Last_Data_Tour__c' => 'nullable',
        'Empreendimento_novo__c' => 'nullable',
        'CreatedDate' => 'nullable',
        'LastModifiedDate' => 'nullable',
        'created_at' => 'nullable',
        'updated_at' => 'nullable',
    ];

    /**
     * Campos que devem ser cast para array
     */
    public const CAST = [
        'imgsCarrossel',
        'Fotos__c',
        'Video_Tour__c',
        'documents',
    ];

    private const DEFAULT_SALESFORCE_FIELDS = [
        'Id',
        'CodigoSienge__c',
        'Sigma__c',
        'Manual__c',
        'SPE__r.CodigoSiengeSPE__c',
        'Name',
        'LogoEmpreendimento__c',
        'StatusMacro__c',
        'DataRealizadaHabitese__c',
        'EstagioComercializacao__c',
        'GOResponsavel__c',
        'DataUltimaAtualizacao__c',
        'Fundacao__c',
        'Estrutura__c',
        'Alvenaria__c',
        'InstalacoesEletricas__c',
        'InstalacoesHidraulicas__c',
        'AcabamentoInterno__c',
        'AcabamentoExterno__c',
        'ServicosComplementares__c',
        'Pintura__c',
        'MobilizacaoCanteiro__c',
        'PorcentagemFisicoAcumulado__c',
        'Sindico__c',
        'DataAGIRealizada__c',
        'UltimaAtualizacaoVideoDrone__c',
        'DataUltimaAtualizacaoMidia__c',
        'DataRealMatriculaIndividualizada__c',
        'TerritorioServico__r.OperatingHoursId',
        'TerritorioServico__c',
        'TerritorioServico__r.IsActive',
        'imgsCarrossel',
        'Fotos__c',
        'Videos__c',
        'documents',
        'DataEntregaContratualCury__c',
        'Video_Tour__c',
        'Last_Data_Tour__c',
        'Empreendimento_novo__c',
        'CreatedDate',
        'LastModifiedDate',
        'created_at',
        'updated_at',
    ];

    /**
     * Mapeamento por tipo de objeto Salesforce
     */
    public const SALESFORCE_FIELDS = [
        'Empreendimento__c' => self::DEFAULT_SALESFORCE_FIELDS,
        'Proposta__c' => ['Empreendimento__r' => self::DEFAULT_SALESFORCE_FIELDS],
    ];

    /**
     * Campos obrigatórios por tipo para validação do Salesforce
     */
    public const REQUIRED_FIELDS_SALESFORCE = [
        'Proposta__c' => [
            'Empreendimento__c',
            'Empreendimento__r.Name',
        ],
        'Empreendimento__c' => [
            'Id',
            'Name',
        ],
    ];

    /**
     * Campos obrigatórios por tipo para validação do banco de dados
     */
    public const REQUIRED_FIELDS_DATABASE = [
        'Proposta__c' => [
            'EmpreendimentoId',
            'name',
        ],
        'Empreendimento__c' => [
            'EmpreendimentoId',
            'name',
        ],
    ];

    /**
     * Define quais campos usar para retry quando a validação falha
     * A chave é o campo no banco de dados, o valor é o campo no Salesforce
     */
    public const RETRY_SALESFORCE = [];

    /**
     * Define qual tipo de objeto consultar no retry
     * A chave é o tipo original, o valor é o tipo a ser consultado
     */
    public const RETRY_OBJECT_TYPES = [];

    /**
     * Definição dos relacionamentos com outras entidades
     */
    public const RELATIONSHIPS = [
        'sindico' => [
            'related_model' => 'App\DDD\Domain\User\Entities\User',
            'primary' => [
                'foreign_key' => 'Sindico__c',
                'local_key' => 'AccountId',
            ],
        ],
        //// VER NO MODEL App\DDD\Domain\RealEstateProject\Entities\RealEstateProject.php; DO ENTITIES - Regra de negócio de DOCUMENTOS PARA EMPREENDIMENTO é diferente do padrão

        // 'documents' => [

            // 'related_model' => 'App\DDD\Domain\Document\Entities\Document',
            // 'primary' => [
            //     'foreign_key' => 'EmpreendimentoId',
            //     'local_key' => 'EmpreendimentoId',
            // ],
            // 'type' => 'hasMany',

        // ],

        'assets' => [
            'related_model' => 'App\DDD\Domain\Asset\Entities\Asset',
            'primary' => [
                'foreign_key' => 'EmpreendimentoId',
                'local_key' => 'EmpreendimentoId',
            ],
        ],
        'proposal' => [
            'related_model' => 'App\DDD\Domain\Proposal\Entities\Proposal',
            'primary' => [
                'foreign_key' => 'EmpreendimentoId',
                'local_key' => 'EmpreendimentoId',
            ],
        ],
        'schedule' => [
            'related_model' => 'App\DDD\Domain\Schedule\Entities\Schedule',
            'primary' => [
                'foreign_key' => 'EmpreendimentoId',
                'local_key' => 'EmpreendimentoId',
            ],
        ],
        'scheduleServicesOptions' => [
            'related_model' => 'App\DDD\Domain\ScheduleServicesOptions\Entities\ScheduleServicesOptions',
            'primary' => [
                'foreign_key' => 'EmpreendimentoId',
                'local_key' => 'EmpreendimentoId',
            ],
        ],
    ];
}
