<?php

declare(strict_types=1);

namespace App\DDD\Application\Proposal\DTOs;

use App\DDD\Application\Proposal\Schema\ProposalSchema;
use App\DDD\Infrastructure\Shared\Abstracts\DTO\AbstractDto;
use App\DDD\Infrastructure\Shared\Interfaces\DTO\DtoInterface;

/**
 * DTO base para operações com documentos
 */
class ProposalDto extends AbstractDto implements DtoInterface
{
    public static function getSchemaClass(): string
    {
        return ProposalSchema::class;
    }

    protected static function getClass(array $data = []): self
    {
        return new self($data);
    }
}
