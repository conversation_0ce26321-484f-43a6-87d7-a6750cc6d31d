<?php

declare(strict_types=1);

namespace App\DDD\Application\Proposal\Services;

use App\DDD\Application\Proposal\Interfaces\ProposalServiceInterface;
use App\DDD\Application\Proposal\Services\Dependencies\ProposalDependencies;
use App\DDD\Domain\Proposal\Interfaces\ProposalValidationInterface;
use App\DDD\Infrastructure\Proposal\Persistence\Interfaces\ProposalRepositoryInterface;
use App\DDD\Infrastructure\Proposal\Processors\WebhookProposalProcessor;
use App\DDD\Infrastructure\Shared\Abstracts\DTO\AbstractDto;
use App\DDD\Infrastructure\Shared\Abstracts\Services\AbstractService;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Collection;

class WebhookProposalService extends AbstractService implements ProposalServiceInterface
{
    use ProposalDependencies;

    public function __construct(
        private readonly ProposalRepositoryInterface $proposalRepository,
        private readonly ProposalValidationInterface $proposalValidator,
        private readonly WebhookProposalProcessor $processorService
    ) {
        parent::__construct(
            $proposalRepository,
            $proposalValidator,
            $processorService
        );
    }

    /**
     * Transform data using the transform component.
     */
    public function processWebhookData(array $data, AbstractService $service): Model|Collection|array|null|AbstractDto
    {
        $data['proposta']['attributes']['type'] = 'proposta';
        return $this->processorService->process($data['proposta']);

    }
}
