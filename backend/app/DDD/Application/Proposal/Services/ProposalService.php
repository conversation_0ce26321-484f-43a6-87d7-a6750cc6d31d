<?php

declare(strict_types=1);

namespace App\DDD\Application\Proposal\Services;

use App\DDD\Application\Proposal\Interfaces\ProposalServiceInterface;
use App\DDD\Application\Proposal\Services\Dependencies\ProposalDependencies;
use App\DDD\Domain\Proposal\Interfaces\ProposalValidationInterface;
use App\DDD\Infrastructure\Proposal\Interfaces\ProposalProcessorInterface;
use App\DDD\Infrastructure\Proposal\Persistence\Interfaces\ProposalRepositoryInterface;
use App\DDD\Infrastructure\Shared\Abstracts\Services\AbstractService;

class ProposalService extends AbstractService implements ProposalServiceInterface
{
    use ProposalDependencies;

    public function __construct(
        private readonly ProposalRepositoryInterface $proposalRepository,
        private readonly ProposalValidationInterface $proposalValidator,
        private readonly ProposalProcessorInterface $processorService
    ) {
        parent::__construct(
            $proposalRepository,
            $proposalValidator,
            $processorService
        );
    }
}
