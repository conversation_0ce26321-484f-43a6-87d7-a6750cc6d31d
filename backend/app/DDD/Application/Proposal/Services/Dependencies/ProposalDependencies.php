<?php

declare(strict_types=1);

namespace App\DDD\Application\Proposal\Services\Dependencies;

use App\DDD\Domain\Proposal\Interfaces\ProposalValidationInterface;
use App\DDD\Infrastructure\Proposal\Interfaces\ProposalProcessorInterface;
use App\DDD\Infrastructure\Proposal\Persistence\Interfaces\ProposalRepositoryInterface;

trait ProposalDependencies
{
    public function repository(): string
    {
        return ProposalRepositoryInterface::class;
    }

    public function validator(): string
    {
        return ProposalValidationInterface::class;
    }

    public function processor(): string
    {
        return ProposalProcessorInterface::class;
    }
}
