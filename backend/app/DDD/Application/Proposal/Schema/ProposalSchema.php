<?php

declare(strict_types=1);

namespace App\DDD\Application\Proposal\Schema;

use App\DDD\Infrastructure\Shared\Abstracts\Entities\Schema\AbstractSchema;

/**
 * Schema para entidade de Proposta
 */
class ProposalSchema extends AbstractSchema
{
    /**
     * Obtém a classe concreta para reflexão
     */
    protected static function getConcreteClass(): string
    {
        return self::class;
    }

    /**
     * Nome da tabela no banco de dados
     */
    public const TABLE_NAME = 'proposals';

    /**
     * Nome do campo de ID na tabela
     */
    public const ID_NAME = 'ProposalId';

    /**
     * Mensagens de erro para validação
     */
    public const MESSAGES_DATABASE = 'Proposal## Dados inválidos: ';

    public const MESSAGES_SALESFORCE = 'Proposal## Dados Salesforce inválidos: ';

    /**
     * Definição dos campos e suas regras de validação
     */
    public const FIELDS = [
        'ProposalId' => 'required|string',
        'AccountId' => 'required|string',
        'AssetId' => 'required|string',
        'ContractId' => 'nullable|string',
        'EmpreendimentoId' => 'required|string',
        'ContractNumber' => 'nullable|string',
        'name' => 'required|string|max:255',
        'PlantaId' => 'nullable|string',
        'UnidadeAtivoName' => 'required|string|max:255',
        'StatusAssinatura__c' => 'required|string|max:100',
        'StatusFinanciamento__c' => 'nullable|string|max:100',
        'NomeCCA__c' => 'nullable|string|max:255',
        'TelefoneCelularCCA__c' => 'nullable|string|max:20',
        'RepasseId' => 'nullable|string',
        'Etapa__c' => 'nullable|string|max:100',
        'CodigoSienge__c' => '',
        'planta' => 'nullable|array',
        'documents' => 'nullable|array',
        'CreatedDate' => 'nullable|date',
        'LastModifiedDate' => 'nullable|date',
        'created_at' => 'nullable|date',
        'updated_at' => 'nullable|date',
    ];

    /**
     * Campos que devem ser cast para array
     */
    // public const CAST = [
    //     // 'planta',
    //     'documents',
    // ];

    /**
     * Mapeamento dos campos do Salesforce para os campos do banco de dados
     */
    private const DEFAULT_SALESFORCE_FIELDS = [
        'Id',
        'Conta__c',
        'Unidade__r.Ativo__r.Id',
        'Unidade__r.Ativo__r.ContratoVigente__c',
        'Empreendimento__c',
        'Unidade__r.Ativo__r.ContratoVigente__r.ContractNumber',
        'Name',
        'Planta__c',
        'Unidade__r.Ativo__r.Name',
        'StatusAssinatura__c',
        'StatusFinanciamento__c',
        'Repasse__r.NomeCCA__c',
        'Repasse__r.AcessoriaCreditoCCA__r.TelefoneCelular__c',
        'Repasse__r.Id',
        'Repasse__r.Etapa__c',
        'Conta__r.CodigoSienge__c',
        'planta',
        'documents',
        'CreatedDate',
        'LastModifiedDate',
        'CreatedDate',
        'LastModifiedDate',
    ];

    private const DEFAULT_WEBHOOK_FIELDS = [
        'ProposalId',
        'user.Id',
        'Unidade__r.Ativo__r.Id',
        'Unidade__r.Ativo__r.ContratoVigente__c',
        'empreendimento.0.Id',
        'Unidade__r.Ativo__r.ContratoVigente__r.ContractNumber',
        'Name',
        'Planta__c',
        'Unidade__r.Ativo__r.Name',
        'StatusAssinatura__c',
        'StatusFinanciamento__c',
        'Repasse__r.NomeCCA__c',
        'Repasse__r.AcessoriaCreditoCCA__r.TelefoneCelular__c',
        'Repasse__r.Id',
        'Repasse__r.Etapa__c',
        'Conta__r.CodigoSienge__c',
        'planta',
        'documents',
        'CreatedDate',
        'LastModifiedDate',
        'CreatedDate',
        'LastModifiedDate',
    ];
    /**
     * Mapeamento por tipo de objeto Salesforce
     */
    public const SALESFORCE_FIELDS = [
        'Proposta__c' => self::DEFAULT_SALESFORCE_FIELDS,
        'proposta' => self::DEFAULT_WEBHOOK_FIELDS,
    ];

    /**
     * Campos obrigatórios por tipo para validação do Salesforce
     */
    public const REQUIRED_FIELDS_SALESFORCE = [
        'Proposta__c' => [
            'Id',
            'Conta__c',
            'Unidade__c',
            'Empreendimento__c',
            'Name',
            'StatusAssinatura__c',
            'Unidade__c',
            'Unidade__r.Ativo__r.Id',
            'Unidade__r.Ativo__r.Name',
        ],
    ];

    /**
     * Campos obrigatórios por tipo para validação do banco de dados
     */
    public const REQUIRED_FIELDS_DATABASE = [
        'Proposta__c' => [
            'ProposalId',
            'AccountId',
            'AssetId',
            'EmpreendimentoId',
            'name',
            'UnidadeAtivoName',
            'StatusAssinatura__c',
        ],
        'proposta' => [
            'ProposalId',
            'user.Id',
            'Unidade__r.Ativo__r.Id',
            'Unidade__r.Ativo__r.ContratoVigente__c',
            'empreendimento.0.Id',
        ],
    ];

    /**
     * Define quais campos usar para retry quando a validação falha
     * A chave é o campo no banco de dados, o valor é o campo no Salesforce
     */
    public const RETRY_SALESFORCE = [
        'Proposta__c' => [
            'AccountId' => 'Conta__c',
        ],
        'proposta' => [
            'AccountId' => 'user.Id',
        ],
    ];

    /**
     * Define qual tipo de objeto consultar no retry
     * A chave é o tipo original, o valor é o tipo a ser consultado
     */
    public const RETRY_OBJECT_TYPES = [
        'Proposta__c' => 'App\\DDD\\Infrastructure\\Asset\\Persistence\\Queries\\GetAssetRetryByProposalQueries',
        'proposta' => 'App\\DDD\\Infrastructure\\Asset\\Persistence\\Queries\\GetAssetRetryByProposalQueries',
    ];

    public const RETRY_OBJECT_TYPES_RELATIONSHIP = [
        'Asset' => [
            'Unidade__c' => 'Id',
            'Unidade__r.Ativo__r.Id' => 'Id',
            'Unidade__r.Ativo__r.ContratoVigente__c' => 'ContratoVigente__c',
            'Unidade__r.Ativo__r.Name' => 'Name',
            'Unidade__r.Ativo__r.ContratoVigente__r.ContractNumber' => 'ContratoVigente__r.ContractNumber',
        ],
    ];

    /**
     * Definição dos relacionamentos com outras entidades
     */
    public const RELATIONSHIPS = [
        'user' => [
            'related_model' => 'App\\DDD\\Domain\\User\\Entities\\User',
            'primary' => [
                'foreign_key' => 'AccountId',
                'local_key' => 'AccountId',
            ],
        ],
        'documents' => [
            'related_model' => 'App\\DDD\\Domain\\Document\\Entities\\Document',
            'primary' => [
                'foreign_key' => 'ProposalId',
                'local_key' => 'ProposalId',
            ],
            'type' => 'hasMany',
        ],
        'realEstateProject' => [
            'related_model' => 'App\\DDD\\Domain\\RealEstateProject\\Entities\\RealEstateProject',
            'primary' => [
                'foreign_key' => 'EmpreendimentoId',
                'local_key' => 'EmpreendimentoId',
            ],
        ],
        'contracts' => [
            'related_model' => 'App\\DDD\\Domain\\Contract\\Entities\\Contract',
            'primary' => [
                'foreign_key' => 'ContractId',
                'local_key' => 'ContractId',
            ],

        ],
        'assets' => [
            'related_model' => 'App\\DDD\\Domain\\Asset\\Entities\\Asset',
            'primary' => [
                'foreign_key' => 'AssetId',
                'local_key' => 'AssetId',
            ],
            'conditions' => [
                [
                    'foreign_key' => 'AccountId',
                    'local_key' => 'AccountId',
                ],
            ],
            'with' => [
                'proposal',
                'realEstateProject',
                'contract',
            ],
        ],
    ];
}
