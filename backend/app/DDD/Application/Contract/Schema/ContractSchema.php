<?php

declare(strict_types=1);

namespace App\DDD\Application\Contract\Schema;

use App\DDD\Infrastructure\Shared\Abstracts\Entities\Schema\AbstractSchema;

/**
 * Schema para entidade de Contrato
 */
class ContractSchema extends AbstractSchema
{
    /**
     * Obtém a classe concreta para reflexão
     */
    protected static function getConcreteClass(): string
    {
        return self::class;
    }

    /**
     * Nome da tabela no banco de dados
     */
    public const TABLE_NAME = 'contracts';

    /**
     * Nome do campo de ID na tabela
     */
    public const ID_NAME = 'ContractId';

    /**
     * Mensagens de erro para validação
     */
    public const MESSAGES_DATABASE = 'Contract## Dados inválidos: ';

    public const MESSAGES_SALESFORCE = 'Contract## Dados Salesforce inválidos: ';

    /**
     * Definição dos campos e suas regras de validação
     */
    public const FIELDS = [
        'ContractId' => 'required|string',
        'AccountId' => 'required|string',
        'ProposalId' => 'nullable|string',
        'EmpreendimentoId' => 'required|string',
        'AssetId' => 'nullable|string',
        'CampanhaId' => 'nullable|string',
        'ContractNumber' => 'required|string|max:100',
        'StatusCarteira__c' => 'nullable|string|max:100',
        'SituacaoEntrega__c' => 'nullable|string|max:100',
        'Regional__c' => 'nullable|string|max:100',
        'Status' => 'nullable|string|max:100',
        'ContratoComProgramaFidelidade__c' => 'nullable|boolean',
        'DataAdesaoProgramaFidelidade__c' => 'nullable|date',
        'DataValidacaoProgramaFidelidade__c' => 'nullable|date',
        'DataChaves__c' => 'nullable|date',
        'DataCompra__c' => 'required|date',
        'documents' => 'nullable|json',
        'CreatedDate' => 'nullable|date',
        'LastModifiedDate' => 'nullable|date',
        'created_at' => 'nullable|date',
        'updated_at' => 'nullable|date',
    ];

    /**
     * Mapeamento dos campos do Salesforce para os campos do banco de dados
     */
    private const DEFAULT_SALESFORCE_FIELDS = [
        'Id',
        'AccountId',
        'Proposta__c',
        'EmpreendimentoAtivo__c',
        'Unidade__c',
        'Campanha__c',
        'ContractNumber',
        'StatusCarteira__c',
        'SituacaoEntrega__c',
        'Regional__c',
        'Status',
        'ContratoComProgramaFidelidade__c',
        'DataAdesaoProgramaFidelidade__c',
        'DataValidacaoProgramaFidelidade__c',
        'DataChaves__c',
        'DataCompra__c',
        'documents',
        'CreatedDate',
        'LastModifiedDate',
        'created_at',
        'updated_at',
    ];

    /**
     * Mapeamento por tipo de objeto Salesforce
     */
    public const SALESFORCE_FIELDS = [
        'Contract' => self::DEFAULT_SALESFORCE_FIELDS,
        'Proposta__c' => [
            'Unidade__r.Ativo__r.ContratoVigente__c',
            'Conta__c',
            'Id',
            'Empreendimento__c',
            'Unidade__r.Ativo__r.Id',
            'Unidade__r.Ativo__r.ContratoVigente__r.Campanha__c',
            'Unidade__r.Ativo__r.ContratoVigente__r.ContractNumber',
            'Unidade__r.Ativo__r.ContratoVigente__r.StatusCarteira__c',
            'Unidade__r.Ativo__r.ContratoVigente__r.SituacaoEntrega__c',
            'Unidade__r.Ativo__r.ContratoVigente__r.Regional__c',
            'Unidade__r.Ativo__r.ContratoVigente__r.Status',
            'Unidade__r.Ativo__r.ContratoVigente__r.ContratoComProgramaFidelidade__c',
            'Unidade__r.Ativo__r.ContratoVigente__r.DataAdesaoProgramaFidelidade__c',
            'Unidade__r.Ativo__r.ContratoVigente__r.DataValidacaoProgramaFidelidade__c',
            'Unidade__r.Ativo__r.ContratoVigente__r.DataChaves__c',
            'Unidade__r.Ativo__r.ContratoVigente__r.DataCompra__c',
            '',
            'Unidade__r.Ativo__r.ContratoVigente__r.CreatedDate',
            'Unidade__r.Ativo__r.ContratoVigente__r.LastModifiedDate',
            'created_at' => '',
            'updated_at' => '',
        ],
    ];

    /**
     * Campos obrigatórios por tipo para validação do Salesforce
     */
    public const REQUIRED_FIELDS_SALESFORCE = [
        'Proposta__c' => [
            'Unidade__r.Ativo__r.ContratoVigente__c',
        ],
        'Contract' => [
            'Id',
            'AccountId',
            'ContractNumber',
            'Unidade__r.RootAsset.Id',
            'DataCompra__c',
        ],
    ];

    /**
     * Campos obrigatórios por tipo para validação do banco de dados
     */
    public const REQUIRED_FIELDS_DATABASE = [
        'ContractId',
        'AccountId',
        'EmpreendimentoId',
        'AssetId',
        'DataCompra__c',
    ];

    /**
     * Define quais campos usar para retry quando a validação falha
     * A chave é o campo no banco de dados, o valor é o campo no Salesforce
     */
    public const RETRY_SALESFORCE = [
        'Proposta__c' => [
            'Unidade__r.Ativo__r.Id',
            'Conta__c',
        ],
        'Contract' => [
            'AssetId' => 'Unidade__c',
            'AccountId' => 'AccountId',
        ],
    ];

    /**
     * Define qual tipo de objeto consultar no retry
     * A chave é o tipo original, o valor é o tipo a ser consultado
     */
    public const RETRY_OBJECT_TYPES = [
        'Proposta__c' => 'Contract',
        'Contract' => 'Contract',
    ];

    /**
     * Definição dos relacionamentos com outras entidades
     */
    public const RELATIONSHIPS = [
        'user' => [
            'related_model' => 'App\DDD\Domain\User\Entities\User',
            'primary' => [
                'foreign_key' => 'AccountId',
                'local_key' => 'AccountId',
            ],
        ],
        'realEstateProject' => [
            'related_model' => 'App\DDD\Domain\RealEstateProject\Entities\RealEstateProject',
            'primary' => [
                'foreign_key' => 'EmpreendimentoId',
                'local_key' => 'EmpreendimentoId',
            ],
        ],
        'documents' => [
            'related_model' => 'App\DDD\Domain\Document\Entities\Document',
            'primary' => [
                'foreign_key' => 'ContractId',
                'local_key' => 'ContractId',
            ],
            'type' => 'hasMany',
        ],
        'proposals' => [
            'related_model' => 'App\DDD\Domain\Proposal\Entities\Proposal',
            'primary' => [
                'foreign_key' => 'ProposalId',
                'local_key' => 'ProposalId',
            ],
            'conditions' => [
                [
                    'foreign_key' => 'AccountId',
                    'local_key' => 'AccountId',
                ],
            ],
        ],
        'assets' => [
            'related_model' => 'App\DDD\Domain\Asset\Entities\Asset',
            'primary' => [
                'foreign_key' => 'AssetId',
                'local_key' => 'AssetId',
            ],
            'conditions' => [
                [
                    'foreign_key' => 'ContractId',
                    'local_key' => 'ContractId',
                ],
            ],
        ],
    ];
}
