<?php

declare(strict_types=1);

namespace App\DDD\Application\Contract\Services;

use App\DDD\Application\Contract\Interfaces\ContractServiceInterface;
use App\DDD\Application\Contract\Services\Dependecies\ContractDependencies;
use App\DDD\Domain\Contract\Interfaces\ContractValidationInterface;
use App\DDD\Infrastructure\Contract\Interfaces\ContractProcessorInterface;
use App\DDD\Infrastructure\Contract\Interfaces\ContractRepositoryInterface;
use App\DDD\Infrastructure\Shared\Abstracts\Services\AbstractService;

class ContractService extends AbstractService implements ContractServiceInterface
{
    use ContractDependencies;

    public function __construct(
        private readonly ContractRepositoryInterface $contractRepository,
        private readonly ContractValidationInterface $contractValidation,
        private readonly ContractProcessorInterface $processorService
    ) {
        parent::__construct(
            $contractRepository,
            $contractValidation,
            $processorService
        );
    }
}
