<?php

declare(strict_types=1);

namespace App\DDD\Application\Contract\Services\Dependecies;

use App\DDD\Domain\Contract\Interfaces\ContractValidationInterface;
use App\DDD\Infrastructure\Contract\Interfaces\ContractProcessorInterface;
use App\DDD\Infrastructure\Contract\Interfaces\ContractRepositoryInterface;

trait ContractDependencies
{
    public function repository(): string
    {
        return ContractRepositoryInterface::class;
    }

    public function validator(): string
    {
        return ContractValidationInterface::class;
    }

    public function processor(): string
    {
        return ContractProcessorInterface::class;
    }
}
