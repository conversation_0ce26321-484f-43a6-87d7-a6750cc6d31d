<?php

declare(strict_types=1);

namespace App\DDD\Application\Contract\DTOs;

use App\DDD\Application\Contract\Schema\ContractSchema;
use App\DDD\Infrastructure\Shared\Abstracts\DTO\AbstractDto;

/**
 * DTO base para operações com documentos
 */
class ContractDto extends AbstractDto
{
    public static function getSchemaClass(): string
    {
        return ContractSchema::class;
    }

    protected static function getClass(array $data = []): self
    {
        return new self($data);
    }
}
