<?php

declare(strict_types=1);

namespace App\DDD\Application\Asset\Services;

use App\DDD\Application\Asset\Interfaces\AssetServiceInterface;
use App\DDD\Application\Asset\Services\Dependencies\AssetDependencies;
use App\DDD\Domain\Asset\Interfaces\AssetValidationInterface;
use App\DDD\Infrastructure\Asset\Interfaces\AssetProcessInterface;
use App\DDD\Infrastructure\Asset\Interfaces\AssetRepositoryInterface;
use App\DDD\Infrastructure\Shared\Abstracts\Services\AbstractService;

class AssetService extends AbstractService implements AssetServiceInterface
{
    use AssetDependencies;

    public function __construct(
        private readonly AssetRepositoryInterface $assetRepository,
        private readonly AssetValidationInterface $assetValidator,
        private readonly AssetProcessInterface $processorService
    ) {
        parent::__construct(
            $assetRepository,
            $assetValidator,
            $processorService
        );
    }
}
