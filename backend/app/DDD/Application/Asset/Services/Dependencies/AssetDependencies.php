<?php

declare(strict_types=1);

namespace App\DDD\Application\Asset\Services\Dependencies;

use App\DDD\Domain\Asset\Interfaces\AssetValidationInterface;
use App\DDD\Infrastructure\Asset\Interfaces\AssetProcessInterface;
use App\DDD\Infrastructure\Asset\Interfaces\AssetRepositoryInterface;

trait AssetDependencies
{
    public function repository(): string
    {
        return AssetRepositoryInterface::class;
    }

    public function validator(): string
    {
        return AssetValidationInterface::class;
    }

    public function processor(): string
    {
        return AssetProcessInterface::class;
    }
}
