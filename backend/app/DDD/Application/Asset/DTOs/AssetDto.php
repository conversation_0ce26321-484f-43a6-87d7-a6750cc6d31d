<?php

declare(strict_types=1);

namespace App\DDD\Application\Asset\DTOs;

use App\DDD\Application\Asset\Schema\AssetSchema;
use App\DDD\Infrastructure\Shared\Abstracts\DTO\AbstractDto;

/**
 * DTO base para operações com documentos
 */
class AssetDto extends AbstractDto
{
    public static function getSchemaClass(): string
    {
        return AssetSchema::class;
    }

    protected static function getClass(array $data = []): self
    {
        return new self($data);
    }
}
