<?php

declare(strict_types=1);

namespace App\DDD\Application\Asset\Assemblers;


use App\DDD\Application\Asset\DTOs\AssetDto;
use Illuminate\Support\Facades\Log;

class AuthAssetAssembler
{
    /**
     * Transforma dados brutos de asset em AssetDto
     */
    public function toDto(array $asset): AssetDto
    {
        $proposalId = data_get($asset, 'ProposalId') ?? data_get($asset, 'proposal.ProposalId');
        $contractId = data_get($asset, 'ContractId') ?? data_get($asset, 'contracts.ContractId');

        $baseData = [
            'AssetId' => data_get($asset, 'AssetId'),
            'UnidadeAtivoName' => data_get($asset, 'Name'),
            'ProposalId' => $proposalId,
            'ContractId' => $contractId,
            'type' => 'morador',
        ];
        // data_getProposal = $asset['proposal'];
        // if (isset($assetProposal)) {
        //     $baseData += [
        //         'ProposalId' => $assetProposal['ProposalId'],
        //     ];
        // } else {
        //     $baseData += [
        //         'ProposalId' => null,
        //     ];
        // }
        // if ($asset['contracts']) {
        //     $baseData['ContractId'] = $asset['contracts']['ContractId'];
        // }

        // if ($asset['real_estate_project']) {
        //     $baseData['Emprendimento'] =
        //         [
        //             'EmpreendimentoId' => $asset['real_estate_project']['EmpreendimentoId'],
        //             'name' => $asset['real_estate_project']['name'],
        //             'Sindico__c' => $asset['real_estate_project']['Sindico__c'],
        //             'CodigoSienge__c' => $asset['real_estate_project']['CodigoSienge__c'],
        //         ];
        // }

        return new AssetDto($baseData);
    }
}