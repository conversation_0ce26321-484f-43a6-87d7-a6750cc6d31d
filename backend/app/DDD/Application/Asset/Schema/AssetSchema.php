<?php

declare(strict_types=1);

namespace App\DDD\Application\Asset\Schema;

use App\DDD\Infrastructure\Shared\Abstracts\Entities\Schema\AbstractSchema;

class AssetSchema extends AbstractSchema
{
    protected static function getConcreteClass(): string
    {
        return self::class;
    }

    public const TABLE_NAME = 'assets';

    public const ID_NAME = 'AssetId';

    public const MESSAGES_DATABASE = 'Asset## Dados inválidos: ';

    public const MESSAGES_SALESFORCE = 'Asset## Dados Salesforce inválidos: ';

    public const FIELDS = [
        'AssetId' => '',
        'AccountId' => '',
        'EmpreendimentoId' => '',
        'ContractId' => '',
        'ProposalId' => '',
        'statusBoletoAto' => '',
        'Name' => '',
        'DataCompra__c' => '',
        'CreatedDate' => 'nullable|date',
        'LastModifiedDate' => 'nullable|date',
        'created_at' => 'nullable|date',
        'updated_at' => 'nullable|date',
    ];

    public const REQUIRED_FIELDS_SALESFORCE = [
        'Proposta__c' => [
            'Empreendimento__c',
            // 'Unidade__r.Ativo__r.Id',
            // 'Unidade__r.Ativo__r.Name'
        ],
    ];

    public const REQUIRED_FIELDS_DATABASE = [
        'Proposta__c' => [
            'AssetId',
            'EmpreendimentoId',
            'Name',
        ],
    ];

    private const DEFAULT_SALESFORCE_FIELDS = [
        'Id',
        'AccountId',
        'Empreendimento__c',
        'ContratoVigente__c',
        'Proposta__c',
        'StatusBoletoAto',
        'Name',
        'DataCompra__c',
        'CreatedDate',
        'LastModifiedDate',
        'created_at',
        'updated_at',
    ];

    /**
     * Mapeamento por tipo de objeto Salesforce
     */
    public const SALESFORCE_FIELDS = [
        'Asset' => self::DEFAULT_SALESFORCE_FIELDS,
        'Proposta__c' => [
            'Unidade__r.Ativo__r.Id',
            'Conta__c',
            'Empreendimento__c',
            'Unidade__r.Ativo__r.ContratoVigente__c',
            'Proposta__c',
            'statusBoletoAto',
            'Unidade__r.Ativo__r.Name',
            'Unidade__r.Ativo__r.ContratoVigente__r.DataCompra__c',
            'Unidade__r.Ativo__r.ContratoVigente__r.CreatedDate',
            'Unidade__r.Ativo__r.ContratoVigente__r.LastModifiedDate',
            '',
            '',
        ],
    ];

    /**
     * Define quais campos usar para retry quando a validação falha
     * A chave é o campo no banco de dados, o valor é o campo no Salesforce
     */
    public const RETRY_SALESFORCE = [];

    /**
     * Define qual tipo de objeto consultar no retry
     * A chave é o tipo original, o valor é o tipo a ser consultado
     */
    public const RETRY_OBJECT_TYPES = [];

    public const RELATIONSHIPS = [
        'user' => [
            'related_model' => 'App\\DDD\\Domain\\User\\Entities\\User',
            'primary' => [
                'foreign_key' => 'AccountId',
                'local_key' => 'AccountId',
            ],
            'with' => [
                'sindico',
                'proposal',
                'realEstateProject',
                'contract',
            ],
        ],
        'realEstateProject' => [
            'related_model' => 'App\\DDD\\Domain\\RealEstateProject\\Entities\\RealEstateProject',
            'primary' => [
                'foreign_key' => 'EmpreendimentoId',
                'local_key' => 'EmpreendimentoId',
            ],
        ],
        'contract' => [
            'related_model' => 'App\\DDD\\Domain\\Contract\\Entities\\Contract',
            'primary' => [
                'foreign_key' => 'ContractId',
                'local_key' => 'ContractId',
            ],
            'conditions' => [
                [
                    'foreign_key' => 'AccountId',
                    'local_key' => 'AccountId',
                ],
            ],
        ],
        'proposal' => [
            'related_model' => 'App\\DDD\\Domain\\Proposal\\Entities\\Proposal',
            'primary' => [
                'foreign_key' => 'ProposalId',
                'local_key' => 'ProposalId',
            ],
            'conditions' => [
                [
                    'foreign_key' => 'AccountId',
                    'local_key' => 'AccountId',
                ],
            ],
        ],
    ];
}
