<?php

declare(strict_types=1);

namespace App\DDD\Application\Schedule\Services;

use App\DDD\Application\Schedule\Interfaces\ScheduleServiceInterface;
use App\DDD\Application\Schedule\Services\Dependecies\ScheduleDependencies;
use App\DDD\Domain\Schedule\Interfaces\ScheduleValidationInterface;
use App\DDD\Infrastructure\Schedule\Interfaces\ScheduleProcessorInterface;
use App\DDD\Infrastructure\Schedule\Persistence\Interfaces\ScheduleRepositoryInterface;
use App\DDD\Infrastructure\Shared\Abstracts\Services\AbstractService;

class ScheduleService extends AbstractService implements ScheduleServiceInterface
{
    use ScheduleDependencies;

    public function __construct(
        private readonly ScheduleRepositoryInterface $scheduleRepository,
        private readonly ScheduleValidationInterface $scheduleValidation,
        private readonly ScheduleProcessorInterface $processorService
    ) {
        parent::__construct(
            $scheduleRepository,
            $scheduleValidation,
            $processorService
        );
    }
}
