<?php

declare(strict_types=1);

namespace App\DDD\Application\Schedule\Services\Dependecies;

use App\DDD\Domain\Schedule\Interfaces\ScheduleValidationInterface;
use App\DDD\Infrastructure\Schedule\Interfaces\ScheduleProcessorInterface;
use App\DDD\Infrastructure\Schedule\Interfaces\ScheduleRepositoryInterface;

trait ScheduleDependencies
{
    public function repository(): string
    {
        return ScheduleRepositoryInterface::class;
    }

    public function validator(): string
    {
        return ScheduleValidationInterface::class;
    }

    public function processor(): string
    {
        return ScheduleProcessorInterface::class;
    }
}
