<?php

declare(strict_types=1);

namespace App\DDD\Application\Schedule\DTOs;

use App\DDD\Application\Schedule\Schema\ScheduleSchema;
use App\DDD\Infrastructure\Shared\Abstracts\DTO\AbstractDto;

/**
 * DTO base para operações com documentos
 */
class ScheduleDto extends AbstractDto
{
    public static function getSchemaClass(): string
    {
        return ScheduleSchema::class;
    }

    protected static function getClass(array $data = []): self
    {
        return new self($data);
    }
}
