<?php

declare(strict_types=1);

namespace App\DDD\Application\Schedule\Schema;

use App\DDD\Infrastructure\Shared\Abstracts\Entities\Schema\AbstractSchema;

/**
 * Schema para entidade de Agendamento
 */
class ScheduleSchema extends AbstractSchema
{
    /**
     * Obtém a classe concreta para reflexão
     */
    protected static function getConcreteClass(): string
    {
        return self::class;
    }

    /**
     * Nome da tabela no banco de dados
     */
    public const TABLE_NAME = 'schedule';

    /**
     * Nome do campo de ID na tabela
     */
    public const ID_NAME = 'ScheduleId';

    /**
     * Mensagens de erro para validação
     */
    public const MESSAGES_DATABASE = 'Schedule## Dados inválidos: ';

    public const MESSAGES_SALESFORCE = 'Schedule## Dados Salesforce inválidos: ';

    /**
     * Definição dos campos e suas regras de validação
     */
    public const FIELDS = [
        'ScheduleId' => 'required|string',
        'EmpreendimentoId' => 'required|string',
        'Subject' => 'required|string|max:255',
        'Type' => 'required|string|max:100',
        'StartDateTime' => 'nullable|date',
        'EndDateTime' => 'nullable|date',
        'Location' => 'nullable|string|max:255',
    ];

    /**
     * Mapeamento dos campos do Salesforce para os campos do banco de dados
     */
    private const DEFAULT_SALESFORCE_FIELDS = [
        'Id',
        'WhatId',
        'Subject',
        'Type',
        'StartDateTime',
        'EndDateTime',
        'Location',
    ];

    /**
     * Mapeamento por tipo de objeto Salesforce
     */
    public const SALESFORCE_FIELDS = [
        'Event' => self::DEFAULT_SALESFORCE_FIELDS,
        'Proposta__c' => self::DEFAULT_SALESFORCE_FIELDS,
        'Empreendimento__c' => self::DEFAULT_SALESFORCE_FIELDS,
    ];

    /**
     * Campos obrigatórios por tipo para validação do Salesforce
     */
    public const REQUIRED_FIELDS_SALESFORCE = [
        'Proposta__c' => [
            'Empreendimento__c',
        ],
        'Empreendimento__c' => [
            'Id',
        ],
        'Event' => [
            'Id',
            'WhatId',
            'StartDateTime',
            'EndDateTime',
        ],
    ];

    /**
     * Campos obrigatórios por tipo para validação do banco de dados
     */
    public const REQUIRED_FIELDS_DATABASE = [
        'Proposta__c' => [
            'ScheduleId',
        ],
        'Empreendimento__c' => [
            'Id',
        ],
        'Event' => [
            'Id',
            'WhatId',
            'StartDateTime',
            'EndDateTime',
        ],
    ];

    /**
     * Define quais campos usar para retry quando a validação falha
     * A chave é o campo no banco de dados, o valor é o campo no Salesforce
     */
    public const RETRY_SALESFORCE = [];

    /**
     * Define qual tipo de objeto consultar no retry
     * A chave é o tipo original, o valor é o tipo a ser consultado
     */
    public const RETRY_OBJECT_TYPES = [];

    /**
     * Definição dos relacionamentos com outras entidades
     */
    public const RELATIONSHIPS = [
        'realEstateProject' => [
            'related_model' => 'App\DDD\Domain\RealEstateProject\Entities\RealEstateProject',
            'foreign_key' => 'EmpreendimentoId',
            'local_key' => 'EmpreendimentoId',
        ],
    ];
}
