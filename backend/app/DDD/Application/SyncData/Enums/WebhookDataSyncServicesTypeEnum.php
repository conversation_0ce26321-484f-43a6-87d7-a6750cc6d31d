<?php

declare(strict_types=1);

namespace App\DDD\Application\SyncData\Enums;

use App\DDD\Application\Asset\Services\AssetService;
use App\DDD\Application\Contract\Services\ContractService;
use App\DDD\Application\Document\Services\DocumentService;
use App\DDD\Application\Proposal\Services\ProposalService;
use App\DDD\Application\Proposal\Services\WebhookProposalService;
use App\DDD\Application\RealEstateProject\Services\RealEstateProjectService;
use App\DDD\Application\Schedule\Services\ScheduleService;
use App\DDD\Application\ScheduleServicesOptions\Services\ScheduleServicesOptionsService;
use App\DDD\Application\User\Services\UserService;
use App\DDD\Infrastructure\Shared\Interfaces\Enum\ServiceTypeEnumInterface;

enum WebhookDataSyncServicesTypeEnum: string implements ServiceTypeEnumInterface
{
    // use ServiceTypeTrait;
    case PROPOSAL = 'proposal';
    case DOCUMENT = 'document';
    // case USER = 'user';
    // case CONTRACT = 'contract';
    // case ASSET = 'asset';
    // case REAL_ESTATE_PROJECT = 'realEstateProject';
    // case SCHEDULE = 'schedule';
    // case SCHEDULE_SERVICES_OPTIONS = 'scheduleServicesOptions';

    /**
     * Get the description for the query type
     */
    public function getDescription(): string
    {
        return match ($this) {
            self::PROPOSAL => 'Proposta',
            self::DOCUMENT => 'Documento',
            // self::USER => 'Usuário',
            // self::CONTRACT => 'Contrato',
            // self::ASSET => 'Ativo',
            // self::REAL_ESTATE_PROJECT => 'Empreendimento',
            // self::SCHEDULE => 'Agendamentos',
            // self::SCHEDULE_SERVICES_OPTIONS => 'Horários disponíveis para serviços'
        };
    }

    /**
     * Get the strategy class for this query type
     */
    public function getStrategyClass(): string
    {
        return match ($this) {
            self::PROPOSAL => WebhookProposalService::class,
            self::DOCUMENT => DocumentService::class,
            // self::USER => UserService::class,
            // self::CONTRACT => ContractService::class,
            // self::ASSET => AssetService::class,
            // self::REAL_ESTATE_PROJECT => RealEstateProjectService::class,
            // self::SCHEDULE => ScheduleService::class,
            // self::SCHEDULE_SERVICES_OPTIONS => ScheduleServicesOptionsService::class
        };
    }

    public function getValue(): string
    {
        return $this->value;
    }
}
