<?php

declare(strict_types=1);

namespace App\DDD\Application\SyncData\Interfaces;

use App\DDD\Infrastructure\SyncData\Exceptions\MissingDataException;
use App\DDD\UI\SyncData\Http\Requests\SyncDataProposalBySignatureRequest;
use Illuminate\Foundation\Http\FormRequest;

interface SyncDataProposalBySignatureServiceInterface
{
    /**
     * Executa o fluxo de sincronização de propostas por assinatura
     *
     * @param  FormRequest  $request
     * @return mixed
     */
    public function execute(SyncDataProposalBySignatureRequest $request);

    /**
     * Trata casos onde dados estão faltando
     *
     * @return mixed
     */
    public function handleMissingData(MissingDataException $e, FormRequest $request);
}
