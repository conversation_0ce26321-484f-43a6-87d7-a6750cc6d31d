<?php

declare(strict_types=1);

namespace App\DDD\Application\SyncData\Services;

use App\DDD\Application\SyncData\Interfaces\SyncDataServicesInterface;
use App\DDD\Application\SyncData\Services\Dependencias\SyncDataDependencies;
use App\DDD\Domain\SyncData\Services\SyncDataQueriesService;
use App\DDD\Domain\SyncData\ValueObjects\SyncDataQueryTypeEnum;
use App\DDD\Infrastructure\Shared\Abstracts\Services\AbstractService;
use App\DDD\Infrastructure\SyncData\Persistence\Factory\SyncDataQueryFactory;
use App\DDD\Infrastructure\SyncData\Persistence\Services\SyncDataPersistenceService;
use App\DDD\Infrastructure\SyncData\Validators\SyncDataServiceValidation;
use App\DDD\Infrastructure\SyncData\Validators\WebhookSyncDataServiceValidation;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\ValidationException;

class WebhookSyncDataProposalBySignatureService extends AbstractService implements SyncDataServicesInterface
{
    use SyncDataDependencies;

    public function __construct(
        private readonly WebhookSyncDataServiceValidation $syncDataValidator,
        private readonly SyncDataPersistenceService $persistenceService,
        private readonly WebhookDataSyncServiceTransform $dataSyncServiceTransform,
    ) {
        parent::__construct(
            null,
            null,
            null,
            $dataSyncServiceTransform
        );
    }

    /**
     * Execute Salesforce data processing with custom pre-processing.
     * In this case, the class DataSyncServiceTransform will call execute on the abstract service because DataSync is responsible for many objects (Proposal, Asset, Contract, etc).
     *
     * @param  array  $params  Parameters for processing
     * @return array Processing results
     */
    public function execute(array $params): array
    {
        try {
            // $params = ['pv' => $params['request']->getData()];

            $dataSyncData = $this->transformWebhookDataInDto($params['request']->getData());

            return $this->persistenceService->persistAll($dataSyncData);
        } catch (ValidationException $e) {
            Log::error($e);

            return ['error' => $e->getMessage()];
        }
    }
}
