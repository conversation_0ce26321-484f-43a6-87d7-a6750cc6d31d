<?php

declare(strict_types=1);

namespace App\DDD\Application\SyncData\Services;

use App\DDD\Application\SyncData\Enums\DataSyncServicesTypeEnum;
use App\DDD\Infrastructure\Shared\Abstracts\Transform\AbstractServiceTransform;
use App\DDD\Infrastructure\Shared\Interfaces\Transform\TransformInterface;

class DataSyncServiceTransform extends AbstractServiceTransform implements TransformInterface
{
    public function getServiceTypeEnum(): string
    {
        return DataSyncServicesTypeEnum::class;
    }
}
