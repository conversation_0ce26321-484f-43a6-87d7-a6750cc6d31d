<?php

declare(strict_types=1);

namespace App\DDD\Application\SyncData\Services;


use App\DDD\Application\SyncData\Interfaces\SyncDataPersistenceServiceInterface;
use App\DDD\Application\SyncData\Interfaces\SyncDataServicesInterface;
use App\DDD\Application\SyncData\Services\Dependencias\SyncDataDependencies;
use App\DDD\Domain\SyncData\Services\SyncDataQueriesService;
use App\DDD\Domain\SyncData\ValueObjects\SyncDataQueryTypeEnum;
use App\DDD\Infrastructure\Shared\Abstracts\Services\AbstractService;
use App\DDD\Infrastructure\SyncData\Persistence\Factory\SyncDataQueryFactory;
use App\DDD\Infrastructure\SyncData\Validators\SyncDataServiceValidation;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\ValidationException;

class SyncDataProposalBySignatureService extends AbstractService implements SyncDataServicesInterface
{

    // IMPORTANTE: Incluir dependencias como o $dataSyncServiceTransform
    use SyncDataDependencies;

    public function __construct(
        private readonly SyncDataServiceValidation $syncDataValidator,
        private readonly SyncDataPersistenceServiceInterface $persistenceService,
        private readonly DataSyncServiceTransform $dataSyncServiceTransform,
    ) {
        parent::__construct(
            null,
            null,
            null,
            $dataSyncServiceTransform
        );
    }

    /**
     * Execute Salesforce data processing with custom pre-processing.
     * In this case, the class DataSyncServiceTransform will call execute on the abstract service because DataSync is responsible for many objects (Proposal, Asset, Contract, etc).
     *
     * @param  array  $params  Parameters for processing
     * @return array Processing results
     */
    public function execute(array $params): array
    {
        try {
            $params = ['pv' => $params['request']->getData()];

            $queryType = SyncDataQueryTypeEnum::SYNC_PROPOSAL_BY_SIGNATURE;
            $queryFactory = new SyncDataQueryFactory();
            $queryService = new SyncDataQueriesService($params, $queryFactory, $queryType);
            $paramsQuery = [
                'params' => $params,
                'queryType' => $queryService,
            ];
            $salesforceData = $this->salesforceService->queryObject('query', $paramsQuery);

            $this->salesforceService->validateSalesforceData($salesforceData);

            $dataSyncData = $this->transformDataInDto($salesforceData['records'][0]);

            return $this->persistenceService->persistAll($dataSyncData);
        } catch (ValidationException $e) {
            Log::error($e);

            return ['error' => $e->getMessage()];
        }
    }
}
