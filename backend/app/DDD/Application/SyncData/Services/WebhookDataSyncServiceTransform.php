<?php

declare(strict_types=1);

namespace App\DDD\Application\SyncData\Services;

use App\DDD\Application\SyncData\Enums\WebhookDataSyncServicesTypeEnum;
use App\DDD\Infrastructure\Shared\Abstracts\Transform\AbstractServiceTransform;
use App\DDD\Infrastructure\Shared\Interfaces\Transform\TransformInterface;

class WebhookDataSyncServiceTransform extends AbstractServiceTransform implements TransformInterface
{
    public function getServiceTypeEnum(): string
    {
        return WebhookDataSyncServicesTypeEnum::class;
    }
}
