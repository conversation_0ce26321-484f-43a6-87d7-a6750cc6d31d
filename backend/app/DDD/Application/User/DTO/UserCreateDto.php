<?php

declare(strict_types=1);

namespace App\DDD\Application\User\DTO;

use App\DDD\Application\User\Schema\UserSchema;
use App\DDD\Infrastructure\Shared\Abstracts\DTO\AbstractDTO;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;

class UserCreateDto extends AbstractDTO
{
    /**
     * Retorna a classe concreta do DTO
     *
     * @param array $data
     * @return self
     */
    protected static function getClass(array $data = []): self
    {
        return new self($data);
    }

    /**
     * Retorna a classe de schema associada ao DTO
     *
     * @return string
     */
    public static function getSchemaClass(): string
    {
        return UserSchema::class;
    }

    /**
     * Gera uma senha aleatória e a define no DTO
     *
     * @return self
     */
    public function generateRandomPassword(): self
    {
        $password = strtoupper(Str::random(8));
        $this->data['password_plaintext'] = $password;
        $this->data['password'] = Hash::make($password);

        return $this;
    }

    /**
     * Sobrescreve o método fromArray para adicionar lógica específica
     *
     * @param array $data
     * @return self
     */
    public static function fromArray(array $data): self
    {
        // Se Id estiver presente mas AccountId não, usa Id como AccountId
        if (isset($data['Id']) && !isset($data['AccountId'])) {
            $data['AccountId'] = $data['Id'];
        }

        // Se password estiver presente, garante que está no formato correto
        if (isset($data['password']) && !empty($data['password'])) {
            // Verifica se a senha já está hasheada
            if (strlen($data['password']) < 60) {
                $data['password_plaintext'] = $data['password'];
                $data['password'] = Hash::make($data['password']);
            }
        }

        return parent::fromArray($data);
    }
}
