<?php

declare(strict_types=1);

namespace App\DDD\Application\User\Schema;

use App\DDD\Infrastructure\Shared\Abstracts\Entities\Schema\AbstractSchema;

class UserSchema extends AbstractSchema
{
    protected static function getConcreteClass(): string
    {
        return self::class;
    }

    public const TABLE_NAME = 'users';

    public const ID_NAME = 'AccountId';

    public const MESSAGES_DATABASE = 'User## Dados inválidos: ';

    public const MESSAGES_SALESFORCE = 'User## Dados Salesforce inválidos: ';

    public const FIELDS = [
        'AccountId' => '',
        'Name' => '',
        'CPF__c' => '',
        'CNPJ__c' => '',
        'email_verified_at' => '',
        'password' => '',
        'password_plaintext' => '',
        'PersonContactId' => '',
        'avatar' => '',
        'alternative_name' => '',
        'Email__c' => '',
        'FirstName' => '',
        'LastName' => '',
        'TelefoneCelular__c' => '',
        'TelefoneCelular2__c' => '',
        'TelefoneComercial__c' => '',
        'TelefoneFixo__c' => '',
        'EmailAlternativo__c' => '',
        'CodigoSienge__c' => '',
        'ShippingPostalCode__c' => '',
        'ShippingNeighborhood__c' => '',
        'ShippingStreet__c' => '',
        'ShippingCity__c' => '',
        'ShippingNumber__c' => '',
        'ShippingState__c' => '',
        'ShippingComplement__c' => '',
        'ShippingCountry__c' => '',
        'MembroPatrimonioAfetacao__c' => 'required|boolean',
        'Sindico__c' => 'required|boolean',
        'CreatedDate' => 'nullable|date',
        'LastModifiedDate' => 'nullable|date',
        'created_at' => 'nullable|date',
        'updated_at' => 'nullable|date',
        'messages' => 'nullable|array',
    ];

    public const SALESFORCE_FIELDS = [
        'Proposta__c' => [
            'Conta__r.Id',
            'Conta__r.Name',
            'Conta__r.Email__c',
            'Conta__r.PersonContactId',
            'Conta__r.PersonContact.Id',
            'Conta__r.PersonContact.FirstName',
            'Conta__r.PersonContact.LastName',
            'Conta__r.PersonContact.Email',
            'Conta__r.PersonContact.Phone',
            'Conta__r.PersonContact.MobilePhone',
            'Conta__r.PersonContact.HomePhone',

        ],
    ];

    public const FIELDS_ANOTHER_RULES = [
        'MembroPatrimonioAfetacao__c' => 'required|boolean',
        'Sindico__c' => 'required|boolean',
    ];

    public const REQUIRED_FIELDS_SALESFORCE = [
        'Account' => [
            'Id',
            'Name',
            'Email__c',
            'PersonContactId',
        ],
        'Proposta__c' => [
            'Conta__r.Id',
            'Conta__r.Name',
            'Conta__r.Email__c',
            'Conta__r.PersonContactId',
        ],
    ];

    public const REQUIRED_FIELDS_DATABASE = [
        'Proposta__c' => [
            'AccountId',
            'Name',
            'Email__c',
            'PersonContactId',
        ],
    ];

    /**
     * Define quais campos usar para retry quando a validação falha
     * A chave é o campo no banco de dados, o valor é o campo no Salesforce
     */
    public const RETRY_SALESFORCE = [];

    /**
     * Define qual tipo de objeto consultar no retry
     * A chave é o tipo original, o valor é o tipo a ser consultado
     */
    public const RETRY_OBJECT_TYPES = [];

    public const RELATIONSHIPS = [
        'sindico' => [
            'related_model' => 'App\\DDD\\Domain\\RealEstateProject\\Entities\\RealEstateProject',
            'primary' => [
                'foreign_key' => 'Sindico__c',
                'local_key' => 'AccountId',
            ],
            'type' => 'hasMany',
            'select' => [
                'EmpreendimentoId',
                'name',
                'StatusMacro__c',
                'imgsCarrossel',
                'Sindico__c',
                'LogoEmpreendimento__c',
                'CodigoSienge__c',
                'imgsCarrossel',
                'Fotos__c',
                'Videos__c',
            ],
        ],
        'documents' => [
            'related_model' => 'App\\DDD\\Domain\\Document\\Entities\\Document',
            'primary' => [
                'foreign_key' => 'AccountId',
                'local_key' => 'AccountId',
            ],
            'type' => 'hasMany',
        ],
        'assets' => [
            'related_model' => 'App\\DDD\\Domain\\Asset\\Entities\\Asset',
            'primary' => [
                'foreign_key' => 'AccountId',
                'local_key' => 'AccountId',
            ],
            'type' => 'hasMany',
            'select' => [
                'AssetId',
                'EmpreendimentoId',
                'ContractId',
                'ProposalId',
                'AccountId',
                'Name',
                'DataCompra__c',
                'statusBoletoAto',
            ],
            'with' => [
                'proposal',
                'realEstateProject',
                'contract',
            ],
        ],
        'contracts' => [
            'related_model' => 'App\\DDD\\Domain\\Contract\\Entities\\Contract',
            'primary' => [
                'foreign_key' => 'AccountId',
                'local_key' => 'AccountId',
            ],
            'type' => 'hasMany',
            'where' => [
                [
                    'column' => 'Status',
                    'operator' => '!=',
                    'value' => 'Distratado',
                ],
            ],
        ],
        'proposals' => [
            'related_model' => 'App\\DDD\\Domain\\Proposal\\Entities\\Proposal',
            'primary' => [
                'foreign_key' => 'AccountId',
                'local_key' => 'AccountId',
            ],
            'type' => 'hasMany',
            'where' => [
                [
                    'column' => 'Etapa__c',
                    'operator' => '!=',
                    'value' => 'Cancelado',
                ],
            ],
        ],
    ];
}
