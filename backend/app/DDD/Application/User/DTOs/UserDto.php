<?php

declare(strict_types=1);

namespace App\DDD\Application\User\DTOs;

use App\DDD\Application\User\Schema\UserSchema;
use App\DDD\Infrastructure\Shared\Abstracts\DTO\AbstractDto;
use App\DDD\Infrastructure\Shared\Interfaces\DTO\DtoInterface;

class UserDto extends AbstractDto implements DtoInterface
{
    public static function getSchemaClass(): string
    {
        return UserSchema::class;
    }

    protected static function getClass(array $data = []): self
    {
        return new self($data);
    }
}
