<?php

declare(strict_types=1);

namespace App\DDD\Application\User\Services;

use App\DDD\Application\User\Interfaces\UserServiceInterface;
use App\DDD\Application\User\Services\Dependencies\UserDependencies;
use App\DDD\Domain\User\Entities\User;
use App\DDD\Infrastructure\Shared\Abstracts\DTO\AbstractDto;
use App\DDD\Infrastructure\Shared\Abstracts\Services\AbstractService;
use App\DDD\Infrastructure\Shared\Utils\FormatDocs;
use App\DDD\Infrastructure\User\Events\UserCreatedEvent;
use App\DDD\Infrastructure\User\Interfaces\UserProcessorInterface;
use App\DDD\Infrastructure\User\Interfaces\UserRepositoryInterface;
use App\DDD\Infrastructure\User\Interfaces\UserValidationInterface;
use App\Services\Salesforce\UserSalesforceService;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\Log;

class UserService extends AbstractService implements UserServiceInterface
{
    use UserDependencies;

    public function __construct(
        private readonly UserRepositoryInterface $userRepository,
        private readonly UserValidationInterface $userValidator,
        private readonly UserProcessorInterface $processorService
    ) {
        parent::__construct(
            $userRepository,
            $userValidator,
            $processorService
        );
    }

    public function create(AbstractDto $dto): Model|Collection
    {
        $user = $this->userRepository->create($dto);

        event(new UserCreatedEvent($user->toArray()));

        return $user;
    }

    public function getUserByCpfOrCnpj(string $userLogin): ?User
    {
        return User::query()
            ->when(
                strlen($userLogin) <= 15,
                function ($query) use ($userLogin) {
                    return $query->where('CPF__c', FormatDocs::formatCpf($userLogin));
                },
                function ($query) use ($userLogin) {
                    return $query->where('CNPJ__c', FormatDocs::formatCnpj($userLogin));
                }
            )
            ->first();
    }


    public function search($inputName, $inputState): LengthAwarePaginator
    {
        $usersQuery = User::query()
            ->select('id', 'Name', 'AccountId', 'CPF__c', 'CNPJ__c', 'Email__c', 'ShippingState__c', 'updated_at')
            ->orderBy('users.updated_at', 'DESC');

        if (isset($inputName)) {
            $query = strtolower($inputName);
            $usersQuery->where(function ($q) use ($query) {
                $q->whereRaw('LOWER("Name") LIKE ?', ["%{$query}%"])
                    ->orWhereRaw('LOWER("Email__c") LIKE ?', ["%{$query}%"])
                    ->orWhereRaw('LOWER("CPF__c") LIKE ?', ["%{$query}%"])
                    ->orWhereRaw('LOWER("CNPJ__c") LIKE ?', ["%{$query}%"]);
            });
        }

        if (isset($inputState)) {
            $state = strtolower($inputState);
            $usersQuery->whereRaw('LOWER("ShippingState__c") LIKE ?', ["%{$state}%"]);
        }

        $page = max(1, intval(request('page', 1)));
        $perPage = 20;

        $totalUsers = $usersQuery->count();

        $users = $usersQuery
            ->forPage($page, $perPage)
            ->get()
            ->map(function ($user) {
                return [
                    'id' => $user->id,
                    'Name' => $user->Name,
                    'AccountId' => $user->AccountId,
                    'CPF__c' => $user->CPF__c,
                    'CNPJ__c' => $user->CNPJ__c,
                    'Email__c' => $user->Email__c,
                    'ShippingState__c' => $user->ShippingState__c,
                    'updated_at' => $user->updated_at,
                ];
            });

        $paginator = new LengthAwarePaginator(
            $users,
            $totalUsers,
            $perPage,
            $page,
            ['path' => request()->url(), 'query' => request()->query()]
        );

        return $paginator;
    }

    public function updateDataBySalesforce($id): User|Collection|null
    {
        $userSalesforceService = app(UserSalesforceService::class);
        $data = $userSalesforceService->getDataBySalesforce($id);
        if ($data === null) {
            return null;
        }

        $user = $this->findOneByField('AccountId', $data['Id']);
        if (!isset($user)) {
            return null;
        }

        $user->Email__c = data_get($data, 'Email__c', $user->Email__c);
        $user->CodigoSienge__c = data_get($data, 'CodigoSienge__c', $user->CodigoSienge__c);
        $user->EmailAlternativo__c = data_get($data, 'EmailAlternativo__c', $user->EmailAlternativo__c);
        $user->TelefoneCelular__c = data_get($data, 'TelefoneCelular__c', $user->TelefoneCelular__c);
        $user->TelefoneCelular2__c = data_get($data, 'TelefoneCelular2__c', $user->TelefoneCelular2__c);
        $user->TelefoneComercial__c = data_get($data, 'TelefoneComercial__c', $user->TelefoneComercial__c);
        $user->TelefoneFixo__c = data_get($data, 'TelefoneFixo__c', $user->TelefoneFixo__c);
        $user->alternative_name = data_get($data, 'alternative_name', $user->alternative_name);
        $user->ShippingPostalCode__c = data_get($data, 'ShippingPostalCode__c', $user->ShippingPostalCode__c);
        $user->ShippingNeighborhood__c = data_get($data, 'ShippingNeighborhood__c', $user->ShippingNeighborhood__c);
        $user->ShippingStreet__c = data_get($data, 'ShippingStreet__c', $user->ShippingStreet__c);
        $user->ShippingCity__c = data_get($data, 'ShippingCity__c', $user->ShippingCity__c);
        $user->ShippingNumber__c = data_get($data, 'ShippingNumber__c', $user->ShippingNumber__c);
        $user->ShippingState__c = data_get($data, 'ShippingState__c', $user->ShippingState__c);
        $user->ShippingComplement__c = data_get($data, 'ShippingComplement__c', $user->ShippingComplement__c);
        $user->ShippingCountry__c = data_get($data, 'ShippingCountry__c', $user->ShippingCountry__c);
        $user->CreatedDate = data_get($data, 'CreatedDate', $user->CreatedDate);
        $user->LastModifiedDate = data_get($data, 'LastModifiedDate', $user->LastModifiedDate);
        $user->MembroPatrimonioAfetacao__c = data_get($data, 'MembroPatrimonioAfetacao__c', $user->MembroPatrimonioAfetacao__c);
        $user->Sindico__c = data_get($data, 'Sindico__c', $user->Sindico__c);
        $user->save();

        $documentService = app('App\Services\DocumentService'::class);
        $documentService->createDocuments($user->AccountId, 'conta');

        return $user;
    }

}
