<?php

declare(strict_types=1);

namespace App\DDD\Application\User\Services\Dependencies;

use App\DDD\Infrastructure\User\Interfaces\UserProcessorInterface;
use App\DDD\Infrastructure\User\Interfaces\UserRepositoryInterface;
use App\DDD\Infrastructure\User\Interfaces\UserValidationInterface;

trait UserDependencies
{
    public function repository(): string
    {
        return UserRepositoryInterface::class;
    }

    public function validator(): string
    {
        return UserValidationInterface::class;
    }

    public function processor(): string
    {
        return UserProcessorInterface::class;
    }
}
