<?php

declare(strict_types=1);

namespace App\DDD\Application\WorkOrder\Schema;

use App\DDD\Infrastructure\Shared\Abstracts\Entities\Schema\AbstractSchema;

class WorkOrderSchema extends AbstractSchema
{
    protected static function getConcreteClass(): string
    {
        return self::class;
    }

    public const TABLE_NAME = 'work_order';

    public const ID_NAME = 'WorkOrderId';

    public const MESSAGES_DATABASE = 'WorkOrder## Dados inválidos: ';

    public const MESSAGES_SALESFORCE = 'WorkOrder## Dados Salesforce inválidos: ';

    public const FIELDS = [
        'WorkOrderId' => '',
        'CaseId' => '',
        'OwnerId' => '',
        'ServiceTerritoryId' => '',
        'WorkOrderNumber' => '',
        'CreatedDate' => '',
        'CreatedById' => '',
        'LastModifiedDate' => '',
        'LastModifiedById' => '',
        'AccountId' => '',
        'AssetId' => '',
        'Description' => '',
        'StartDate' => '',
        'EndDate' => '',
        'Subject' => '',
        'Status' => '',
    ];

    public const SALESFORCE_FIELDS = [
        'WorkOrder' => [
            'Id' ,
            'CaseId' ,
            'OwnerId' ,
            'ServiceTerritoryId' ,
            'WorkOrderNumber' ,
            'CreatedDate' ,
            'CreatedById' ,
            'LastModifiedDate' ,
            'LastModifiedById' ,
            'AccountId' ,
            'AssetId' ,
            'Description' ,
            'StartDate' ,
            'EndDate' ,
            'Subject' ,
            'Status' ,
        ],
    ];

    public const FIELDS_ANOTHER_RULES = [

    ];

    public const REQUIRED_FIELDS_SALESFORCE = [
        'WorkOrder' => [
            'Id',
            'WorkOrderNumber',
            'AccountId',
            'CaseId',
        ]
    ];

    public const REQUIRED_FIELDS_DATABASE = [
        'WorkOrder' => [
            'WorkOrder',
            'WorkOrderNumber',
            'AccountId',
            'CaseId',
        ],
    ];


    public const RETRY_OBJECT_TYPES_RELATIONSHIP = [
        // 'Asset' => [
        //     'Unidade__c' => 'Id',
        //     'Unidade__r.Ativo__r.Id' => 'Id',
        //     'Unidade__r.Ativo__r.ContratoVigente__c' => 'ContratoVigente__c',
        //     'Unidade__r.Ativo__r.Name' => 'Name',
        //     'Unidade__r.Ativo__r.ContratoVigente__r.ContractNumber' => 'ContratoVigente__r.ContractNumber',
        // ],
    ];


    /**
     * Define quais campos usar para retry quando a validação falha
     * A chave é o campo no banco de dados, o valor é o campo no Salesforce
     */
    public const RETRY_SALESFORCE = [];

    /**
     * Define qual tipo de objeto consultar no retry
     * A chave é o tipo original, o valor é o tipo a ser consultado
     */
    public const RETRY_OBJECT_TYPES = [];

    public const RELATIONSHIPS = [
        'case' => [
            'related_model' => 'App\\Models\\CasesSales',
            'primary' => [
                'foreign_key' => 'CaseId',
                'local_key' => 'CaseId',
            ]
        ],
        'asset' => [
            'related_model' => 'App\\DDD\\Domain\\Asset\\Entities\\Asset',
            'primary' => [
                'foreign_key' => 'AssetId',
                'local_key' => 'AssetId',
            ],
        ],
    ];
}
