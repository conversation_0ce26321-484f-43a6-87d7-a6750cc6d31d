<?php

declare(strict_types=1);

namespace App\DDD\Application\WorkOrder\Services;

use App\DDD\Application\WorkOrder\Interfaces\WorkOrderServiceInterface;
use App\DDD\Domain\WorkOrder\Interfaces\WorkOrderValidationInterface;
use App\DDD\Domain\WorkOrder\Services\WorkOrderQueriesService;
use App\DDD\Domain\WorkOrder\ValueObjects\WorkOrderQueryTypeEnum;
use App\DDD\Infrastructure\Shared\Abstracts\Services\AbstractService;
use App\DDD\Infrastructure\WorkOrder\Factory\WorkOrderQueryFactory;
use App\DDD\Infrastructure\WorkOrder\Interfaces\WorkOrderProcessorInterface;
use App\DDD\Infrastructure\WorkOrder\Persistence\Interfaces\WorkOrderRepositoryInterface;
use Illuminate\Support\Facades\Log;

class WorkOrderService extends AbstractService implements WorkOrderServiceInterface
{
    private WorkOrderQueryFactory $queryFactory;

    public function __construct(
        private readonly WorkOrderRepositoryInterface $workOrderRepository,
        private readonly WorkOrderValidationInterface $workOrderValidator,
        private readonly WorkOrderProcessorInterface $processorService
    ) {
        parent::__construct(
            $workOrderRepository,
            $workOrderValidator,
            $processorService
        );
        $this->queryFactory = new WorkOrderQueryFactory();
    }

    public function validator(): string
    {
        return WorkOrderValidationInterface::class;
    }
    public function getWorkOrders(array $params): array|string|null
    {
        //$params = data_get($params, 'request', $params);
        $params = isset($params['request']) ? ['NomeEmpreendimento__c' => $params['request']->getData()['NomeEmpreendimento__c']] : $params;

        $queryType = WorkOrderQueryTypeEnum::GET_WORK_ORDERS;
        $queryService = new WorkOrderQueriesService($params, $this->queryFactory, $queryType);
        $paramsQuery = [
            'params' => $params,
            'queryType' => $queryService,
        ];
        $salesforceData = $this->salesforceService->queryObject('query', $paramsQuery);
        $salesforceData = $salesforceData['records'];

        if(count($salesforceData) === 0){
            return [];
        }

        $result = $this->execute(['salesforceData' => $salesforceData]);

        $result = data_get($result, 'models', $result);
        $result = data_get($result, 'model', $result);
        Log::debug('####################################');
        Log::debug('result: ' . json_encode($result));
        Log::debug($result);
        return $result ?? [];
    }
    public function verifyWorkOrderIsAvaliable(array $params): bool
    {
        $params = ['startDate' => $params['startDate'], 'NomeEmpreendimento__c' => $params['NomeEmpreendimento__c']];

        $queryType = WorkOrderQueryTypeEnum::GET_WORK_ORDER_DATE;
        $queryService = new WorkOrderQueriesService($params, $this->queryFactory, $queryType);

        $paramsQuery = [
            'params' => $params,
            'queryType' => $queryService,
        ];

        $avaliabe = $this->salesforceService->queryObject('query', $paramsQuery);
        $avaliabe = data_get($avaliabe, 'records', []);
        if (count($avaliabe) > 0) {
            return false;
        }

        return true;

    }

}
