<?php

declare(strict_types=1);

namespace App\DDD\Application\WorkOrder\DTOs;

use App\DDD\Application\WorkOrder\Schema\WorkOrderSchema;
use App\DDD\Infrastructure\Shared\Abstracts\DTO\AbstractDto;
use App\DDD\Infrastructure\Shared\Interfaces\DTO\DtoInterface;

class WorkOrderDto extends AbstractDto implements DtoInterface
{
    public static function getSchemaClass(): string
    {
        return WorkOrderSchema::class;
    }

    protected static function getClass(array $data = []): self
    {
        return new self($data);
    }
}
