<?php

declare(strict_types=1);

namespace App\DDD\Domain\ScheduleServicesOptions\Validation;

use App\DDD\Application\ScheduleServicesOptions\Schema\ScheduleServicesOptionsSchema;
use App\DDD\Domain\ScheduleServicesOptions\Interfaces\ScheduleServicesOptionsValidationInterface;
use App\DDD\Infrastructure\Shared\Abstracts\Validators\AbstractValidator;

class ScheduleServicesOptionsValidation extends AbstractValidator implements ScheduleServicesOptionsValidationInterface
{
    public function getSchemaClass(): string
    {
        return ScheduleServicesOptionsSchema::class;
    }
}
