<?php

declare(strict_types=1);

namespace App\DDD\Domain\ScheduleServicesOptions\Entities;

use App\DDD\Application\ScheduleServicesOptions\Schema\ScheduleServicesOptionsSchema;
use App\DDD\Infrastructure\Shared\Abstracts\Entities\AbstractEntities;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ScheduleServicesOptions extends AbstractEntities
{
    use HasFactory;

    public static function getSchemaClass(): string
    {
        return ScheduleServicesOptionsSchema::class;
    }

    public function realEstateProject(): BelongsTo
    {
        return $this->createComplexRelationship('realEstateProject');
    }
}
