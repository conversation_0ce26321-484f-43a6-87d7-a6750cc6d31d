<?php

declare(strict_types=1);

namespace App\DDD\Domain\Document\Validation;

use App\DDD\Application\Document\Schema\DocumentSchema;
use App\DDD\Domain\Document\Interfaces\DocumentValidationInterface;
use App\DDD\Infrastructure\Shared\Abstracts\Validators\AbstractValidator;

class DocumentValidation extends AbstractValidator implements DocumentValidationInterface
{
    public function getSchemaClass(): string
    {
        return DocumentSchema::class;
    }
}
