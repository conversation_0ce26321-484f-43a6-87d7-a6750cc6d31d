<?php

declare(strict_types=1);

namespace App\DDD\Domain\Document\ValueObjects;

use App\DDD\Infrastructure\Shared\Interfaces\Enum\EnumInterface;

enum SubgrupoEnum: string implements EnumInterface
{
    case HABITE_SE = 'Habite-se';
    case MANUAL_DO_SINDICO = 'Manual do Síndico';
    case ATESSE_AREA_COMUM = 'Ateste - Área Comum';
    case AVCB = 'AVCB';
    case NOTA_FISCAL_DOS_EXTINTORES = 'Nota fiscal dos extintores';
    case ART_DE_EXECUCAO_DE_REDE_DE_GAS = 'ART de execução de rede de gás';
    case LAUDO_DE_TESTE_DA_REDE_DE_GAS = 'Laudo de teste da rede de gás';
    case LAUDO_DE_LIMPEZA_DE_ESGOTO_E_AGUAS_PLUVIAIS = 'Laudo de limpeza de esgoto e águas pluviais';
    case TESTE_DE_ESTANQUEIDADE_DAS_REDES_DE_DISTRIBUICAO_DE_AGUA_FRIA_E_ESGOTO = 'Teste de estanqueidade das redes de distribuição de água fria e esgoto';
    case NOTA_FISCAL_DOS_INTERFONES = 'Nota fiscal dos interfones';
    case CERTIFICADO_DE_GARANTIA_DOS_INTERFONES = 'Certificado de garantia dos interfones';
    case NOTA_FISCAL_DAS_BOMBAS_DE_PISCINA_E_RESERVATORIO_DE_AGUA = 'Nota fiscal das bombas de piscina e reservatório de água';
    case CERTIFICADO_DE_GARANTIA_DAS_BOMBAS_DE_PISCINA_E_RESERVATORIO_DE_AGUA = 'Certificado de garantia das bombas de piscina e reservatório de água';
    case NOTA_FISCAL_DAS_ANTENAS_COLETIVAS = 'Nota fiscal das antenas coletivas';
    case CERTIFICADO_DE_GARANTIA_DAS_ANTENAS_COLETIVAS = 'Certificado de garantia das antenas coletivas';
    case ANOTACAO_DE_RESPONSABILIDADE_TECNICA_ART_DA_EXECUCAO_DO_TELHAO = 'Anotação  de responsabilidade técnica - ART da execução do telhado';
    case COMPROVANTE_DE_LIGACAO_EMITIDA_DA_CONCESSIONARIA_DE_AGUA_ESGOTO = 'Comprovante de ligação emitida da concessionária de água/ esgoto';
    case COMPROVANTE_DE_LIGACAO_EMITIDA_PELA_CONCESSIONARIA_DE_ENERGIA = 'Comprovante de ligação emitida pela concessionária de energia';
    case NOTA_FISCAL_DO_PRIMEIRO_ABASTECIMENTO_DE_GAS = 'Nota fiscal do primeiro abastecimento de gás';
    case ATERRAMENTO_DOS_PARA_RAIOS = 'Aterramento dos para-raios';
    case PLANILHA_RELACIONANDO_NUMERO_DO_HIDROMETRO_X_NUMERO_APARTAMENTO = 'Planilha relacionando número do hidrômetro x número apartamento';
    case REGISTRO_FOTOGRÁFICO_DO_RELOGIO_DA_ADMINISTRACAO_DO_CONDOMINIO = 'Registro fotográfico do relógio da administração do condomínio';
    case TERMO_DE_CIENCIA_E_DECLARACAO_DOS_DEBITOS_INERENTES_AO_RELOGIO_E_HIDROMETRO_DA_ADMINISTRACAO = 'Termo de ciência e declaração dos débitos inerentes ao relógio e hidrômetro da administração';
    case LAUDO_DA_DESINFECACAO_E_PORTABILIDADE_DE_AGUA_DO_RESERVATORIO = 'Laudo da desinfecção e portabilidade de água do reservatório';
    case RELATORIO_DE_BALANCO_PATRIMONIAL = 'Relatório de Balanço Patrimonial';
    case MINUTA_DE_CONVENCAO_DE_CONDOMINIO = 'Minuta de convenção de Condomínio';
    case MEMORIAL_DESCRITIVO = 'Memorial Descritivo';
    case MANUAL_DO_PROPRIETARIO = 'Manual do Proprietário';
    case ATA_PATRIMONIO_DE_AFETACAO = 'Ata-Patrimônio de Afetação';
    case VISITA_OBRA_PATRIMONIO = 'Visita Obra - Patrimônio';
    case ATA_DE_ASSEMBLEIA_DE_INSTALACAO = 'Ata de Assembleia de Instalação';
    case LISTA_DOS_INTEGRANTES_DA_COMISSAO = 'Lista dos Integrantes da Comissão';
    case MATERIAL_WORKSHOP = 'Material Workshop';
    case MEMORIAL_DE_INCORPORACAO = 'Memorial de Incorporação';
    case NUMERO_DE_CONTRIBUINTE = 'Número de Contribuinte';

    public function label(): string
    {
        return $this->value;
    }

    public static function isValid(string $value): bool
    {
        return in_array($value, self::cases());
    }

    public static function toArray(): array
    {
        return array_map(fn (SubgrupoEnum $subgrupo) => $subgrupo->value, self::cases());
    }

    public static function fromSalesforce(string $value): EnumInterface
    {
        return self::from($value);
    }
}
