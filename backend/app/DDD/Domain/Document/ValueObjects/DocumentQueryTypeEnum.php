<?php

declare(strict_types=1);

namespace App\DDD\Domain\Document\ValueObjects;

use App\DDD\Infrastructure\Document\Persistence\Queries\GetDocumentFilesQueries;
use App\DDD\Infrastructure\Document\Persistence\Queries\GetIdDocumentQueries;

enum DocumentQueryTypeEnum: string
{
    case GET_IDS_DOCUMENTS = 'GET_IDS_DOCUMENTS';
    case GET_FILES_DOCUMENTS = 'GET_FILES_DOCUMENTS';

    /**
     * Get the description for the query type
     */
    public function getDescription(): string
    {
        return match ($this) {
            self::GET_IDS_DOCUMENTS => 'Pega os ids dos documentos',
            self::GET_FILES_DOCUMENTS => 'Pega os arquivos dos documentos',
        };
    }

    /**
     * Get the strategy class for this query type
     */
    public function getStrategyClass(): string
    {
        return match ($this) {
            self::GET_IDS_DOCUMENTS => GetIdDocumentQueries::class,
            self::GET_FILES_DOCUMENTS => GetDocumentFilesQueries::class,
        };
    }

    /**
     * Check if the query type requires authentication
     */
    public function requiresAuthentication(): bool
    {
        return match ($this) {
            self::GET_IDS_DOCUMENTS => false,
            self::GET_FILES_DOCUMENTS => false,
        };
    }
}
