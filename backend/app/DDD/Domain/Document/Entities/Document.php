<?php

declare(strict_types=1);

namespace App\DDD\Domain\Document\Entities;

use App\DDD\Application\Document\Schema\DocumentSchema;
use App\DDD\Infrastructure\Shared\Abstracts\Entities\AbstractEntities;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Document extends AbstractEntities
{
    use HasFactory;

    public static function getSchemaClass(): string
    {
        return DocumentSchema::class;
    }

    public function user(): BelongsTo
    {
        return $this->createComplexRelationship('user');
    }

    public function contracts(): BelongsTo
    {
        return $this->createComplexRelationship('contracts');
    }

    public function proposal(): BelongsTo
    {
        return $this->createComplexRelationship('proposal');
    }

    public function realEstateProject(): BelongsTo
    {
        return $this->createComplexRelationship('realEstateProject');
    }
}
