<?php

declare(strict_types=1);

namespace App\DDD\Domain\Installment\ValueObjects;

use App\DDD\Infrastructure\Installment\Persistence\Queries\GetBoletoAtoQueries;
use App\DDD\Infrastructure\Installment\Persistence\Queries\GetBoletoQueries;
use App\DDD\Infrastructure\Installment\Persistence\Queries\GetInstallmentsQueries;

enum InstallmentQueryTypeEnum: string
{
    case GET_INSTALLMENTS = 'GET_INSTALLMENTS';
    case GET_BOLETO_ATO = 'GET_BOLETO_ATO';
    case GET_BOLETO = 'GET_BOLETO';

    /**
     * Get the description for the query type
     */
    public function getDescription(): string
    {
        return match ($this) {
            self::GET_INSTALLMENTS => 'Pega as parcelas',
            self::GET_BOLETO_ATO => 'Pega o boleto ato',
            self::GET_BOLETO => 'Pega o boleto',
        };
    }

    /**
     * Get the strategy class for this query type
     */
    public function getStrategyClass(): string
    {
        return match ($this) {
            self::GET_INSTALLMENTS => GetInstallmentsQueries::class,
            self::GET_BOLETO_ATO => GetBoletoAtoQueries::class,
            self::GET_BOLETO => GetBoletoQueries::class,
        };
    }

    /**
     * Check if the query type requires authentication
     */
    public function requiresAuthentication(): bool
    {
        return match ($this) {
            self::GET_INSTALLMENTS => false,
            self::GET_BOLETO_ATO => false,
            self::GET_BOLETO => false,
        };
    }
}
