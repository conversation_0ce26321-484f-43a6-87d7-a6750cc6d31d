<?php

declare(strict_types=1);

namespace App\DDD\Domain\RealEstateProject\Validation;

use App\DDD\Application\RealEstateProject\Schema\RealEstateProjectSchema;
use App\DDD\Domain\RealEstateProject\Interfaces\RealEstateProjectValidationInterface;
use App\DDD\Infrastructure\Shared\Abstracts\Validators\AbstractValidator;

class RealEstateProjectValidation extends AbstractValidator implements RealEstateProjectValidationInterface
{
    public function getSchemaClass(): string
    {
        return RealEstateProjectSchema::class;
    }
}
