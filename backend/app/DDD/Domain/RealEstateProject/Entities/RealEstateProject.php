<?php

declare(strict_types=1);

namespace App\DDD\Domain\RealEstateProject\Entities;

use App\DDD\Application\RealEstateProject\Schema\RealEstateProjectSchema;
use App\DDD\Domain\Document\Entities\Document;
use App\DDD\Domain\Document\ValueObjects\SubgrupoEnum;
use App\DDD\Infrastructure\Shared\Abstracts\Entities\AbstractEntities;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;

class RealEstateProject extends AbstractEntities
{
    use HasFactory;

    public static function getSchemaClass(): string
    {
        return RealEstateProjectSchema::class;
    }

    public function user(): BelongsTo
    {
        return $this->createComplexRelationship('user');
    }

    public function sindico(): BelongsTo
    {
        return $this->createComplexRelationship('sindico');
    }

    public function contracts(): BelongsTo
    {
        return $this->createComplexRelationship('contracts');
    }

    public function proposal(): BelongsTo
    {
        return $this->createComplexRelationship('proposal');
    }

    public function assets(): BelongsTo
    {
        return $this->createComplexRelationship('assets');
    }

    public function schedule(): BelongsTo
    {
        return $this->createComplexRelationship('schedule');
    }

    public function scheduleServicesOptions(): BelongsTo
    {
        return $this->createComplexRelationship('scheduleServicesOptions');
    }

    public function documents(): HasMany
    {
        return $this->hasMany(Document::class, 'EmpreendimentoId', 'EmpreendimentoId')
            ->whereIn('Subgrupo__c', SubgrupoEnum::toArray())->orderBy('TituloArquivo__c', 'asc');
    }
}
