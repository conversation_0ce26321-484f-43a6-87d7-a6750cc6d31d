<?php

declare(strict_types=1);

namespace App\DDD\Domain\User\Validation;

use App\DDD\Application\User\Schema\UserSchema;
use App\DDD\Infrastructure\Shared\Abstracts\Validators\AbstractValidator;
use App\DDD\Infrastructure\User\Interfaces\UserValidationInterface;

class UserValidation extends AbstractValidator implements UserValidationInterface
{
    public function getSchemaClass(): string
    {
        return UserSchema::class;
    }

    public function isAdmin(int $userId): bool
    {
        // return $this->getSchema()->where('id', $userId)->where('is_admin', true)->exists();
        return true;// $this->getSchema()->where('id', $userId)->where('is_admin', true)->exists();
    }

    public function exists(int $userId): bool
    {
        // return $this->getSchema()->where('id', $userId)->exists();
        return true; // $this->getSchema()->where('id', $userId)->exists();
    }

}
