<?php

declare(strict_types=1);

namespace App\DDD\Domain\User\Entities;

use App\DDD\Application\User\Schema\UserSchema;
use App\DDD\Domain\Document\Entities\Document;
use App\DDD\Infrastructure\Shared\Abstracts\Entities\AbstractEntities;
use App\Models\CasesSales;
use App\Models\ConfigDateToNegotiation;
use App\Models\Empreendimento;
use App\Models\Message;
use App\Models\UserSales;
use Illuminate\Auth\Authenticatable;
use Illuminate\Auth\MustVerifyEmail;
use Illuminate\Auth\Passwords\CanResetPassword;
use Illuminate\Contracts\Auth\Access\Authorizable as AuthorizableContract;
use Illuminate\Contracts\Auth\Authenticatable as AuthenticatableContract;
use Illuminate\Contracts\Auth\CanResetPassword as CanResetPasswordContract;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Foundation\Auth\Access\Authorizable;
use Illuminate\Notifications\Notifiable;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;
use Laravel\Sanctum\HasApiTokens;

class User extends AbstractEntities implements AuthenticatableContract, AuthorizableContract, CanResetPasswordContract
{
    use Authenticatable;
    use Authorizable;
    use CanResetPassword;
    use MustVerifyEmail;
    use HasApiTokens;
    use HasFactory;
    use Notifiable;

    protected $hidden = [
        'password',
        'password_plaintext',
        'remember_token',
    ];

    public static function getSchemaClass(): string
    {
        return UserSchema::class;
    }

    public function getId(): int
    {
        return $this->id;
    }

    public function getPassword(): string
    {
        return $this->password;
    }

    public function assets(): HasMany
    {
        return $this->createComplexRelationship('assets');
    }

    public function contracts(): HasMany
    {
        return $this->createComplexRelationship('contracts');
    }

    public function proposals(): HasMany
    {
        return $this->createComplexRelationship('proposals');
    }

    public function sindico(): HasMany
    {
        return $this->createComplexRelationship('sindico');
    }

    // public function sindico2(): HasMany
    // {
    //     return $this->createComplexRelationship('sindico2');
    // }

    // public function sindico(): HasMany
    // {
    //     // $sindico = $this->hasMany(Empreendimento::class, 'Sindico__c', 'AccountId')
    //     // ->select([
    //     //     'EmpreendimentoId',
    //     //     'name',
    //     //     'StatusMacro__c',
    //     //     'imgsCarrossel',
    //     //     'Sindico__c',
    //     // ]);

    //     // Log::debug('sindico');
    //     // Log::debug('sindico', ['data' => $sindico->get()]);

    //     $sindico1 =  $this->createComplexRelationship('sindico');
    //     Log::debug('sindico1');
    //     Log::debug('sindico1', ['data' => $sindico1]);
    //     // $sindico2 =  $this->createComplexRelationship('sindico2');
    //     // Log::debug('sindico2');
    //     // Log::debug('sindico2', ['data' => $sindico2]);
    //     return $sindico1;
    // }

    public function contentsAuth(): Collection
    {
        $assetContents = collect();
        $this->assets()->chunk(50, function ($assets) use (&$assetContents) {
            $assetContents = $assetContents->merge($this->processAssetsAuth($assets));
        });

        $sindicoContents = $this->processSindicos();

        return $assetContents->concat($sindicoContents);
    }


    private function processAssetsAuth(Collection $assets): Collection
    {
        if ($assets->isEmpty()) {
            return collect();
        }

        return $assets->map(function ($asset) {
            $baseData = [
                'AssetId' => $asset->AssetId,
                'UnidadeAtivoName' => $asset->Name,
                'statusBoletoAto' => $asset->statusBoletoAto,
                'DataAntecipacaoDesconto__c' => $this->getConfigDate(),
                'type' => 'morador',

            ];

            $assetProposal = $asset->proposal;

            if ($assetProposal !== null) {
                $baseData += [
                    'ProposalId' => $assetProposal->ProposalId,
                ];
            } else {
                $baseData += [
                    'ProposalId' => null,
                ];
            }

            if ($asset->contract) {
                $baseData['ContractId'] = $asset->contract->ContractId;
                $baseData['SituacaoEntrega__c'] = $asset->contract->SituacaoEntrega__c;
                $baseData['Contract']['SituacaoEntrega__c'] = $asset->contract->SituacaoEntrega__c;
                $baseData['Contract']['StatusCarteira__c'] = $asset->contract->StatusCarteira__c;
            }

            if ($asset->realEstateProject) {
            $baseData['Empreendimento'] =
                    [
                        'EmpreendimentoId' => $asset->realEstateProject->EmpreendimentoId,
                        'name' => $asset->realEstateProject->name,
                        'LogoEmpreendimento__c' => $asset->realEstateProject->LogoEmpreendimento__c,
                        'Sindico__c' => $asset->realEstateProject->Sindico__c,
                        'CodigoSienge__c' => $asset->realEstateProject->CodigoSienge__c,
                        'StatusMacro__c' => $asset->realEstateProject->StatusMacro__c,
                        'DataRealizadaHabitese__c' => $asset->realEstateProject->DataRealizadaHabitese__c,
                    ];
            }
            // if ($asset->Empreendimento) {
            //     $baseData['Empreendimento'] =
            //         [
            //             'EmpreendimentoId' => $asset->Empreendimento->EmpreendimentoId,
            //             'name' => $asset->Empreendimento->name,
            //             'Sindico__c' => $asset->Empreendimento->Sindico__c,
            //             'CodigoSienge__c' => $asset->Empreendimento->CodigoSienge__c,
            //             'StatusMacro__c' => $asset->Empreendimento->StatusMacro__c,
            //             'DataRealizadaHabitese__c' => $asset->Empreendimento->DataRealizadaHabitese__c,
            //         ];
            // }

            return $baseData;
        });
    }

    private function processSindicos(): Collection
    {
        $sindicos = $this->sindico;
        if ($sindicos->isEmpty()) {
            return collect();
        }

        return $sindicos->map(function ($sindico) {
            return [
                'id' => $sindico->id,
                'ProposalId' => 'p' . $this->AccountId,
                'ContractId' => 'c' . $sindico->EmpreendimentoId,
                'name' => $this->getSindicoName($sindico),
                'UnidadeAtivoName' => null,
                'DataCompra__c' => null,
                'AssetId' => null,
                'PlantaId' => null,
                'planta' => null,
                'StatusFinanciamento__c' => null,
                'NomeCCA__c' => null,
                'TelefoneCelularCCA__c' => null,
                // 'documents' => $sindico->documentos(),
                'DataAntecipacaoDesconto__c' => null,
                'statusBoletoAto' => null,
                'Empreendimento' => $sindico->toArray(),
                'Contract' => null,
                'Etapa__c' => null,
                'type' => $sindico->StatusMacro__c !== 'Entregue' ? 'membro' : 'sindico',
            ];
            // });
        });
    }


    public function contents(): Collection
    {
        $assetContents = collect();
        $this->assets()->chunk(50, function ($assets) use (&$assetContents) {
            $assetContents = $assetContents->merge($this->processAssets($assets));
        });

        $sindicoContents = $this->processSindicos();

        return $assetContents->merge($sindicoContents);
        // });
    }

    private function processAssets(Collection $assets): Collection
    {
        if ($assets->isEmpty()) {
            return collect();
        }

        // $assets = $assets->take(1);
        return $assets->map(function ($asset) {
            $cacheKey = "asset_content_{$asset->AssetId}";

            $statusBoletoAto = $asset->statusBoletoAto;

            $baseData = [
                'AssetId' => $asset->AssetId,
                'name' => $asset->Name,
                'UnidadeAtivoName' => $asset->Name,
                'DataCompra__c' => $asset->DataCompra__c,
                'statusBoletoAto' => $statusBoletoAto,
                'DataAntecipacaoDesconto__c' => $this->getConfigDate(),
                'type' => 'morador',
            ];

            $assetProposal = $asset->proposal;


            if (isset($assetProposal)) {
                $baseData += [
                    'ProposalId' => $assetProposal->ProposalId,
                    'PlantaId' => $assetProposal->PlantaId,
                    'planta' => $assetProposal->planta,
                    'StatusFinanciamento__c' => $assetProposal->StatusFinanciamento__c,
                    'NomeCCA__c' => $assetProposal->NomeCCA__c,
                    'TelefoneCelularCCA__c' => $assetProposal->TelefoneCelularCCA__c,
                    'Etapa__c' => $assetProposal->Etapa__c,
                ];
                // $baseData['proposal'] = $assetProposal;
            }

            if ($asset->Contracts) {
                $baseData['ContractId'] = $asset->Contracts->ContractId;
                $baseData['Contract'] = $asset->Contracts;
            }

            if ($asset->Empreendimento) {
                $baseData['Empreendimento'] = $asset->Empreendimento;
                $baseData['Empreendimento']['documents'] = $asset->Empreendimento->documentos();
            }

            $baseData['documents'] = $this->getCachedDocuments($asset);
            return $baseData;

        });
    }


    private function getCachedDocuments($asset)
    {
        // $cacheKey = "docs_{$asset->AssetId}";

        // return Cache::remember($cacheKey, self::CACHE_TTL, function () use ($asset) {
        $docs = isset($asset->proposal) ? $asset->proposal->documentos() : $this->documentos();

        return isset($docs) && count($docs) > 0
            ? collect($docs)->map(function ($doc) {
                return [
                    'DocumentId' => $doc->DocumentId,
                    'EmpreendimentoId' => $doc->EmpreendimentoId,
                    'ProposalId' => $doc->ProposalId,
                    'Link__c' => $doc->Link__c,
                    'TituloArquivo__c' => $doc->TituloArquivo__c,
                    'VisivelCliente__c' => $doc->VisivelCliente__c,
                    'Subgrupo__c' => $doc->Subgrupo__c,
                    'IdInterno__c' => $doc->IdInterno__c,
                    'LinkS3' => $doc->LinkS3,
                    'Name' => $doc->Name,
                ];
            })
            : null;
        // });
    }

    private function determineStatusFinanciamento($asset): ?string
    {
        return $asset->proposal->StatusFinanciamento__c ?? $asset->Contracts->StatusCarteira__c ?? null;
    }

    public function getSindicoName($sindico): string
    {
        return $sindico->Name . ($sindico->StatusMacro__c !== 'Entregue' ? ' membro' : ' sindico');
    }

    private function getConfigDate()
    {
        return ConfigDateToNegotiation::first()->DataAntecipacaoDesconto__c;

    }

    public function documentos()
    {
        $documents = $this->hasMany(Document::class, 'AccountId', 'AccountId')
            ->orderBy('TituloArquivo__c', 'asc')
            ->get();
        return $documents;

    }

    public function cases()
    {
        return $this->hasMany(CasesSales::class, 'AccountId', 'AccountId');
    }

    public function messages()
    {
        return $this->hasMany(Message::class, 'RelationId', 'PersonContactId');
    }

    public function UserId()
    {
        return $this->getUserIdAttribute();
    }

    public function getUserIdAttribute()
    {
        return $this->UserSales()->value('UserId');
    }

    public function UserSales()
    {
        return $this->belongsTo(UserSales::class, 'AccountId', 'AccountId');
    }

    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
        ];
    }

    // public function documents(): Collection
    // {
    //     return $this->hasMany(Document::class, 'AccountId', 'AccountId')
    //         ->orderBy('TituloArquivo__c', 'asc')
    //         ->get();

    // }

    public function documents(): HasMany
    {
        return $this->createComplexRelationship('documents');
    }
}
