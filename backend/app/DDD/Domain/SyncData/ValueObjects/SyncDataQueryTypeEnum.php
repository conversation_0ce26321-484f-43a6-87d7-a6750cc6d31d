<?php

declare(strict_types=1);

namespace App\DDD\Domain\SyncData\ValueObjects;

use App\DDD\Infrastructure\SyncData\Persistence\Queries\Strategies\SyncProposalBySignatureQuery;

enum SyncDataQueryTypeEnum: string
{
    case SYNC_PROPOSAL_BY_SIGNATURE = 'sync_proposal_by_signature';

    /**
     * Get the description for the query type
     */
    public function getDescription(): string
    {
        return match ($this) {
            self::SYNC_PROPOSAL_BY_SIGNATURE => 'Sincroniza proposta por assinatura'
        };
    }

    /**
     * Get the strategy class for this query type
     */
    public function getStrategyClass(): string
    {
        return match ($this) {
            self::SYNC_PROPOSAL_BY_SIGNATURE => SyncProposalBySignatureQuery::class,
        };
    }

    /**
     * Check if the query type requires authentication
     */
    public function requiresAuthentication(): bool
    {
        return match ($this) {
            self::SYNC_PROPOSAL_BY_SIGNATURE => false,
        };
    }
}
