<?php

declare(strict_types=1);

namespace App\DDD\Domain\Schedule\ValueObjects;

use App\DDD\Infrastructure\Shared\Interfaces\Enum\EnumInterface;

enum SubjectEnum: string implements EnumInterface
{
    case ASSEMBLEIA_GERAL = 'Assembleia Geral';
    case ULTIMA_ATUALIZACAO_DO__ANDAMENTO_DE_OBRA = 'Última atualização do  andamento de obra';
    case ULTIMA_ATUALIZACAO_DO_ANDAMENTO_DE_OBRA = 'Última atualização do andamento de obra';

    public function label(): string
    {
        return $this->value;
    }

    public static function isValid(string $value): bool
    {
        //    return in_array($value, self::cases());
        return in_array($value, array_column(self::getConstants(), 'value'));
    }

    public static function toArray(): array
    {
        //        return array_map(fn (SubjectEnum $subgrupo) => $subgrupo->value, self::cases());
        return array_column(self::getConstants(), 'value');
    }

    public static function fromSalesforce(string $value): EnumInterface
    {
        //        return self::from($value);
        foreach (self::getConstants() as $case) {
            if ($case->value === $value) {
                return $case;
            }
        }
        throw new \ValueError("\"$value\" is not a valid backing value for enum " . self::class);
    }

    private static function getConstants(): array
    {
        return [
            self::ASSEMBLEIA_GERAL,
            self::ULTIMA_ATUALIZACAO_DO__ANDAMENTO_DE_OBRA,
            self::ULTIMA_ATUALIZACAO_DO_ANDAMENTO_DE_OBRA
        ];
    }
}
