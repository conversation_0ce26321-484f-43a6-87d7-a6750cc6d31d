<?php

declare(strict_types=1);

namespace App\DDD\Domain\Schedule\Entities;

use App\DDD\Application\Schedule\Schema\ScheduleSchema;
use App\DDD\Infrastructure\Shared\Abstracts\Entities\AbstractEntities;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Schedule extends AbstractEntities
{
    use HasFactory;

    public static function getSchemaClass(): string
    {
        return ScheduleSchema::class;
    }

    public function realEstateProject(): BelongsTo
    {
        return $this->createComplexRelationship('realEstateProject');
    }
}
