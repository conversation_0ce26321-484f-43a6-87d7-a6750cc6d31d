<?php

declare(strict_types=1);

namespace App\DDD\Domain\Schedule\Validation;

use App\DDD\Application\Schedule\Schema\ScheduleSchema;
use App\DDD\Domain\Schedule\Interfaces\ScheduleValidationInterface;
use App\DDD\Infrastructure\Shared\Abstracts\Validators\AbstractValidator;

class ScheduleValidation extends AbstractValidator implements ScheduleValidationInterface
{
    public function getSchemaClass(): string
    {
        return ScheduleSchema::class;
    }
}
