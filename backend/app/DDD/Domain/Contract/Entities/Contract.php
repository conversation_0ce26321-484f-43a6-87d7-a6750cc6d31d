<?php

declare(strict_types=1);

namespace App\DDD\Domain\Contract\Entities;

use App\DDD\Application\Contract\Schema\ContractSchema;
use App\DDD\Infrastructure\Shared\Abstracts\Entities\AbstractEntities;
use App\DDD\Infrastructure\Shared\Interfaces\Entities\EntitiesInterface;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Contract extends AbstractEntities implements EntitiesInterface
{
    use HasFactory;

    public static function getSchemaClass(): string
    {
        return ContractSchema::class;
    }

    public function user(): BelongsTo
    {
        return $this->createComplexRelationship('user');
    }

    public function assets(): BelongsTo
    {
        return $this->createComplexRelationship('assets');
    }

    public function contracts(): BelongsTo
    {
        return $this->createComplexRelationship('contracts');
    }

    public function proposals(): BelongsTo
    {
        return $this->createComplexRelationship('proposals');
    }

    public function documents(): HasMany
    {
        return $this->createComplexRelationship('documents');
    }

    public function realEstateProject(): BelongsTo
    {
        return $this->createComplexRelationship('realEstateProject');
    }
}
