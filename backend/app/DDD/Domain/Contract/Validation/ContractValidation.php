<?php

declare(strict_types=1);

namespace App\DDD\Domain\Contract\Validation;

use App\DDD\Application\Contract\Schema\ContractSchema;
use App\DDD\Domain\Contract\Interfaces\ContractValidationInterface;
use App\DDD\Infrastructure\Shared\Abstracts\Validators\AbstractValidator;

class ContractValidation extends AbstractValidator implements ContractValidationInterface
{
    public function getSchemaClass(): string
    {
        return ContractSchema::class;
    }
}
