<?php

declare(strict_types=1);

namespace App\DDD\Domain\WorkOrder\Entities;

use App\DDD\Application\WorkOrder\Schema\WorkOrderSchema;
use App\DDD\Infrastructure\Shared\Abstracts\Entities\AbstractEntities;
use Illuminate\Database\Eloquent\Factories\HasFactory;


class WorkOrder extends AbstractEntities
{
    use HasFactory;

    public static function getSchemaClass(): string
    {
        return WorkOrderSchema::class;
    }

    public function Case()
    {
        return $this->createComplexRelationship('case');
    }

    public function asset()
    {
        return $this->createComplexRelationship('asset');
    }

}
