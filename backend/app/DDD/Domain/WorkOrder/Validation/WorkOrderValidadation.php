<?php

declare(strict_types=1);

namespace App\DDD\Domain\WorkOrder\Validation;

use App\DDD\Application\WorkOrder\Schema\WorkOrderSchema;
use App\DDD\Domain\WorkOrder\Interfaces\WorkOrderValidationInterface;
use App\DDD\Infrastructure\Shared\Abstracts\Validators\AbstractValidator;

class WorkOrderValidadation extends AbstractValidator implements WorkOrderValidationInterface
{
    public function validate(array $data): bool
    {
        return true;
    }

    public function validateWorkOrder(array $data): bool
    {
        return true;
    }

    public function getSchemaClass(): string
    {
        return WorkOrderSchema::class;
    }
}