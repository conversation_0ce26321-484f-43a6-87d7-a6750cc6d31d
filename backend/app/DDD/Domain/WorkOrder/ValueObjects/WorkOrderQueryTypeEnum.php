<?php

declare(strict_types=1);

namespace App\DDD\Domain\WorkOrder\ValueObjects;

use App\DDD\Infrastructure\WorkOrder\Persistence\Queries\GetWorkOrderByDate;
use App\DDD\Infrastructure\WorkOrder\Persistence\Queries\GetWorkOrderByRealEstateProject;

enum WorkOrderQueryTypeEnum: string
{
    case GET_WORK_ORDERS = 'GET_WORK_ORDERS';
    case GET_WORK_ORDER_DATE = 'GET_WORK_ORDER_DATE';
    /**
     * Get the description for the query type
     */
    public function getDescription(): string
    {
        return match ($this) {
            self::GET_WORK_ORDERS => 'Pega as ordens de serviço',
            self::GET_WORK_ORDER_DATE => 'Pega as datas das ordens de serviço',
        };
    }

    /**
     * Get the strategy class for this query type
     */
    public function getStrategyClass(): string
    {
        return match ($this) {
            self::GET_WORK_ORDERS => GetWorkOrderByRealEstateProject::class,
            self::GET_WORK_ORDER_DATE => GetWorkOrderByDate::class,
        };
    }

    /**
     * Check if the query type requires authentication
     */
    public function requiresAuthentication(): bool
    {
        return match ($this) {
            self::GET_WORK_ORDERS => false,
            self::GET_WORK_ORDER_DATE => false,
        };
    }
}
