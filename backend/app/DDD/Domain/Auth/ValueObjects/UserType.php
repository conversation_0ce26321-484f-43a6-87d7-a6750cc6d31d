<?php

declare(strict_types=1);

namespace App\DDD\Domain\Auth\ValueObjects;

enum UserType: string
{
    case CLIENTE = 'cliente';
    // - cliente padrao. Deve ter nos dados os campos membro_de_patriminio e Sindico__c false.
    case SINDICO_CLIENTE = 'sindico_cliente';
    // - Sindico que tambem e cliente. Deve ter os campos Sindico__c com um ID de empreendimmento e membro_de_patrimonio false. O empreendimento deve ter o campo Sindico__c com o ID do AccountId do sindico.
    case SINDICO_PROFISSIONAL = 'sindico_profissional';
    // - Sindico que nao e cliente. Deve ter os campos Sindico__c com um ID de empreendimmento, membro_de_patrimonio null ou false e não está atrelado a um ativo.
    case MEMBRO_PATRIMONIO = 'membro_patrimonio';
    // - Cliente que e membro de patrimonio. Deve ter os campos Sindico__c true e membro_de_patrimonio true.
    case PERMUTANTE = 'permutante';
    // regra ainda não definida.
}
