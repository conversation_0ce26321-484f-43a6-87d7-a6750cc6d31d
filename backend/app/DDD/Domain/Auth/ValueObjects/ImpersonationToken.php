<?php

namespace App\DDD\Domain\Auth\ValueObjects;

use Carbon\Carbon;

class ImpersonationToken
{
    private string $token;
    private int $adminId;
    private int $userId;
    private Carbon $expiresAt;

    public function __construct(string $token, int $adminId, int $userId, Carbon $expiresAt)
    {
        $this->token = $token;
        $this->adminId = $adminId;
        $this->userId = $userId;
        $this->expiresAt = $expiresAt;
    }

    public function getToken(): string
    {
        return $this->token;
    }

    public function getAdminId(): int
    {
        return $this->adminId;
    }

    public function getUserId(): int
    {
        return $this->userId;
    }

    public function getExpiresAt(): Carbon
    {
        return $this->expiresAt;
    }

    public function isExpired(): bool
    {
        return $this->expiresAt->isPast();
    }
}