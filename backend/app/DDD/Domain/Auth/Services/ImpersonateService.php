<?php

namespace App\DDD\Domain\Auth\Services;


use App\DDD\Domain\Auth\Interfaces\ImpersonationServiceInterface;
use App\DDD\Domain\Auth\ValueObjects\ImpersonationToken;
use App\DDD\Domain\User\Entities\User;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class ImpersonateService implements ImpersonationServiceInterface
{
    public function createImpersonationToken(int $adminId, int $userId): ImpersonationToken
    {
        $user = User::where('id', $userId)->first();

        $token = $user->createToken('temp_token_'.$user->AccountId, ['*'], now()->addWeek());

        $expiresAt = Carbon::now()->addHours(2);

        return new ImpersonationToken($token->plainTextToken, $adminId, $userId, $expiresAt);
    }

}
