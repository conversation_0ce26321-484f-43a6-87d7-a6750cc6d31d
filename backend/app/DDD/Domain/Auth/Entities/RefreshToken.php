<?php

declare(strict_types=1);

namespace App\DDD\Domain\Auth\Entities;

use DateTime;
use Illuminate\Database\Eloquent\Model;

class RefreshToken extends Model
{
    protected $fillable = [
        'user_id',
        'token',
        'device_info',
        'expires_at',
        'created_at',
        'updated_at',
    ];

    private ?DateTime $updatedAt;

    public function __construct(
        int $userId,
        string $token,
        DateTime $expiresAt,
        ?string $deviceInfo = null,
        ?DateTime $createdAt = null,
        ?DateTime $updatedAt = null
    ) {
        $this->userId = $userId;
        $this->token = $token;
        $this->deviceInfo = $deviceInfo;
        $this->expiresAt = $expiresAt;
        $this->createdAt = $createdAt ?? new DateTime();
        $this->updatedAt = $updatedAt;
    }

    public function getUserId(): int
    {
        return $this->userId;
    }

    public function getToken(): string
    {
        return $this->token;
    }

    public function getDeviceInfo(): ?string
    {
        return $this->deviceInfo;
    }

    public function getExpiresAt(): DateTime
    {
        return $this->expiresAt;
    }

    public function isExpired(): bool
    {
        return $this->expiresAt < new DateTime();
    }

    public function setExpiresAt(DateTime $expiresAt): void
    {
        $this->expiresAt = $expiresAt;
        $this->updatedAt = new DateTime();
    }

    public function getCreatedAt(): DateTime
    {
        return $this->createdAt;
    }

    public function getUpdatedAt(): ?DateTime
    {
        return $this->updatedAt;
    }
}
