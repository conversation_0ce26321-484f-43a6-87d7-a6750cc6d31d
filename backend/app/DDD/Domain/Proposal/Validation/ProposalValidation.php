<?php

declare(strict_types=1);

namespace App\DDD\Domain\Proposal\Validation;

use App\DDD\Application\Proposal\Schema\ProposalSchema;
use App\DDD\Domain\Proposal\Interfaces\ProposalValidationInterface;
use App\DDD\Infrastructure\Proposal\Exceptions\ProposalSalesforceException;
use App\DDD\Infrastructure\Shared\Abstracts\Validators\AbstractValidator;

class ProposalValidation extends AbstractValidator implements ProposalValidationInterface
{
    public function getSchemaClass(): string
    {
        return ProposalSchema::class;
    }

    public function isValidateSalesforceData(array &$data): bool
    {
        $cpf = data_get($data, 'Conta__r.CPF__c');
        $cnpj = data_get($data, 'Conta__r.CNPJ__c');
        if ((bool) $cpf && (bool) $cnpj) {
            $message = 'CPF ou CNPJ é obrigatório: CPF:'.$data['Conta__r']['CPF__c'].' CNPJ:'.$data['Conta__r']['CNPJ__c'];

            throw new ProposalSalesforceException($message);
        }

        return parent::isValidateSalesforceData($data);
    }
}
