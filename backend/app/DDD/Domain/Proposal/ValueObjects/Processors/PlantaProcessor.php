<?php

declare(strict_types=1);

namespace App\DDD\Domain\Proposal\ValueObjects\Processors;

use App\DDD\Domain\Document\Interfaces\DocumentValidationInterface;
use App\DDD\Domain\Proposal\Interfaces\PlantaProcessorInterface;
use App\DDD\Infrastructure\Document\Interfaces\DocumentProcessorInterface;
use App\DDD\Infrastructure\Shared\Abstracts\Processors\AbstractProcessor;
use App\DDD\Infrastructure\Shared\Services\ServiceLocator;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;

/**
 * Processador para plantas de propostas
 */
class PlantaProcessor extends AbstractProcessor implements PlantaProcessorInterface
{
    /**
     * @var DocumentProcessorInterface Processador de documentos
     */
    private readonly DocumentProcessorInterface $documentProcessor;

    /**
     * @var bool Indica se deve usar fila
     */
    private bool $useQueue = false;

    /**
     * @var bool Indica se deve criar imagens
     */
    private bool $createImage = true;

    /**
     * Construtor
     *
     * @param  DocumentValidationInterface  $documentValidator  Validador de documentos
     * @param  DocumentProcessorInterface  $documentProcessor  Processador de documentos
     * @param  ServiceLocator|null  $serviceLocator  Localizador de serviços (opcional)
     */
    public function __construct(
        private readonly DocumentValidationInterface $documentValidator,
        DocumentProcessorInterface $documentProcessor,
        ?ServiceLocator $serviceLocator = null
    ) {

        parent::__construct($documentValidator, $serviceLocator);
        $this->documentProcessor = $documentProcessor;

        // Registra o processador de documentos no localizador
        if ($serviceLocator) {
            $serviceLocator->register('documentsProcessor', $documentProcessor);
        }
    }

    /**
     * @inheritdoc
     */
    public function getSchemaClass(): string
    {
        // Esta implementação não precisa de um schema específico
        return '';
    }

    /**
     * @inheritdoc
     */
    public function setUseQueue(bool $useQueue): self
    {
        $this->useQueue = $useQueue;

        return $this;
    }

    /**
     * @inheritdoc
     */
    public function setCreateImage(bool $createImage): self
    {
        $this->createImage = $createImage;

        return $this;
    }

    /**
     * @inheritdoc
     */
    public function process(array $data): Collection
    {
        try {
            // Prepara os dados para processamento
            $dataPlanta = [
                'attributes' => ['type' => 'Planta__c'],
                'Id' => data_get($data, 'Planta__c'),
            ];

            // Se o ID da planta não estiver disponível, retorna uma coleção vazia
            if (empty($dataPlanta['Id'])) {
                Log::info('ID da planta não encontrado nos dados');

                return collect([]);
            }

            // Configura o processador de documentos
            $this->documentProcessor->setUseQueue($this->useQueue);
            $this->documentProcessor->setCreateImage($this->createImage);

            // Processa os documentos da planta
            $processedDocs = $this->documentProcessor->process($dataPlanta);

            return collect($processedDocs);
        } catch (\Exception $e) {
            Log::error('Erro ao processar planta: '.$e->getMessage(), [
                'exception' => $e,
                'data' => $data,
            ]);

            return collect([]);
        }
    }

    /**
     * @inheritdoc
     */
    public function parseData(array $data): array
    {
        // Não há processamento específico neste método para PlantaProcessor
        return [];
    }

    /**
     * Verifica se um documento de planta é uma imagem válida
     * Método comentado mantido para referência futura
     *
     * @param  array  $plantaData  Dados da planta
     * @return bool Se contém uma imagem válida
     */
    /*
    private function checkIfIsImage(array $plantaData): bool
    {
        if (empty($plantaData)) {
            return false;
        }

        foreach ($plantaData as $item) {
            if (!isset($item['Link__c'])) {
                continue;
            }

            $fullUrl = config('services.s3.url') . $item['Link__c'] . '.webp';
            if ($this->documentProcessor->isValidImage($fullUrl)) {
                return true;
            }
        }

        return false;
    }
    */
}
