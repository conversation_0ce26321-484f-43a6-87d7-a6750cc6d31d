<?php

declare(strict_types=1);

namespace App\DDD\Domain\Proposal\Interfaces;

use App\DDD\Infrastructure\Shared\Interfaces\Processors\ProcessorsInterface;
use Illuminate\Support\Collection;

/**
 * Interface para processadores de plantas
 */
interface PlantaProcessorInterface extends ProcessorsInterface
{
    /**
     * Define se deve usar fila ao processar documentos de planta
     *
     * @param  bool  $useQueue  Indica se deve usar fila
     */
    public function setUseQueue(bool $useQueue): self;

    /**
     * Define se deve criar imagens dos documentos de planta
     *
     * @param  bool  $createImage  Indica se deve criar imagens
     */
    public function setCreateImage(bool $createImage): self;

    /**
     * Processa dados de planta
     *
     * @param  array  $data  Dados a serem processados
     * @return Collection Coleção de documentos de planta processados
     */
    public function process(array $data): Collection;
}
