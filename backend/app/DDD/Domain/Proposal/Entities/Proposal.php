<?php

declare(strict_types=1);

namespace App\DDD\Domain\Proposal\Entities;

use App\DDD\Application\Proposal\Schema\ProposalSchema;
use App\DDD\Infrastructure\Shared\Abstracts\Entities\AbstractEntities;
use App\DDD\Infrastructure\Shared\Interfaces\Entities\EntitiesInterface;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Proposal extends AbstractEntities implements EntitiesInterface
{
    use HasFactory;

    public static function getSchemaClass(): string
    {
        return ProposalSchema::class;
    }

    public function user(): BelongsTo
    {
        return $this->createComplexRelationship('user');
    }

    public function contracts(): BelongsTo
    {
        return $this->createComplexRelationship('contracts');
    }

    // public function proposal(): BelongsTo
    // {
    //     return $this->createComplexRelationship('proposal');
    // }

    public function assets(): BelongsTo
    {
        return $this->createComplexRelationship('assets');
    }

    public function documents(): HasMany
    {
        return $this->createComplexRelationship('documents');
    }
    // {
    // {
    // {
    // {
    // {
    // {

    //     $documentsAccount = $this->hasMany(Document::class, 'AccountId', 'AccountId')
    //         ->orderBy('TituloArquivo__c', 'asc')
    //         ->get();

    //     $documentsContract = $this->hasMany(Document::class, 'ContractId', 'ContractId')
    //         ->orderBy('TituloArquivo__c', 'asc')
    //         ->get();

    //     $documents = count($documentsAccount) > 0 ? $documentsAccount->merge($documentsContract) : $documentsContract;

    //     $documentsProposal = $this->createComplexRelationship('documents')->get();

    //     return $documents->merge($documentsProposal);
    // }

    public function realEstateProject(): BelongsTo
    {
        return $this->createComplexRelationship('realEstateProject');
    }
}
