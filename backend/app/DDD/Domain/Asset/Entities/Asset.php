<?php

declare(strict_types=1);

namespace App\DDD\Domain\Asset\Entities;

use App\DDD\Application\Asset\Schema\AssetSchema;
use App\DDD\Infrastructure\Shared\Abstracts\Entities\AbstractEntities;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Asset extends AbstractEntities
{
    use HasFactory;

    public static function getSchemaClass(): string
    {
        return AssetSchema::class;
    }

    public function user(): BelongsTo
    {
        return $this->createComplexRelationship('user');
    }

    public function contract(): BelongsTo
    {
        return $this->createComplexRelationship('contract');
    }

    public function proposal(): BelongsTo
    {
        return $this->createComplexRelationship('proposal');
    }

    public function realEstateProject(): BelongsTo
    {
        return $this->createComplexRelationship('realEstateProject');
    }
}
