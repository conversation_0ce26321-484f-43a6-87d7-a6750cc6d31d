<?php

declare(strict_types=1);

namespace App\DDD\Domain\Asset\Validation;

use App\DDD\Application\Asset\Schema\AssetSchema;
use App\DDD\Domain\Asset\Interfaces\AssetValidationInterface;
use App\DDD\Infrastructure\Shared\Abstracts\Validators\AbstractValidator;

class AssetValidation extends AbstractValidator implements AssetValidationInterface
{
    public function getSchemaClass(): string
    {
        return AssetSchema::class;
    }
}
