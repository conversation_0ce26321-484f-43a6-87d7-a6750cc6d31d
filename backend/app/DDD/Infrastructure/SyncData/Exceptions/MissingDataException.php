<?php

declare(strict_types=1);

namespace App\DDD\Infrastructure\SyncData\Exceptions;

use PHPUnit\Event\Code\Throwable;

class MissingDataException extends SyncDataException
{
    protected array $missingFields;

    protected ?array $data;

    protected int $attemptCount;

    public function __construct(
        array $missingFields,
        ?array $data = null,
        int $attemptCount = 1,
        string $message = '',
        int $code = 0,
        ?Throwable $previous = null,
        array $errors = [],
        array $context = []
    ) {
        $message = $message ?: $this->buildMessage($missingFields);
        $context = array_merge($context, [
            'missing_fields' => $missingFields,
            'data' => $data,
            'attempt_count' => $attemptCount,
        ]);

        parent::__construct($message, $code, $previous, $errors, $context);

        $this->missingFields = $missingFields;
        $this->data = $data;
        $this->attemptCount = $attemptCount;
    }

    private function buildMessage(array $missingFields): string
    {
        return sprintf(
            'Dados obrigatórios ausentes: %s',
            implode(', ', $missingFields)
        );
    }

    public function getMissingFields(): array
    {
        return $this->missingFields;
    }

    public function getData(): ?array
    {
        return $this->data;
    }

    public function getAttemptCount(): int
    {
        return $this->attemptCount;
    }

    public function shouldRetry(): bool
    {
        return $this->attemptCount < 3; // Máximo de 3 tentativas
    }
}
