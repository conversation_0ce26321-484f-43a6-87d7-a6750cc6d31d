<?php

declare(strict_types=1);

namespace App\DDD\Infrastructure\SyncData\Providers;

use App\DDD\Infrastructure\Asset\Providers\Config\AssetBindings;
use App\DDD\Infrastructure\Contract\Providers\Config\ContractBindings;
use App\DDD\Infrastructure\Document\Providers\Config\DocumentsBindings;
use App\DDD\Infrastructure\External\Salesforce\Providers\Config\SalesforceBindings;
use App\DDD\Infrastructure\External\Sienge\Providers\Config\SiengeBindings;
use App\DDD\Infrastructure\Installment\Providers\Config\InstallmentsBindings;
use App\DDD\Infrastructure\Proposal\Providers\Config\ProposalBindings;
use App\DDD\Infrastructure\RealEstateProject\Providers\Config\RealEstateProjectBindings;
use App\DDD\Infrastructure\Schedule\Providers\Config\ScheduleBindings;
use App\DDD\Infrastructure\ScheduleServicesOptions\Providers\Config\ScheduleServicesOptionsBindings;
use App\DDD\Infrastructure\Shared\Notifications\Providers\NotificationBinding;
use App\DDD\Infrastructure\SyncData\Providers\Config\SyncDataBindings;
use App\DDD\Infrastructure\User\Providers\Config\UserBindings;
use Illuminate\Contracts\Foundation\Application;

class ServiceBindings
{
    /**
     * Array com todas as classes de binding do sistema
     */
    protected array $bindings = [
        // InstallmentsBindings::class,
        // NotificationBinding::class,
        // SalesforceBindings::class,
        // DocumentsBindings::class,
        // SiengeBindings::class,
        // RealEstateProjectBindings::class,
        // ScheduleBindings::class,
        // ProposalBindings::class,
        // UserBindings::class,
        // ContractBindings::class,
        // AssetBindings::class,
        // ScheduleServicesOptionsBindings::class,
        SyncDataBindings::class,

    ];

    /**
     * Registra todas as bindings do sistema
     */
    public function register(Application $app): void
    {
        foreach ($this->bindings as $binding) {
            $this->registerBinding($app, $binding);
        }
    }

    /**
     * Registra uma binding específica
     */
    private function registerBinding(Application $app, string $bindingClass): void
    {
        if (class_exists($bindingClass)) {
            (new $bindingClass())->register($app);
        }
    }
}
