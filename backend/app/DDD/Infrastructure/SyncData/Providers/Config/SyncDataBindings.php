<?php

declare(strict_types=1);

namespace App\DDD\Infrastructure\SyncData\Providers\Config;

use App\DDD\Application\SyncData\Interfaces\DataSyncServiceTransformInterface;
use App\DDD\Application\SyncData\Interfaces\SyncDataPersistenceServiceInterface;
use App\DDD\Application\SyncData\Interfaces\SyncDataProposalBySignatureServiceInterface;
use App\DDD\Application\SyncData\Interfaces\WebhookDataSyncServiceTransformInterface;
use App\DDD\Application\SyncData\Interfaces\WebhookSyncDataProposalBySignatureServiceInterface;
use App\DDD\Application\SyncData\Services\DataSyncServiceTransform;
use App\DDD\Application\SyncData\Services\SyncDataProposalBySignatureService;
use App\DDD\Application\SyncData\Services\WebhookDataSyncServiceTransform;
use App\DDD\Application\SyncData\Services\WebhookSyncDataProposalBySignatureService;
use App\DDD\Infrastructure\Shared\Abstracts\Bindings\AbstractBindings;
use App\DDD\Infrastructure\Shared\Factories\ServiceFactory;
use App\DDD\Infrastructure\Shared\Interfaces\Bindings\BindingsInterface;
use App\DDD\Infrastructure\Shared\Interfaces\Factories\ServiceFactoryInterface;

use App\DDD\Infrastructure\SyncData\Persistence\Services\SyncDataPersistenceService;
use App\DDD\Infrastructure\SyncData\Validators\SyncDataServiceValidation;
use App\DDD\Infrastructure\SyncData\Validators\WebhookSyncDataServiceValidation;
use Illuminate\Contracts\Foundation\Application;

class SyncDataBindings extends AbstractBindings implements BindingsInterface
{
    public function registerServices(Application $app): void
    {
        $app->singleton(
            DataSyncServiceTransformInterface::class,
            DataSyncServiceTransform::class
        );

        $app->bind(
            SyncDataProposalBySignatureServiceInterface::class,
            function ($app) {
                return new SyncDataProposalBySignatureService(
                    $app->make(SyncDataServiceValidation::class),
                    $app->make(SyncDataPersistenceServiceInterface::class),
                    $app->make(DataSyncServiceTransform::class)
                );
            }
        );

        $app->singleton(SyncDataPersistenceServiceInterface::class, function ($app) {
            return new SyncDataPersistenceService(
                $app->make(ServiceFactoryInterface::class),
            );
        });
        $app->singleton(ServiceFactoryInterface::class, function ($app) {
            return new ServiceFactory($app);
        });

        $app->singleton(
            WebhookDataSyncServiceTransformInterface::class,
            WebhookDataSyncServiceTransform::class
        );

        $app->bind(
            WebhookSyncDataProposalBySignatureServiceInterface::class,
            function ($app) {
                return new WebhookSyncDataProposalBySignatureService(
                    $app->make(WebhookSyncDataServiceValidation::class),
                    $app->make(SyncDataPersistenceServiceInterface::class),
                    $app->make(WebhookDataSyncServiceTransform::class)
                );
            }
        );

    }
}
