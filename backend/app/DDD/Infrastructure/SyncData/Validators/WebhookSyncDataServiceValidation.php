<?php

declare(strict_types=1);

namespace App\DDD\Infrastructure\SyncData\Validators;

use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\ValidationException;

class WebhookSyncDataServiceValidation
{
    public function validate(array $params): bool
    {
        $validator = Validator::make([], []);
        if (! isset($params['records'])) {
            $validator->errors()->add('salesforceData', 'Missing records in Salesforce response');
        }

        if (empty($params['records'])) {
            $validator->errors()->add('salesforceData', 'No records found in Salesforce response');
        }

        if ($validator->fails()) {
            throw new ValidationException($validator);
        }

        return true;
    }

    public function isValidateRulesDatabase(array $data): bool
    {
        return true;
    }

    public function isValidateSalesforceData(array $data): bool
    {
        return true;
    }

    public function rules_database(): array
    {
        return [];
    }

    public function rules_salesforce(): array
    {
        return [];
    }

    public function messages_database(): string
    {
        return '';
    }
}
