<?php

declare(strict_types=1);

namespace App\DDD\Infrastructure\SyncData\Persistence\Services;

use App\DDD\Application\SyncData\Enums\DataSyncServicesTypeEnum;
use App\DDD\Application\SyncData\Interfaces\SyncDataPersistenceServiceInterface;
use App\DDD\Infrastructure\Shared\Abstracts\Persistence\AbstractPersistenceService;
use App\DDD\Infrastructure\Shared\Interfaces\Persistence\PersistenceServiceInterface;

class SyncDataPersistenceService extends AbstractPersistenceService implements SyncDataPersistenceServiceInterface
{
    protected function getServiceTypeEnum(): string
    {
        return DataSyncServicesTypeEnum::class;
    }
}
