<?php

declare(strict_types=1);

namespace App\DDD\Infrastructure\SyncData\Persistence\Factory;

use App\DDD\Domain\SyncData\ValueObjects\SyncDataQueryTypeEnum;
use App\DDD\Infrastructure\Shared\Abstracts\Factories\AbstractQueryFactory;
use App\DDD\Infrastructure\Shared\Interfaces\Queries\QueryFactoryInterface;
use App\DDD\Infrastructure\SyncData\Persistence\Queries\Strategies\SyncProposalBySignatureQuery;

class SyncDataQueryFactory extends AbstractQueryFactory implements QueryFactoryInterface
{
    public function create($type): SyncProposalBySignatureQuery
    {
        return match ($type) {
            SyncDataQueryTypeEnum::SYNC_PROPOSAL_BY_SIGNATURE => new SyncProposalBySignatureQuery(),
            default => throw new \InvalidArgumentException('Invalid query type'),
        };
    }
}
