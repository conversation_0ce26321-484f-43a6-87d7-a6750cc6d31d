<?php

declare(strict_types=1);

namespace App\DDD\Infrastructure\External\Webook\Validation;

use App\DDD\Infrastructure\External\Salesforce\Interfaces\SalesforceValidationInterface;

class SalesforceWebhookValidation implements SalesforceValidationInterface
{
    private const REQUIRED_FIELDS = [

    ];

    public function isValidWebhook(array $payload): bool
    {
        if (! $this->hasRequiredFields($payload)) {
            return false;
        }

        if ($payload['source'] !== 'salesforce') {
            return false;
        }

        return $this->isValidEventType($payload['event_type']);
    }

    private function hasRequiredFields(array $payload): bool
    {
        return ! empty(array_intersect_key(array_flip(self::REQUIRED_FIELDS), $payload));
    }

    private function isValidEventType(string $eventType): bool
    {
        return in_array($eventType, ['create', 'update', 'delete']);
    }

    public function isValidRecords(array $data): bool
    {
        if (! isset($data['records']) || empty($data['records'])) {
            throw new SalesforceValidationException('No records found in Salesforce response');
        }

        return true;
    }

    public function isValidateRulesDatabase(array $data): bool
    {
        return true;
    }

    public function isValidateSalesforceData(array $data): bool
    {
        return true;
    }
}
