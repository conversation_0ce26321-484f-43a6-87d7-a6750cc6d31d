<?php

declare(strict_types=1);

namespace App\DDD\Infrastructure\External\Sienge\Factories;

// use App\DDD\Infrastructure\External\Sienge\Strategies\ApexExecutorStrategyInfra;
// use App\DDD\Infrastructure\External\Sienge\Strategies\DeleteExecutorStrategyInfra;
// use App\DDD\Infrastructure\External\Sienge\Strategies\FileExecutorStrategyInfra;
// use App\DDD\Infrastructure\External\Sienge\Strategies\InsertExecutorStrategyInfra;
use App\DDD\Infrastructure\External\Sienge\Strategies\DownloadExecutorStrategyInfra;
use App\DDD\Infrastructure\External\Sienge\Strategies\QueryExecutorStrategyInfra;
// use App\DDD\Infrastructure\External\Sienge\Strategies\UpdateExecutorStrategyInfra;
use App\DDD\Infrastructure\Shared\Interfaces\Strategies\ExecutorStrategyInterface;
use InvalidArgumentException;

class ExecutorStrategyInfraFactory
{
    public function make(string $type): ExecutorStrategyInterface
    {
        return match ($type) {
            'query' => new QueryExecutorStrategyInfra(),
            'download' => new DownloadExecutorStrategyInfra(),
            // 'apex' => new ApexExecutorStrategyInfra(),
            // 'insert' => new InsertExecutorStrategyInfra(),
            // 'update' => new UpdateExecutorStrategyInfra(),
            // 'delete' => new DeleteExecutorStrategyInfra(),
            // 'file' => new FileExecutorStrategyInfra(),
            default => throw new InvalidArgumentException('Invalid executor type'),
        };
    }
}
