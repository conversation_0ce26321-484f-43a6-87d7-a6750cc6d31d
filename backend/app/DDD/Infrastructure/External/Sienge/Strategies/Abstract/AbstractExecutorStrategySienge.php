<?php

declare(strict_types=1);

namespace App\DDD\Infrastructure\External\Sienge\Strategies\Abstract;

use App\DDD\Infrastructure\External\Sienge\Interfaces\SiengeAuthInterface;
use App\DDD\Infrastructure\External\Sienge\Interfaces\SiengeServiceInterface;
use App\DDD\Infrastructure\Shared\Interfaces\Strategies\ExecutorStrategyInterface;

class AbstractExecutorStrategySienge implements ExecutorStrategyInterface
{
    public SiengeServiceInterface $serviceSienge;

    public SiengeAuthInterface $authSienge;

    public function __construct()
    {
        $this->serviceSienge = app(SiengeServiceInterface::class);
        $this->authSienge = app(SiengeAuthInterface::class);
    }

    public function auth(): SiengeAuthInterface
    {
        return $this->authSienge;
    }

    public function setExecutor(string $type): void
    {
        $this->serviceSienge->setExecutor($type);
    }

    public function getService(): SiengeServiceInterface
    {
        return $this->serviceSienge;
    }

    public function execute(array $params): array
    {
        return $this->serviceSienge->execute($params);
    }
}
