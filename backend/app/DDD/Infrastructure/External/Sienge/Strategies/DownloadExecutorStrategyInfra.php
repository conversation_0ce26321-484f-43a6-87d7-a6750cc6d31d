<?php

declare(strict_types=1);

namespace App\DDD\Infrastructure\External\Sienge\Strategies;

use App\DDD\Infrastructure\External\Sienge\Strategies\Abstract\AbstractExecutorStrategySienge;
use App\DDD\Infrastructure\Shared\Interfaces\Strategies\ExecutorStrategyInterface;
use Illuminate\Support\Facades\Http;

class DownloadExecutorStrategyInfra extends AbstractExecutorStrategySienge implements ExecutorStrategyInterface
{
    /**
     * Execute a SOQL query on Sienge
     *
     * @param  array  $params  {
     *
     * @type string $query    The SOQL query to execute
     *              }
     *
     * @return array Response from Sienge API containing query results
     */
    public function execute(array $params): array
    {
        $url = "{$this->auth()->getEndpoint()}/{$params['query']}";
        $response = Http::withHeaders(['Authorization' => 'Basic '.$this->auth()->getToken()])
            ->timeout(600)
            ->connectTimeout(600)
            ->get($url);

        if ($response->successful()) {
            return [
                'data' => $response->body(),
                'status' => true,
            ];
        }

        return [
            'error' => $response->json(),
            'status' => false,
        ];
    }
}
