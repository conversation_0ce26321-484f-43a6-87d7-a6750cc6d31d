<?php

declare(strict_types=1);

namespace App\DDD\Infrastructure\External\Sienge\Providers;

use App\DDD\Infrastructure\External\Sienge\Providers\Config\SiengeBindings;
use App\Providers\ApiServicesProviders\BaseApiDDDServiceProvider;

class SiengeInfraProvider extends BaseApiDDDServiceProvider
{
    public function register(): void
    {
        parent::register();
        (new SiengeBindings())->register($this->app);
    }
}
