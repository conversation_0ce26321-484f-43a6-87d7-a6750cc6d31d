<?php

declare(strict_types=1);

namespace App\DDD\Infrastructure\External\Sienge\Providers\Config;

use App\DDD\Infrastructure\External\Sienge\Factories\ExecutorStrategyInfraFactory;
use App\DDD\Infrastructure\External\Sienge\Interfaces\SiengeAuthInterface;
use App\DDD\Infrastructure\External\Sienge\Interfaces\SiengeServiceInterface;
use App\DDD\Infrastructure\External\Sienge\Services\SiengeAuthService;
use App\DDD\Infrastructure\External\Sienge\Services\SiengeService;
use App\DDD\Infrastructure\External\Sienge\Strategies\Abstract\AbstractExecutorStrategySienge;
use App\DDD\Infrastructure\Shared\Abstracts\Bindings\AbstractBindings;
use App\DDD\Infrastructure\Shared\Interfaces\Bindings\BindingsInterface;
use App\DDD\Infrastructure\Shared\Interfaces\Strategies\ExecutorStrategyInterface;
use Illuminate\Contracts\Foundation\Application;

class SiengeBindings extends AbstractBindings implements BindingsInterface
{
    public function registerServices(Application $app): void
    {
        $app->singleton(
            SiengeAuthInterface::class,
            function ($app) {
                return new SiengeAuthService();
            }
        );
        $app->singleton(
            SiengeServiceInterface::class,
            function ($app) {
                return new SiengeService();
            }
        );
    }

    public function registerStrategies(Application $app): void
    {
        $app->singleton(
            ExecutorStrategyInterface::class,
            AbstractExecutorStrategySienge::class
        );

        $app->singleton(
            ExecutorStrategyInfraFactory::class,
            function ($app) {
                return new ExecutorStrategyInfraFactory();
            }
        );
    }
}
