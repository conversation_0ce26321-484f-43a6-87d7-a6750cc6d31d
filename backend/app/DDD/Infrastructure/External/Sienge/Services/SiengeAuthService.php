<?php

declare(strict_types=1);

namespace App\DDD\Infrastructure\External\Sienge\Services;

use App\DDD\Infrastructure\External\Sienge\Exceptions\SiengeAuthenticationException;
use App\DDD\Infrastructure\External\Sienge\Interfaces\SiengeAuthInterface;
use Exception;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class SiengeAuthService implements SiengeAuthInterface
{
    private string $token;

    private string $endpoint;

    private string $endpointApex;

    public function __construct()
    {
        try {
            $this->authenticate();
        } catch (Exception $e) {
            Log::error('Error authenticating with <PERSON>eng<PERSON>', [
                'error' => $e->getMessage(),
            ]);

            throw new SiengeAuthenticationException('Failed to authenticate with Sienge', 500, $e->getMessage());
        }
    }

    private function authenticate(): void
    {
        $expiresAt = 60;
        $tokenKey = 'sienge_token';
        $endpointKey = 'sienge_endpoint';
        $token = Cache::get($tokenKey);
        $endpoint = Cache::get($endpointKey);

        if (! $token || ! $endpoint) {
            $username = config('services.sienge.username');
            $password = config('services.sienge.password');
            $token = base64_encode("{$username}:{$password}");
            $endpoint = config('services.sienge.base_url');
            $expiresAt = now()->addMinutes($expiresAt);
            Cache::put($tokenKey, $token, $expiresAt);
            Cache::put($endpointKey, $endpoint, $expiresAt);
        }

        $this->token = $token;
        $this->endpoint = $endpoint;
    }

    public function getToken(): string
    {
        return $this->token;
    }

    public function getEndpoint(): string
    {
        return $this->endpoint;
    }

    public function getApexEndpoint(): string
    {
        return $this->endpointApex;
    }
}
