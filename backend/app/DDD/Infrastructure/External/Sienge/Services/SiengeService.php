<?php

declare(strict_types=1);

namespace App\DDD\Infrastructure\External\Sienge\Services;

use App\DDD\Infrastructure\External\Sienge\Factories\ExecutorStrategyInfraFactory;
use App\DDD\Infrastructure\External\Sienge\Interfaces\SiengeAuthInterface;
use App\DDD\Infrastructure\External\Sienge\Interfaces\SiengeServiceInterface;
use App\DDD\Infrastructure\Shared\Interfaces\Strategies\ExecutorStrategyInterface;

class SiengeService  implements SiengeServiceInterface
{
    public ExecutorStrategyInterface $strategy;

    public function setExecutor(string $type): void
    {
        $factory = app(ExecutorStrategyInfraFactory::class);
        $this->strategy = $factory->make($type);
    }

    public function execute(array $params): array
    {
        return $this->strategy->execute($params);
    }

    public function authSienge(): SiengeAuthInterface
    {
        return app(SiengeAuthInterface::class);
    }
}
