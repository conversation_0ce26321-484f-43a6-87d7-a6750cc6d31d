<?php

declare(strict_types=1);

namespace App\DDD\Infrastructure\External\Salesforce\Providers;

use App\DDD\Infrastructure\External\Salesforce\Providers\Config\SalesforceBindings;
use App\Providers\ApiServicesProviders\BaseApiDDDServiceProvider;

class SalesforceInfraProvider extends BaseApiDDDServiceProvider
{
    public function register(): void
    {
        parent::register();
        (new SalesforceBindings())->register($this->app);
    }
}
