<?php

declare(strict_types=1);

namespace App\DDD\Infrastructure\External\Salesforce\Providers\Config;

use App\DDD\Infrastructure\External\Salesforce\Factories\ExecutorStrategyInfraFactory;
use App\DDD\Infrastructure\External\Salesforce\Interfaces\SalesforceAuthInterface;
use App\DDD\Infrastructure\External\Salesforce\Interfaces\SalesforceServiceInterface;
use App\DDD\Infrastructure\External\Salesforce\Services\SalesforceAuthService;
use App\DDD\Infrastructure\External\Salesforce\Services\SalesforceService;
use App\DDD\Infrastructure\External\Salesforce\Strategies\Abstract\AbstractExecutorStrategySalesforce;
use App\DDD\Infrastructure\External\Salesforce\Validation\SalesforceServiceValidation;
use App\DDD\Infrastructure\External\Webook\Validation\SalesforceWebhookValidation;
use App\DDD\Infrastructure\Shared\Abstracts\Bindings\AbstractBindings;
use App\DDD\Infrastructure\Shared\Interfaces\Bindings\BindingsInterface;
use App\DDD\Infrastructure\Shared\Interfaces\Strategies\ExecutorStrategyInterface;
use Illuminate\Contracts\Foundation\Application;

class SalesforceBindings extends AbstractBindings implements BindingsInterface
{
    public function registerServices(Application $app): void
    {
        $app->singleton(
            SalesforceServiceInterface::class,
            function ($app) {
                return new SalesforceService(
                    $app->make(ExecutorStrategyInfraFactory::class),
                    $app->make(SalesforceServiceValidation::class)
                );
            }
        );

        $app->singleton(
            SalesforceAuthInterface::class,
            SalesforceAuthService::class
        );
    }

    public function registerStrategies(Application $app): void
    {
        $app->singleton(
            ExecutorStrategyInterface::class,
            AbstractExecutorStrategySalesforce::class
        );

        $app->singleton(
            ExecutorStrategyInfraFactory::class,
            function ($app) {
                return new ExecutorStrategyInfraFactory();
            }
        );
    }

    public function registerValidators(Application $app): void
    {
        // Registra validadores
        $app->singleton(
            'salesforce.webhook.validator',
            SalesforceWebhookValidation::class
        );
    }
}
