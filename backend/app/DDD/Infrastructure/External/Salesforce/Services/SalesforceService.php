<?php

declare(strict_types=1);

namespace App\DDD\Infrastructure\External\Salesforce\Services;

use App\DDD\Infrastructure\External\Salesforce\Factories\ExecutorStrategyInfraFactory;
use App\DDD\Infrastructure\External\Salesforce\Interfaces\SalesforceServiceInterface;
use App\DDD\Infrastructure\External\Salesforce\Validation\SalesforceServiceValidation;
use App\DDD\Infrastructure\Shared\Abstracts\DTO\AbstractDto;
use App\DDD\Infrastructure\Shared\Interfaces\Processors\ProcessorsInterface;
use App\DDD\Infrastructure\Shared\Interfaces\Strategies\ExecutorStrategyInterface;
use App\DDD\Infrastructure\Shared\Interfaces\Validators\ValidatorDataInterface;
use App\DDD\Infrastructure\Shared\Utils\ArrayValuePopulator;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;

class SalesforceService implements SalesforceServiceInterface
{
    private ?ExecutorStrategyInterface $strategy = null;

    private ExecutorStrategyInfraFactory $factory;

    private SalesforceServiceValidation $salesforceServiceValidation;

    private string $schemaClass;

    private ?ValidatorDataInterface $validatorObject = null;

    private ?ProcessorsInterface $processorObject = null;

    public function __construct(
        ExecutorStrategyInfraFactory $factory,
        SalesforceServiceValidation $salesforceServiceValidation
    ) {
        $this->factory = $factory;
        $this->salesforceServiceValidation = $salesforceServiceValidation;
    }

    public function setSchemaClass(string $schemaClass): void
    {
        $this->schemaClass = $schemaClass;
    }

    public function setValidator(ValidatorDataInterface $validator): void
    {
        $this->validatorObject = $validator;
    }

    public function setProcessor(ProcessorsInterface $processor): void
    {
        $this->processorObject = $processor;
    }

    public function setExecutor(string $type): void
    {
        $this->strategy = $this->factory->make($type);
    }

    /**
     * Execute strategy with provided parameters
     *
     * @param  array  $params  Parameters to execute
     * @return array Result of execution
     *
     * @throws \RuntimeException When strategy is not set
     */
    public function execute(array $params): array
    {
        if ($this->strategy === null) {
            throw new \RuntimeException('Executor strategy not set. Call setExecutor() first.');
        }

        return $this->strategy->execute($params);
    }

    /**
     * Query a Salesforce object
     *
     * @param  string  $objectType  Type of Salesforce object
     * @param  array  $params  Query parameters containing 'queryType' and 'params'
     * @return array Query results
     */
    public function queryObject(string $objectType, array $params): array
    {
        $this->setExecutor($objectType);

        $queryType = $params['queryType'];
        $queryType->setParams($params['params']);

        $queryToExecute = ['query' => $queryType->execute()];

        return $this->execute($queryToExecute);
    }

    /**
     * Serviço responsável por realizar operações com o Salesforce.
     */
    public function validateSalesforceData(array $data): bool
    {
        return $this->salesforceServiceValidation->validate($data);
    }

    /**
     * Processa os dados vindos do Salesforce
     *
     * @param  array  $salesforceData  Dados do Salesforce
     * @return Model|Collection|array|null Modelo salvo, Collection ou array de modelos
     */
    public function processSalesforceData(array $salesforceData): Model|Collection|array|null|AbstractDto
    {

        $type = data_get($salesforceData, 'attributes.type');

        try {
            // Tenta validar os dados do Salesforce
            $this->validatorObject->isValidateSalesforceData($salesforceData);

            // Se após validação, os dados forem nulos, a validação falhou sem lançar exceção
            if ($salesforceData === null) {
                Log::info("Validação do Salesforce retornou dados nulos para tipo {$type}");

                return null;
            }

            // Processa e obtém o DTO ou Collection de DTOs
            $result = $this->processorObject->process($salesforceData);

            // Se o processador retornou Collection vazia, não há nada para salvar
            if ($result instanceof Collection && $result->isEmpty()) {
                Log::info("Processador retornou Collection vazia para tipo {$type}");

                return null;
            }

            // Verifica o tipo de resultado e processa adequadamente
            return $result;
        } catch (\Exception $e) {
            Log::warning("Erro na validação para tipo {$type}: ".$e->getMessage());

            // Tenta fazer retry com base no schema
            $retryData = $this->handleSalesforceRetry($salesforceData);
            // $type = data_get($retryData, 'attributes.type');
            $populator = new ArrayValuePopulator($this->schemaClass);
            $retryData = $populator->populateNullValues($salesforceData, $retryData);
            if (! empty($retryData)) {
                Log::info("Executando retry para tipo {$type}");

                return $this->processSalesforceData($retryData);
            }

            // Registra o erro
            Log::error("Falha no processamento dos dados para tipo {$type}", [
                'error' => $e->getMessage(),
                'data' => json_encode($salesforceData),
            ]);

            throw $e;
        }
    }

    /**
     * Processa dados em lote do Salesforce
     *
     * @param  array  $salesforceDataArray  Array de dados do Salesforce
     * @return array Array de modelos salvos
     */
    public function processBulkSalesforceData(array $salesforceDataArray): array
    {
        $results = [];
        foreach ($salesforceDataArray as $salesforceData) {
            try {
                $result = $this->processSalesforceData($salesforceData);
                if ($result) {
                    $results[] = $result;
                }
            } catch (\Exception $e) {
                Log::error('processBulkSalesforceData', ['error' => $e->getMessage()]);

                // Continua processando outros registros
                continue;
            }
        }

        return $results;
    }

    /**
     * Manipula a lógica de retry com base no schema
     *
     * @param  array  $salesforceData  Dados originais do Salesforce
     * @return array Novos dados do Salesforce após retry ou array vazio se falhar
     */
    protected function handleSalesforceRetry(array $salesforceData): array
    {
        // Obtém o tipo de objeto a partir dos dados
        $type = data_get($salesforceData, 'attributes.type');

        // Obtém a classe do Schema através do validador
        if (! $this->validatorObject || ! method_exists($this->validatorObject, 'getSchemaClass')) {
            Log::warning('Validator não implementa método getSchemaClass para retry');

            return [];
        }

        $this->schemaClass = $this->validatorObject->getSchemaClass();

        if (! class_exists($this->schemaClass)) {
            Log::warning("Classe do Schema {$this->schemaClass} não existe");

            return [];
        }

        // Verifica se o schema tem definido a constante RETRY_SALESFORCE
        if (! defined("{$this->schemaClass}::RETRY_SALESFORCE")) {
            Log::info("Schema {$this->schemaClass} não tem constante RETRY_SALESFORCE definida");

            return [];
        }

        // Obtém a configuração de retry para o tipo específico
        $retryConfig = $this->schemaClass::RETRY_SALESFORCE[$type] ?? null;

        if (empty($retryConfig)) {
            Log::info("Não existe configuração de retry para o tipo {$type} no schema: {$this->schemaClass}");

            return [];
        }

        // Constrói os parâmetros para a consulta
        $queryParams = [];
        foreach ($retryConfig as $dbField => $salesforceField) {
            $value = data_get($salesforceData, $salesforceField);

            if (! empty($value)) {
                $queryParams[$dbField] = $value;
            }
        }

        if (empty($queryParams)) {
            Log::warning('Parâmetros de query vazios para retry');

            return [];
        }

        // Obtém o tipo de objeto para o retry do schema
        $queryObjectType = $this->getQueryObjectTypeForRetry($this->schemaClass, $type);

        if (empty($queryObjectType)) {
            Log::warning("Tipo de objeto para retry não definido para {$type}");

            return [];
        }

        // Verifica se o queryObjectType é uma classe (para custom queries) ou um tipo direto do Salesforce

        try {
            // Instancia a classe de query
            $queryClass = new $queryObjectType($queryParams);
            $paramsQuery = [
                'params' => $queryParams,
                'queryType' => $queryClass,
            ];
            // Executa a query através do Salesforce Service
            $salesforceData = $this->queryObject('query', $paramsQuery);
            $this->validateSalesforceData($salesforceData);

            return $salesforceData['records'][0];
        } catch (\Exception $e) {
            Log::error('Erro ao executar query personalizada para retry: '.$e->getMessage());

            return [];
        }
    }

    /**
     * Determina o tipo de objeto Salesforce a ser consultado durante o retry com base no Schema
     *
     * @param  string  $this->schemaClass  Nome completo da classe do Schema
     * @param  string  $originalType  Tipo original do objeto Salesforce
     * @return string Tipo do objeto a ser consultado no retry
     */
    protected function getQueryObjectTypeForRetry(string $schemaClass, string $originalType): string
    {
        // Se não existir RETRY_OBJECT_TYPES no schema, usa o tipo original
        if (! defined("{$schemaClass}::RETRY_OBJECT_TYPES")) {
            // Verificamos se o tipo atual é uma chave em RETRY_SALESFORCE
            // Se não for ou se RETRY_SALESFORCE[$originalType] estiver vazio, retornamos o tipo original
            if (
                ! defined("{$this->schemaClass}::RETRY_SALESFORCE") ||
                ! isset($this->schemaClass::RETRY_SALESFORCE[$originalType]) ||
                empty($this->schemaClass::RETRY_SALESFORCE[$originalType])
            ) {
                return $originalType;
            }

            // Para outros tipos, mantemos o mesmo tipo
            return $originalType;
        }

        // Se existir RETRY_OBJECT_TYPES, usa o tipo especificado ou mantém o original
        return $this->schemaClass::RETRY_OBJECT_TYPES[$originalType] ?? $originalType;
    }
}
