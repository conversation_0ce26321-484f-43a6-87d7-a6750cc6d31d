<?php

declare(strict_types=1);

namespace App\DDD\Infrastructure\External\Salesforce\Services;

use App\DDD\Infrastructure\External\Salesforce\Exceptions\SalesforceAuthenticationException;
use App\DDD\Infrastructure\External\Salesforce\Interfaces\SalesforceAuthInterface;
use Exception;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class SalesforceAuthService implements SalesforceAuthInterface
{
    private ?string $token;

    private ?string $endpoint;

    private ?string $endpointApex;

    private string $tokenKey = 'salesforce_token';

    private string $endpointKey = 'salesforce_endpoint';

    private string $endpointapexKey = 'salesforce_endpointapex';

    private int $expiresAt = 60;

    private int $maxAttempts = 3;

    public function __construct()
    {
        $this->authenticate();
    }

    private function cleanCache(): void
    {
        Cache::forget($this->tokenKey);
        Cache::forget($this->endpointKey);
        Cache::forget($this->endpointapexKey);
    }

    private function authenticate(): void
    {
        $this->token = Cache::get($this->tokenKey);
        $this->endpoint = Cache::get($this->endpointKey);
        $this->endpointApex = Cache::get($this->endpointapexKey);

        if (! $this->token || ! $this->endpoint) {
            $attempts = 0;
            while ($attempts < $this->maxAttempts) {
                try {
                    $response = Http::asForm()
                        ->post(config('services.salesforce.base_url').'services/oauth2/token', [
                            'grant_type' => 'password',
                            'client_id' => config('services.salesforce.client_id'),
                            'client_secret' => config('services.salesforce.client_secret'),
                            'username' => config('services.salesforce.username'),
                            'password' => config('services.salesforce.password').config('services.salesforce.secret_token'),
                        ]);

                    if ($response->successful()) {
                        $data = $response->json();
                        $this->token = $data['access_token'];
                        $this->endpoint = $data['instance_url'].'/services/data/v'.config('services.salesforce.version');
                        $this->endpointApex = $data['instance_url'].'/services/apexrest/v1';
                        $expiresAt = now()->addMinutes($this->expiresAt);
                        Cache::put($this->tokenKey, $this->token, $expiresAt);
                        Cache::put($this->endpointKey, $this->endpoint, $expiresAt);
                        Cache::put($this->endpointapexKey, $this->endpointApex, $expiresAt);

                        break; // Sai do loop após uma tentativa bem-sucedida
                    } else {
                        Log::error('Salesforce authentication failed', [
                            'response' => $response->json(),
                            'status' => $response->status(),
                        ]);
                        $this->cleanCache();
                        $attempts++;

                        continue; // Tenta novamente
                    }
                } catch (Exception $e) {
                    Log::error('Error authenticating with Salesforce', [
                        'error' => $e->getMessage(),
                    ]);
                    $this->cleanCache();
                    $attempts++;
                    if ($attempts >= $this->maxAttempts) {
                        throw new SalesforceAuthenticationException('Failed to authenticate with Salesforce after '.$this->maxAttempts.' attempts', 500, $e->getMessage());
                    }
                }
            }
        }
    }

    public function getToken(): string
    {
        return $this->token;
    }

    public function getEndpoint(): string
    {
        return $this->endpoint;
    }

    public function getApexEndpoint(): string
    {
        return $this->endpointApex;
    }
}
