<?php

declare(strict_types=1);

namespace App\DDD\Infrastructure\External\Salesforce\Interfaces;

use App\DDD\Infrastructure\Shared\Abstracts\DTO\AbstractDto;
use App\DDD\Infrastructure\Shared\Interfaces\Processors\ProcessorsInterface;
use App\DDD\Infrastructure\Shared\Interfaces\Validators\ValidatorDataInterface;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Collection;

interface SalesforceServiceInterface
{
    public function setExecutor(string $type): void;

    public function execute(array $params): array;

    public function queryObject(string $objectType, array $params): array;

    public function validateSalesforceData(array $data): bool;

    public function processSalesforceData(array $salesforceData): Model|Collection|array|null|AbstractDto;

    public function processBulkSalesforceData(array $salesforceData): array;

    public function setValidator(ValidatorDataInterface $validator): void;

    public function setProcessor(ProcessorsInterface $processor): void;

    public function setSchemaClass(string $schemaClass): void;
}
