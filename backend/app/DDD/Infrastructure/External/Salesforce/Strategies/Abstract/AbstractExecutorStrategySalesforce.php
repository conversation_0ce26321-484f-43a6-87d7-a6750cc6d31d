<?php

declare(strict_types=1);

namespace App\DDD\Infrastructure\External\Salesforce\Strategies\Abstract;

use App\DDD\Application\SyncData\Services\DataSyncTransformationService;
use App\DDD\Infrastructure\External\Salesforce\Interfaces\SalesforceAuthInterface;
use App\DDD\Infrastructure\External\Salesforce\Interfaces\SalesforceServiceInterface;
use App\DDD\Infrastructure\Shared\Abstracts\Strategies\AbstractExecutorStrategy;
use App\DDD\Infrastructure\Shared\Interfaces\Strategies\ExecutorStrategyInterface;

// class AbstractExecutorStrategySalesforce //extends AbstractExecutorStrategy //implements ExecutorStrategyInterface
// class AbstractExecutorStrategySalesforce implements ExecutorStrategyInterface
// {
//     // private SalesforceAuthInterface $authSalesforce;
//     public SalesforceServiceInterface $serviceSalesforce;
//     public function __construct() {
//         // $this->authSalesforce = app(SalesforceAuthInterface::class);
//         $this->serviceSalesforce = app(SalesforceServiceInterface::class);
// //
//     }
//     // public function auth(): SalesforceAuthInterface
//     // {
//         // return $this->authSalesforce;
//     // }

//     // protected function getService(): SalesforceServiceInterface
//     // {
//     //     return $this->serviceSalesforce ??= app(SalesforceServiceInterface::class);
//     // }

//     public function execute(array $params): array{
//         // dd($params);
//         return $this->serviceSalesforce->execute($params);
//     }
//     // public function execute(array $params): array
//     // {
//     //     // dd($params);
//     //     return $this->getService()->execute($params);
//     //     // return [];
//     // }

// }

// protected SalesforceServiceInterface $serviceSalesforce;
// private SalesforceAuthInterface $authSalesforce;
// public SalesforceServiceInterface $serviceSalesforce;

class AbstractExecutorStrategySalesforce implements ExecutorStrategyInterface
{
    // public SalesforceServiceInterface $serviceSalesforce;
    public SalesforceAuthInterface $authSalesforce;

    // public DataSyncTransformationService $dataSyncTransformationService;
    public function __construct()
    {
        // $this->serviceSalesforce = app(SalesforceServiceInterface::class);
        $this->authSalesforce = app(SalesforceAuthInterface::class);
    }

    public function auth(): SalesforceAuthInterface
    {
        return $this->authSalesforce;
    }
    // public function setExecutor(string $type): void
    // {
    //     $this->serviceSalesforce->setExecutor($type);
    // }
    // public function getService(): SalesforceServiceInterface
    // {
    //     return $this->serviceSalesforce;
    // }

    public function execute(array $params): array
    {
        return [];
    }
    //     // return $this->serviceSalesforce->execute($params);
    // }
    // public function makeData(array $params): array
    // {
    //     return $this->dataSyncTransformationService->transformDataInDto($params);
    // }
}
