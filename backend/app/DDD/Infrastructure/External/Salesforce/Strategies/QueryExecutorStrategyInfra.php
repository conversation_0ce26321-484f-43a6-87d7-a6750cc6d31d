<?php

declare(strict_types=1);

namespace App\DDD\Infrastructure\External\Salesforce\Strategies;

use App\DDD\Infrastructure\External\Salesforce\Strategies\Abstract\AbstractExecutorStrategySalesforce;
use App\DDD\Infrastructure\Shared\Interfaces\Strategies\ExecutorStrategyInterface;
use Illuminate\Support\Facades\Http;

class QueryExecutorStrategyInfra extends AbstractExecutorStrategySalesforce implements ExecutorStrategyInterface
{
    /**
     * Execute a SOQL query on Salesforce
     *
     * @param  array  $params  {
     *
     * @type string $query    The SOQL query to execute
     * @type bool $tooling  Whether to use the Tooling API (optional)
     *            }
     *
     * @return array Response from Salesforce API containing query results
     */
    public function execute(array $params): array
    {
        $toolingUrl = isset($params['tooling']) ? 'tooling/' : '';
        $url = "{$this->auth()->getEndpoint()}/{$toolingUrl}query/?q={$params['query']}";

        $response = Http::withHeaders([
            'Authorization' => "Bearer {$this->auth()->getToken()}",
        ])
            ->timeout(600)
            ->connectTimeout(600)
            ->get($url);

        if ($response->successful()) {
            return $response->json();
        }

        return [
            'error' => $response->json(),
            'status' => false,
        ];
    }
}
