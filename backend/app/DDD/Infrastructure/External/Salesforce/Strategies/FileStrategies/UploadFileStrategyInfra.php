<?php

declare(strict_types=1);

namespace App\DDD\Infrastructure\External\Salesforce\Strategies\FileStrategies;

use App\DDD\Infrastructure\External\Salesforce\Strategies\Abstract\AbstractExecutorStrategySalesforce;
use App\DDD\Infrastructure\External\Salesforce\Strategies\InsertExecutorStrategyInfra;
use App\DDD\Infrastructure\Shared\Interfaces\Strategies\ExecutorStrategyInterface;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\File\File;

class UploadFileStrategyInfra extends AbstractExecutorStrategySalesforce implements ExecutorStrategyInterface
{
    /**
     * Execute a file upload operation to Salesforce
     *
     * @param  array  $params  {
     *
     * @type File $file  File to be updated
     * @type string $id  Id of the file to be updated
     *              }
     *
     * @return array Response from Salesforce API
     */
    public function execute(array $params): array
    {
        if (!($params['file'] instanceof File)) {
            throw new \InvalidArgumentException('O parâmetro "file" deve ser uma instância de UploadedFile.');
        }

        $file = $params['file'];
        $fileBase64 = base64_encode(file_get_contents($file->getRealPath()));
        $fileName = $file->getFilename();
        $contentVersionData = [
            'Title' => $fileName,
            'PathOnClient' => $fileName,
            'VersionData' => $fileBase64,
            'FirstPublishLocationId' => $params['id'],
        ];
        $params = [
            'objectType' => 'ContentVersion',
            'data' => $contentVersionData,
        ];

        $insertStrategy = new InsertExecutorStrategyInfra();

        return $insertStrategy->execute($params);
    }
}
