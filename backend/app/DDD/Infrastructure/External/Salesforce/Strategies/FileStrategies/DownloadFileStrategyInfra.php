<?php

declare(strict_types=1);

namespace App\DDD\Infrastructure\External\Salesforce\Strategies\FileStrategies;

use App\DDD\Infrastructure\External\Salesforce\Strategies\Abstract\AbstractExecutorStrategySalesforce;
use App\DDD\Infrastructure\Shared\Interfaces\Strategies\ExecutorStrategyInterface;
use App\DDD\Infrastructure\Shared\Services\FilesService;
use Illuminate\Support\Facades\Http;

class DownloadFileStrategyInfra extends AbstractExecutorStrategySalesforce implements ExecutorStrategyInterface
{
    public function __construct(
        private readonly FilesService $filesService
    ) {
        parent::__construct();
    }

    /**
     * Execute a file upload operation to Salesforce
     *
     * @param  array  $params  {
     *
     * @type File $file  File to be updated
     * @type string $id  Id of the file to be updated
     *              }
     *
     * @return array Response from Salesforce API
     */
    public function execute(array $params): array
    {
        $pathfile = sprintf(
            '%s/%s/documents/%s/',
            $params['type'],
            $params['documentData'][$params['type']],
            $params['documentData']['Id']
        );

        $file = $params['documentData']['Link__c'] ?? $params['documentData']['IdInterno__c'];
        $filename = $params['documentData']['TituloArquivo__c'];
        $filename = $this->filesService->processFileName($filename);

        if (preg_match('/drive\.google\.com/', $file)) {
            $file = $this->filesService->getPdfDrive($file);
        } else {
            $filePath = $this->filesService->showpdfsales($file);
            $file = $this->urlpdf($filePath);
        }

        $mime_type = $this->filesService->determineContentType($file, $filename);

        if (! str_ends_with(strtolower($filename), '.pdf') && ! str_starts_with($mime_type, 'image/')) {
            $filename .= '.pdf';
        }

        if (! isset($file) || ! isset($filename)) {
            return [
                'error' => 'Arquivo não encontrado',
                'status' => false,
            ];
        }

        return $this->filesService->uploadFileS3($pathfile.$filename, $file);
    }

    public function urlpdf($src)
    {
        $response = Http::withHeaders(['Authorization' => "Bearer {$this->auth()->getToken()}"])
            ->timeout(600)
            ->connectTimeout(600)
            ->get($src);
        if ($response->successful()) {
            return $response->getBody()->getContents();
        }

        return [
            'error' => $response->json(),
            'status' => false,
        ];
    }
}
