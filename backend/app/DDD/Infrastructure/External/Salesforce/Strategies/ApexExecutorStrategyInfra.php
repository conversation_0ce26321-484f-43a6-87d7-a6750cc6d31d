<?php

declare(strict_types=1);

namespace App\DDD\Infrastructure\External\Salesforce\Strategies;

use App\DDD\Infrastructure\External\Salesforce\Strategies\Abstract\AbstractExecutorStrategySalesforce;
use App\DDD\Infrastructure\Shared\Interfaces\Strategies\ExecutorStrategyInterface;
use Illuminate\Support\Facades\Http;

class ApexExecutorStrategyInfra extends AbstractExecutorStrategySalesforce implements ExecutorStrategyInterface
{
    /**
     * Execute an Apex REST endpoint on Salesforce
     *
     * @param  array  $params  {
     *
     * @type string $query The Apex REST endpoint path
     * @type array $data  The data to send to the endpoint (optional)
     *             }
     *
     * @return array Response from Salesforce API
     */
    public function execute(array $params): array
    {
        $url = "{$this->auth()->getEndpoint()}/{$params['query']}";

        $response = Http::withHeaders([
            'Authorization' => "Bearer {$this->auth()->getToken()}",
        ])
            ->timeout(600)
            ->connectTimeout(600)
            ->post($url, $params['data']);

        if ($response->successful()) {
            return $response->json();
        }

        return [
            'error' => $response->json(),
            'status' => false,
        ];
    }
}
