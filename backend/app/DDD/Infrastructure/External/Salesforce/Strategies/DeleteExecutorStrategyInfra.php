<?php

declare(strict_types=1);

namespace App\DDD\Infrastructure\External\Salesforce\Strategies;

use App\DDD\Infrastructure\External\Salesforce\Strategies\Abstract\AbstractExecutorStrategySalesforce;
use App\DDD\Infrastructure\Shared\Interfaces\Strategies\ExecutorStrategyInterface;
use Illuminate\Support\Facades\Http;

class DeleteExecutorStrategyInfra extends AbstractExecutorStrategySalesforce implements ExecutorStrategyInterface
{
    /**
     * Execute a delete operation on Salesforce object
     *
     * @param  array  $params  {
     *
     * @type string $query The Salesforce object type (e.g., 'Account', 'Contact')
     * @type array $data {
     * @type string $id The Salesforce record ID to delete
     *              }
     *              }
     *
     * @return array Response from Salesforce API
     */
    public function execute(array $params): array
    {
        $url = "{$this->auth()->getEndpoint()}/sobjects/{$params['query']}/{$params['data']['id']}";

        $response = Http::withHeaders([
            'Authorization' => "Bearer {$this->auth()->getToken()}",
            'Content-Type' => 'application/json',
        ])
            ->timeout(600)
            ->connectTimeout(600)
            ->delete($url);

        if ($response->successful()) {
            return $response->json();
        }

        return [
            'error' => $response->json(),
            'status' => false,
        ];
    }
}
