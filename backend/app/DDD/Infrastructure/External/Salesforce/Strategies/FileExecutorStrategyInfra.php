<?php

declare(strict_types=1);

namespace App\DDD\Infrastructure\External\Salesforce\Strategies;

use App\DDD\Infrastructure\External\Salesforce\Strategies\Abstract\AbstractExecutorStrategySalesforce;
use App\DDD\Infrastructure\External\Salesforce\Strategies\FileStrategies\DownloadFileStrategyInfra;
use App\DDD\Infrastructure\External\Salesforce\Strategies\FileStrategies\UploadFileStrategyInfra;
use App\DDD\Infrastructure\Shared\Interfaces\Services\FilesServiceInterface;
use App\DDD\Infrastructure\Shared\Interfaces\Strategies\ExecutorStrategyInterface;

class FileExecutorStrategyInfra extends AbstractExecutorStrategySalesforce implements ExecutorStrategyInterface
{
    private ExecutorStrategyInterface $strategy;

    /**
     * Execute a file upload operation to Salesforce
     *
     * @param  array  $params  {
     *
     * @type string $query     The Salesforce API endpoint for file upload
     * @type array $data {
     * @type string $type
     * @type string $Title         The title of the file in Salesforce
     * @type string $VersionData   The base64 encoded file content
     *              }
     *              }
     *
     * @return array Response from Salesforce API
     */
    public function execute(array $params): array
    {
        $this->strategy = match ($params['type']) {
            'upload' => new UploadFileStrategyInfra(),
            'download' => new DownloadFileStrategyInfra(app()->make(FilesServiceInterface::class)),
            default => throw new \InvalidArgumentException('Invalid executor action')
        };

        return $this->strategy->execute($params);
    }
}
