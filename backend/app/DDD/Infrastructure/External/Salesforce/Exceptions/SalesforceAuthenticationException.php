<?php

declare(strict_types=1);

namespace App\DDD\Infrastructure\External\Salesforce\Exceptions;

use Exception;
use Throwable;

class SalesforceAuthenticationException extends Exception
{
    private array $context;

    public function __construct(
        string $message = 'Authentication with Salesforce failed',
        int $code = 0,
        ?Throwable $previous = null,
        array $context = []
    ) {
        parent::__construct($message, $code, $previous);
        $this->context = $context;
    }

    public function getContext(): array
    {
        return $this->context;
    }

    public static function invalidCredentials(): self
    {
        return new self(
            'Invalid Salesforce credentials provided',
            1001,
            null,
            ['error_type' => 'invalid_credentials']
        );
    }

    public static function tokenExpired(): self
    {
        return new self(
            'Salesforce authentication token has expired',
            1002,
            null,
            ['error_type' => 'token_expired']
        );
    }

    public static function configurationMissing(): self
    {
        return new self(
            'Missing required Salesforce configuration',
            1003,
            null,
            ['error_type' => 'config_missing']
        );
    }

    public static function networkError(string $details = ''): self
    {
        return new self(
            'Network error while authenticating with Salesforce: '.$details,
            1004,
            null,
            [
                'error_type' => 'network_error',
                'details' => $details,
            ]
        );
    }

    public static function fromResponse(array $responseData): self
    {
        $message = $responseData['error_description'] ?? $responseData['message'] ?? 'Unknown Salesforce error';
        $errorType = $responseData['error'] ?? 'unknown';

        return new self(
            $message,
            1005,
            null,
            [
                'error_type' => $errorType,
                'response_data' => $responseData,
            ]
        );
    }
}
