<?php

declare(strict_types=1);

namespace App\DDD\Infrastructure\External\Salesforce\Factories;

use App\DDD\Infrastructure\External\Salesforce\Strategies\ApexExecutorStrategyInfra;
use App\DDD\Infrastructure\External\Salesforce\Strategies\DeleteExecutorStrategyInfra;
use App\DDD\Infrastructure\External\Salesforce\Strategies\FileExecutorStrategyInfra;
use App\DDD\Infrastructure\External\Salesforce\Strategies\InsertExecutorStrategyInfra;
use App\DDD\Infrastructure\External\Salesforce\Strategies\QueryExecutorStrategyInfra;
use App\DDD\Infrastructure\External\Salesforce\Strategies\UpdateExecutorStrategyInfra;
use App\DDD\Infrastructure\Shared\Interfaces\Strategies\ExecutorStrategyInterface;
use InvalidArgumentException;

class ExecutorStrategyInfraFactory
{
    public function make(string $type = 'query'): ExecutorStrategyInterface
    {
        return match ($type) {
            'query' => new QueryExecutorStrategyInfra(),
            'apex' => new ApexExecutorStrategyInfra(),
            'insert' => new InsertExecutorStrategyInfra(),
            'update' => new UpdateExecutorStrategyInfra(),
            'delete' => new DeleteExecutorStrategyInfra(),
            'file' => new FileExecutorStrategyInfra(),
            default => throw new InvalidArgumentException('Invalid executor type'),
        };
    }
}
