<?php

declare(strict_types=1);

namespace App\DDD\Infrastructure\Schedule\Persistence\Repositories;

use App\DDD\Domain\Schedule\Entities\Schedule;
use App\DDD\Infrastructure\Schedule\Persistence\Interfaces\ScheduleRepositoryInterface;
use App\DDD\Infrastructure\Shared\Abstracts\Repositories\AbstractRepository;

class ScheduleRepository extends AbstractRepository implements ScheduleRepositoryInterface
{
    public function getModelClass(): string
    {
        return Schedule::class;
    }
}
