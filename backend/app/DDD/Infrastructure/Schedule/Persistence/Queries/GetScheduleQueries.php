<?php

declare(strict_types=1);

namespace App\DDD\Infrastructure\Schedule\Persistence\Queries;

use App\DDD\Domain\Schedule\ValueObjects\SubjectEnum;
use App\DDD\Infrastructure\Shared\Abstracts\Queries\AbstractQueries;
use App\DDD\Infrastructure\Shared\Interfaces\Queries\QueryStrategyInterface;

class GetScheduleQueries extends AbstractQueries implements QueryStrategyInterface
{
    public function execute(): string
    {
        $subjectsString = "'".implode("', '", SubjectEnum::toArray())."'";

        $query = "Select
            Id,
            WhatId,
            Subject,
            Type,
            StartDateTime,
            EndDateTime,
            Location
        FROM
            Event
        WHERE
            Subject IN ({$subjectsString}) ";

        if (isset($this->getParams()['empreendimentoId'])) {
            $query .= "AND WhatId='{$this->getParams()['empreendimentoId']}' ";
        }

        return $query;
    }
}
