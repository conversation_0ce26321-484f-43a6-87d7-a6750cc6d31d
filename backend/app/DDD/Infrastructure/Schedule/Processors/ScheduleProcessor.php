<?php

declare(strict_types=1);

namespace App\DDD\Infrastructure\Schedule\Processors;

use App\DDD\Application\Schedule\DTOs\CreateScheduleDto;
use App\DDD\Application\Schedule\Schema\ScheduleSchema;
use App\DDD\Domain\Schedule\Interfaces\ScheduleValidationInterface;
use App\DDD\Infrastructure\External\Salesforce\Interfaces\SalesforceServiceInterface;
use App\DDD\Infrastructure\Schedule\Interfaces\ScheduleProcessorInterface;
use App\DDD\Infrastructure\Schedule\Persistence\Queries\GetScheduleQueries;
use App\DDD\Infrastructure\Shared\Abstracts\Processors\AbstractProcessor;
use App\DDD\Infrastructure\Shared\Interfaces\DTO\DtoInterface;
use App\DDD\Infrastructure\Shared\Interfaces\Services\ServiceLocatorInterface;
use Illuminate\Support\Facades\Log;
use InvalidArgumentException;

/**
 * Processador para agendamentos
 */
class ScheduleProcessor extends AbstractProcessor implements ScheduleProcessorInterface
{
    /**
     * @param  ScheduleValidationInterface  $validator  Validador de agendamentos
     * @param  SalesforceServiceInterface  $salesforceService  Serviço do Salesforce
     * @param  ServiceLocatorInterface|null  $serviceLocator  Localizador de serviços (opcional)
     */
    public function __construct(
        ScheduleValidationInterface $validator,
        SalesforceServiceInterface $salesforceService,
        ?ServiceLocatorInterface $serviceLocator = null
    ) {
        parent::__construct($validator, $serviceLocator);

        // Registra o serviço do Salesforce
        $this->serviceLocator->register('salesforceService', $salesforceService);
    }

    /**
     * Retorna a classe do schema a ser utilizado
     */
    public function getSchemaClass(): string
    {
        return ScheduleSchema::class;
    }

    /**
     * Cria um DTO a partir dos dados processados
     * Caso o retorno seja um Collection, deixe como null.
     *
     * @param  array  $data  Os dados processados
     * @return DtoInterface|null O DTO criado ou null
     */
    protected function createDto(array $data): ?DtoInterface
    {
        return null;
    }

    /**
     * Registra as estratégias de resolução para cada tipo de objeto
     */
    public function preProcess(array $data): array
    {
        // Estratégia para Proposta__c
        $this->typeResolver->registerStrategy('Proposta__c', function (array $data) {
            return $data['Empreendimento__c'];
        });

        // Estratégia para Empreendimento__c
        $this->typeResolver->registerStrategy('Empreendimento__c', function (array $data) {
            return $data['Id'];
        });

        return $data;
    }

    /**
     * Processa os dados de agendamento conforme o tipo
     */
    public function parseData(array $data): array
    {
        $type = $data['attributes']['type'] ?? null;
        if (! $type) {
            throw new InvalidArgumentException('Tipo de dados não informado');
        }

        try {
            Log::info('########');
            Log::info($type);
            Log::info($data);

            // Resolve o ID do empreendimento com base no tipo
            $empreendimentoId = $this->typeResolver->apply($type, $data);
            Log::info($empreendimentoId);
            // Obtém os dados de agendamento
            $scheduleData = $this->getScheduleServices($empreendimentoId['id']);
            Log::info($scheduleData);
            $records = $scheduleData['records'] ?? null;

            if ($records === null) {
                return [];
            }
            Log::info('########');

            return $this->processScheduleRecords($records, $type);
        } catch (InvalidArgumentException $e) {
            throw new InvalidArgumentException("Tipo de dado não suportado: {$type}");
        }
    }

    /**
     * Processa os registros de agendamento e cria os DTOs
     *
     * @param  array  $records  Registros de agendamento
     * @param  string  $type  Tipo de objeto
     * @return array Array de DTOs de agendamento
     */
    private function processScheduleRecords(array $records, string $type): array
    {
        $schedules = [];

        foreach ($records as $scheduleData) {
            $this->validator->isValidateSalesforceData($scheduleData);
            $schedule = $this->transformData($scheduleData, $type);
            Log::info('######## schedule DTO');
            Log::info('######## schedule DTO');
            Log::info('######## schedule DTO');
            Log::info('######## schedule DTO');
            Log::info('######## schedule DTO');
            Log::info($schedule);
            Log::info('########');
            $schedules[] = new CreateScheduleDto($schedule);
        }

        return $schedules;
    }

    /**
     * Busca os serviços de agendamento pelo ID do empreendimento
     *
     * @param  string  $empreendimentoId  ID do empreendimento
     * @return array|null Dados de agendamento ou null
     */
    private function getScheduleServices(string $empreendimentoId): ?array
    {
        $paramsQuery = [
            'params' => ['empreendimentoId' => $empreendimentoId],
            'queryType' => new GetScheduleQueries(),
        ];

        /** @var SalesforceServiceInterface $salesforceService */
        $salesforceService = $this->service('salesforceService');

        return $salesforceService->queryObject('query', $paramsQuery);
    }
}
