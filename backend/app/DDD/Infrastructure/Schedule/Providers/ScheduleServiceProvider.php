<?php

declare(strict_types=1);

namespace App\DDD\Infrastructure\Schedule\Providers;

use App\DDD\Infrastructure\Schedule\Providers\Config\ScheduleBindings;
use App\Providers\ApiServicesProviders\BaseApiDDDServiceProvider;

class ScheduleServiceProvider extends BaseApiDDDServiceProvider
{
    public function register(): void
    {
        parent::register();
        (new ScheduleBindings())->register($this->app);
    }
}

// use App\DDD\Application\Schedule\Interfaces\ScheduleServiceInterface;

// use App\DDD\Application\Schedule\Services\ScheduleService;
// use App\DDD\Domain\Schedule\Interfaces\ScheduleValidationInterface;
// use App\DDD\Domain\Schedule\Validation\ScheduleValidation;
// use App\DDD\Infrastructure\Schedule\Interfaces\ScheduleProcessorInterface;
// use App\DDD\Infrastructure\Schedule\Persistence\Interfaces\ScheduleRepositoryInterface;

// use App\DDD\Infrastructure\Schedule\Persistence\Repositories\ScheduleRepository;
// use App\DDD\Infrastructure\Schedule\Processors\ScheduleProcessor;
// use App\DDD\Infrastructure\User\Interfaces\UserProcessorInterface;

// use App\DDD\Infrastructure\User\Processors\UserProcessor;
// use App\Providers\ApiServicesProviders\BaseApiDDDServiceProvider;
// ;

// class ScheduleServiceProvider extends BaseApiDDDServiceProvider
// {
//     protected const MAIN_SERVICES = [
//         ScheduleServiceInterface::class => ScheduleService::class,
//         ScheduleRepositoryInterface::class => ScheduleRepository::class,
//         ScheduleProcessorInterface::class => ScheduleProcessor::class,
//         ScheduleValidationInterface::class => ScheduleValidation::class
//     ];

//     protected function registerMainService(): void
//     {
//         $this->app->singleton(ScheduleServiceInterface::class, function ($app) {
//             return new ScheduleService();
//         });

//         $this->app->singleton(ScheduleProcessorInterface::class, function ($app) {
//             return new ScheduleProcessor(
//                 $app->make(ScheduleServiceInterface::class),
//                 $app->make(ScheduleValidationInterface::class)
//             );
//         });

//         $this->app->bind(ScheduleRepositoryInterface::class, ScheduleRepository::class);
//         $this->app->bind(ScheduleValidationInterface::class, ScheduleValidation::class);
//         $this->app->bind(UserProcessorInterface::class, UserProcessor::class);

//     }

// }
