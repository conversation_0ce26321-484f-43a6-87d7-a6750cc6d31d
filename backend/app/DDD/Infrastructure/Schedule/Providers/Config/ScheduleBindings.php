<?php

declare(strict_types=1);

namespace App\DDD\Infrastructure\Schedule\Providers\Config;

use App\DDD\Application\Schedule\Interfaces\ScheduleServiceInterface;
use App\DDD\Application\Schedule\Services\ScheduleService;
use App\DDD\Domain\Schedule\Interfaces\ScheduleValidationInterface;
use App\DDD\Domain\Schedule\Validation\ScheduleValidation;
use App\DDD\Infrastructure\External\Salesforce\Interfaces\SalesforceServiceInterface;
use App\DDD\Infrastructure\Schedule\Interfaces\ScheduleProcessorInterface;
use App\DDD\Infrastructure\Schedule\Persistence\Interfaces\ScheduleRepositoryInterface;
use App\DDD\Infrastructure\Schedule\Persistence\Repositories\ScheduleRepository;
use App\DDD\Infrastructure\Schedule\Processors\ScheduleProcessor;
use App\DDD\Infrastructure\Shared\Abstracts\Bindings\AbstractBindings;
use App\DDD\Infrastructure\Shared\Interfaces\Bindings\BindingsInterface;
use App\DDD\Infrastructure\Shared\Services\NotificationService;
use Illuminate\Contracts\Foundation\Application;

class ScheduleBindings extends AbstractBindings implements BindingsInterface
{
    public function registerServices(Application $app): void
    {
        if (! $app->bound(ScheduleRepositoryInterface::class)) {
            $app->singleton(
                ScheduleRepositoryInterface::class,
                function ($app) {
                    return new ScheduleRepository();
                }
            );
        }

        $app->singleton(
            ScheduleServiceInterface::class,
            function ($app) {
                return new ScheduleService(
                    $app->make(ScheduleRepositoryInterface::class),
                    $app->make(ScheduleValidationInterface::class),
                    $app->make(ScheduleProcessorInterface::class)
                );
            }
        );
    }

    public function registerRepositories(Application $app): void
    {
        $app->singleton(
            ScheduleRepositoryInterface::class,
            function ($app) {
                return new ScheduleRepository();
            }
        );
    }

    public function registerProcessors(Application $app): void
    {
        $app->singleton(
            ScheduleProcessorInterface::class,
            function ($app) {
                return new ScheduleProcessor(
                    $app->make(ScheduleValidationInterface::class),
                    $app->make(SalesforceServiceInterface::class)
                );
            }
        );
    }

    public function registerValidators(Application $app): void
    {
        $app->singleton(
            ScheduleValidationInterface::class,
            function ($app) {
                return new ScheduleValidation($app->make(NotificationService::class));
            }
        );
    }
}
