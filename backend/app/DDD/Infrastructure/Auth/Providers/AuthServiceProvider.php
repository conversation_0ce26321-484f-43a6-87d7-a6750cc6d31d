<?php

declare(strict_types=1);

namespace App\DDD\Infrastructure\Auth\Providers;

use App\DDD\Infrastructure\Auth\Providers\Config\AuthBindings;
use App\DDD\UI\Auth\Http\Middleware\CheckClientRole;
use App\DDD\UI\Auth\Http\Middleware\CheckPermutanteRole;
use App\DDD\UI\Auth\Http\Middleware\CheckSindicoRole;
use App\Providers\ApiServicesProviders\BaseApiDDDServiceProvider;

class AuthServiceProvider extends BaseApiDDDServiceProvider
{
    public function register(): void
    {
        parent::register();
        (new AuthBindings())->register($this->app);
    }

    public function boot()
    {
        $router = $this->app['router'];

        $router->aliasMiddleware('auth.client', CheckClientRole::class);
        $router->aliasMiddleware('auth.permutante', CheckPermutanteRole::class);
        $router->aliasMiddleware('auth.sindico', CheckSindicoRole::class);
    }
}
