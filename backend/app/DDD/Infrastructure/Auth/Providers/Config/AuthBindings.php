<?php

declare(strict_types=1);

namespace App\DDD\Infrastructure\Auth\Providers\Config;

use App\DDD\Application\Auth\Interfaces\AuthServiceInterface;
use App\DDD\Application\Auth\Interfaces\ImpersonateApplicationServiceInterface;
use App\DDD\Application\Auth\Services\AuthService;
use App\DDD\Application\Auth\Services\ImpersonateApplicationService;
// use App\DDD\Domain\Auth\Interfaces\AuthenticationServiceInterface;
// use App\DDD\Domain\Auth\Interfaces\ImpersonateServiceInterface;
use App\DDD\Domain\Auth\Interfaces\ImpersonationServiceInterface;
use App\DDD\Domain\Auth\Services\ImpersonateService;
use App\DDD\Infrastructure\Auth\Interfaces\AuthRepositoryInterface;
use App\DDD\Infrastructure\Auth\Interfaces\ImpersonateTokenServiceInterface;
use App\DDD\Infrastructure\Auth\Interfaces\TokenServiceInterface;
use App\DDD\Infrastructure\Auth\Persistence\Repositories\AuthRepository;
use App\DDD\Infrastructure\Auth\Services\ImpersonateTokenService;
use App\DDD\Infrastructure\Auth\Services\ImpersonationService;
use App\DDD\Infrastructure\Auth\Services\TokenService;
use App\DDD\Infrastructure\Shared\Abstracts\Bindings\AbstractBindings;
use App\DDD\Infrastructure\Shared\Interfaces\Bindings\BindingsInterface;
use App\DDD\UI\Auth\Http\Controllers\AuthControllerV2;
use App\DDD\UI\Auth\Http\Controllers\ImpersonateController;
use App\DDD\UI\Auth\Http\Controllers\ImpersonationController;
use App\DDD\UI\Auth\Http\Middleware\CheckImpersonation;
use App\DDD\UI\Auth\Http\Middleware\CheckImpersonationHeader;
use App\DDD\UI\Auth\Interfaces\AuthControllerInterface;
use App\DDD\UI\Auth\Interfaces\ImpersonateControllerInterface;
use Illuminate\Contracts\Foundation\Application;

class AuthBindings extends AbstractBindings implements BindingsInterface
{
    public function registerServices(Application $app): void
    {
        // Serviços existentes
        $app->bind(AuthServiceInterface::class, AuthService::class);
        // $app->bind(AuthenticationServiceInterface::class, AuthService::class);
        $app->bind(AuthRepositoryInterface::class, AuthRepository::class);
        $app->bind(TokenServiceInterface::class, TokenService::class);
        $app->bind(ImpersonationServiceInterface::class, ImpersonateService::class);

        // Novos serviços para impersonificação
        $app->bind(ImpersonationServiceInterface::class, ImpersonateService::class);
        $app->bind(ImpersonateApplicationServiceInterface::class, ImpersonateApplicationService::class);
        $app->bind(ImpersonateTokenServiceInterface::class, ImpersonateTokenService::class);


        // Controllers existentes
        $app->singleton(AuthControllerInterface::class, function ($app) {
            return new AuthControllerV2(
                $app->make(AuthServiceInterface::class)
            );
        });

        $app->singleton(CheckImpersonationHeader::class, function ($app) {
            return new CheckImpersonationHeader(
                $app->make(ImpersonateTokenServiceInterface::class)
            );
        });
        // Novo controller para impersonificação
        // $app->singleton(ImpersonationControllerInterface::class, function ($app) {
        //     return new ImpersonationController(
        //         $app->make(ImpersonateApplicationServiceInterface::class)
        //     );
        // });
        $router = $app['router'];

        $router->aliasMiddleware('check.impersonation', CheckImpersonationHeader::class);
    }

}