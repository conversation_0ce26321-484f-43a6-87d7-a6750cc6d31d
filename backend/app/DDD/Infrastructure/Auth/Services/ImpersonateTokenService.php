<?php

namespace App\DDD\Infrastructure\Auth\Services;

use App\DDD\Domain\Auth\ValueObjects\ImpersonationToken;
use App\DDD\Domain\User\Entities\User;
use App\DDD\Infrastructure\Auth\Interfaces\ImpersonateTokenServiceInterface;
use Carbon\Carbon;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Laravel\Sanctum\PersonalAccessToken;

class ImpersonateTokenService extends TokenService implements ImpersonateTokenServiceInterface
{

    public function storeToken(ImpersonationToken $token): bool
    {
        try {
            $expiration = $token->getExpiresAt()->diffInMinutes(Carbon::now());

            $cacheKey = $token->getToken();
            $stored = Cache::put(
                $cacheKey,
                [
                    'token' => $token->getToken(),
                    'admin_id' => $token->getAdminId(),
                    'user_id' => $token->getUserId(),
                    'expires_at' => $token->getExpiresAt()->toIso8601String()
                ],
                now()->addHours(2)
            );

            if (!$stored) {
                throw new \RuntimeException('Falha ao armazenar token no cache');
            }

            $data = Cache::get($cacheKey);

            if (!$data) {
                Log::error('Token não encontrado no cache após armazenamento', ['cache_key' => $cacheKey]);
                throw new \RuntimeException('Token não pôde ser recuperado do cache após armazenamento');
            }


            return true;
        } catch (\Exception $e) {
            Log::error('Failed to store impersonation token', [
                'error' => $e->getMessage()
            ]);

            return false;
        }
    }

    public function validateToken(string $token): ?ImpersonationToken
    {
        $data = Cache::get($token);

        if (!$data) {
            return null;
        }

        return new ImpersonationToken(
            $data['token'],
            $data['admin_id'],
            $data['user_id'],
            Carbon::parse($data['expires_at'])
        );
    }

    public function revokeImpersonateToken(string $token, string $device_info): bool
    {
        $tokenParts = explode("|", $token);
        $tokenId = $tokenParts[0];
        $token = PersonalAccessToken::find($tokenId);
        if($token === null)return false;
        $name = $token->name;
        PersonalAccessToken::where('name', $name)->delete();
        return true;
    }
}
