<?php

declare(strict_types=1);

namespace App\DDD\Infrastructure\Auth\Services;

use App\DDD\Domain\Auth\Entities\RefreshToken;
use App\DDD\Domain\User\Entities\User;
use App\DDD\Infrastructure\Auth\Interfaces\TokenServiceInterface;
use DateTime;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class TokenService implements TokenServiceInterface
{
    private const REFRESH_TOKEN_EXPIRES = 2592000;

    public function createToken(User $user, string $deviceinfo): array
    {
        $token = $user->createToken($deviceinfo, ['*'], now()->addWeek());
        $refreshToken = $this->generateRefreshToken($user->getId(), $deviceinfo);

        return [
            'access_token' => $token->plainTextToken,
            'expires_at' => $token->accessToken->expires_at->toISOString(),
            'refresh_token' => $refreshToken->getToken(),
        ];
    }

    public function revokeTokens(User $user): bool
    {
        // $userModel = app()->make(User::class)->find($user->getId());

        return $user->tokens()->delete() > 0;
    }

    public function revokeToken(User $user, string $device_info): bool
    {
        // $userModel = app()->make(User::class)->find($user->getId());

        return $user->tokens()->where('name', $device_info)->delete() > 0;
    }

    public function revokeRefreshToken(string $refreshToken): bool
    {
        return RefreshToken::where('token', $refreshToken)
            ->delete() > 0;
    }

    public function refreshToken(string $refreshToken): ?array
    {
        $validRefreshToken = $this->validateRefreshToken($refreshToken);

        if (!$validRefreshToken) {
            return null;
        }

        // Revogar o refresh token antigo
        $this->revokeRefreshToken($refreshToken);

        // Obter o usuário
        $userId = $validRefreshToken->getUserId();
        $userModel = app()->make(User::class)->find($userId);

        if (!$userModel) {
            return null;
        }

        // Converter para nosso modelo de domínio

        // $userModel->tokens()->delete();
        $deviceinfo = $validRefreshToken->getDeviceinfo() ?? 'unknown';
        if($deviceinfo !== 'unknown'){
            $userModel->tokens()->where('name', $deviceinfo)->delete();
        }

        return $this->createToken($userModel, $deviceinfo);
    }

    public function validateRefreshToken(string $refreshToken): ?RefreshToken
    {
        $tokenData = RefreshToken::where('token', $refreshToken)
            ->first();

        if (!$tokenData) {
            return null;
        }

        // Converter para entidade de domínio
        $refreshTokenEntity = new RefreshToken(
            $tokenData->user_id,
            $tokenData->token,
            new DateTime($tokenData->expires_at),
            $tokenData->device_info,
            new DateTime($tokenData->created_at),
            $tokenData->updated_at ? new DateTime($tokenData->updated_at) : null
        );

        // Verificar se expirou
        if ($refreshTokenEntity->isExpired()) {
            // Remover token expirado
            $this->revokeRefreshToken($refreshToken);

            return null;
        }

        return $refreshTokenEntity;
    }

    /**
     * Gera um novo refresh token para o usuário.
     */
    private function generateRefreshToken(int $userId, string $deviceinfo): RefreshToken
    {
        // Gerar token único
        $token = Str::random(64);

        // Calcular expiração
        $expiresAt = new DateTime();
        $expiresAt->modify('+' . self::REFRESH_TOKEN_EXPIRES . ' seconds');

        // Criar entidade
        return new RefreshToken(
            $userId,
            $token,
            $expiresAt,
            $deviceinfo,
            now(),
            now()
        );
        // $refreshToken = new RefreshToken(
        //     $userId,
        //     $token,
        //     $expiresAt,
        //     $deviceinfo,
        //     now(),
        //     now()
        // );

        // Salvar no banco
        // RefreshToken::create([
        //     'user_id' => $userId,
        //     'token' => $token,
        //     'device_info' => $deviceinfo,
        //     'expires_at' => $expiresAt->format('Y-m-d H:i:s'),
        //     'created_at' => now(),
        //     'updated_at' => now(),
        // ]);

        // return $refreshToken;
    }
}
