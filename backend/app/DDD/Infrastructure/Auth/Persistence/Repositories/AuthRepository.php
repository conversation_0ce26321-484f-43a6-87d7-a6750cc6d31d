<?php

declare(strict_types=1);

namespace App\DDD\Infrastructure\Auth\Persistence\Repositories;

use App\DDD\Domain\User\Entities\User;
use App\DDD\Infrastructure\Auth\Interfaces\AuthRepositoryInterface;
use App\DDD\Infrastructure\Shared\Utils\FormatDocs;
use App\DDD\Infrastructure\User\Persistence\Repositories\UserRepository;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;

use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;

class AuthRepository extends UserRepository implements AuthRepositoryInterface
{


    public function find(int|string $id): Model|Collection|null
    {
        $userLogin = $id;

        // Busca apenas o usuário primeiro
        $query = $this->getModelClass()::query()
            ->select([
                'id',
                'AccountId',
                // 'PersonContactId',
                'Email__c',
                'CPF__c',
                'CNPJ__c',
                'email_verified_at',
                'password',
                'password_plaintext'
            ])
            ->when(
                strlen($userLogin) <= 15,
                function ($query) use ($userLogin) {
                    return $query->where('CPF__c', FormatDocs::formatCpf($userLogin));
                },
                function ($query) use ($userLogin) {
                    return $query->where('CNPJ__c', FormatDocs::formatCnpj($userLogin));
                }
            );


        // Executa a consulta apenas para o usuário
        $userModel = $query->first();
        return $userModel;

    }

    /**
     * Valida as credenciais do usuário
     *
     * @param User $user
     * @param string $password
     * @return bool
     */
    public function validateCredentials(User $user, string $password): bool
    {
        $verifyed = Hash::check($password, $user->password);
        if (!$verifyed) {
            $verifyed = Hash::check($password, $user->password_plaintext);
        }
        return $verifyed;
    }


}
