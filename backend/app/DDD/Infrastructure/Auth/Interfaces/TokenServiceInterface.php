<?php

declare(strict_types=1);

namespace App\DDD\Infrastructure\Auth\Interfaces;

use App\DDD\Domain\Auth\Entities\RefreshToken;
use App\DDD\Domain\User\Entities\User;
use Illuminate\Database\Eloquent\Model;
interface TokenServiceInterface
{
    /**
     * Cria um novo token para o usuário.
     *
     * @param  string  $deviceInfo  Informação sobre o dispositivo/navegador
     * @return array [accessToken, refreshToken]
     */
    public function createToken(User $user, string $deviceInfo): array;

    /**
     * Revoga todos os tokens de um usuário.
     */
    public function revokeTokens(User $user): bool;

    /**
     * Revoga um token específico.
     */
    public function revokeToken(User $user, string $device_info): bool;

    /**
     * Revoga um refresh token específico.
     */
    public function revokeRefreshToken(string $refreshToken): bool;

    /**
     * Atualiza o access token usando o refresh token.
     *
     * @return array|null [accessToken, refreshToken] ou null se inválido
     */
    public function refreshToken(string $refreshToken): ?array;

    /**
     * Verifica se um refresh token é válido.
     */
    public function validateRefreshToken(string $refreshToken): ?RefreshToken;
}
