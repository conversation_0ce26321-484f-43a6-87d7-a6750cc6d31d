<?php

declare(strict_types=1);

namespace App\DDD\Infrastructure\Auth\Interfaces;

// use App\DDD\Domain\Auth\Entities\Role;
// use App\DDD\Domain\Auth\Entities\UserRole;
use App\DDD\Domain\User\Entities\User;
use App\DDD\Infrastructure\User\Interfaces\UserRepositoryInterface;
use Illuminate\Database\Eloquent\Model;
interface AuthRepositoryInterface extends UserRepositoryInterface
{


    /**
     * Valida as credenciais de um usuário.
     *
     * @param  User  $user  O usuário a ser validado.
     * @param  string  $password  A senha a ser comparada.
     * @return bool  Retorna true se as credenciais forem válidas, false caso contrário.
     */
    public function validateCredentials(User $user, string $password): bool;

    /**
     * Obtém todas as roles atribuídas a um usuário.
     *
     * @param  int  $userId
     * @return Role[]
     */
    // public function getUserRoles(string $accountId): array;

    /**
     * Obtém a relação de role específica de um usuário.
     */
    // public function getUserRole(int $userId, int $roleId): ?UserRole;

    /**
     * Obtém uma role pelo seu nome.
     */
    // public function getRoleByName(string $roleName): ?Role;

    /**
     * Salva um novo relacionamento entre usuário e role.
     */
    // public function saveUserRole(UserRole $userRole): bool;

    /**
     * Obtém as permissões associadas a uma role.
     *
     * @return mixed
     */
    // public function getPermissionsByRoleId(int $roleId);
}
