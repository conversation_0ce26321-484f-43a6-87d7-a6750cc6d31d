<?php

declare(strict_types=1);

namespace App\DDD\Infrastructure\Installment\Persistence\Queries;

use App\DDD\Infrastructure\Installment\Persistence\Interfaces\InstallmentQueryInterface;
use App\DDD\Infrastructure\Shared\Abstracts\Queries\AbstractQueries;

class GetBoletoAtoQueries extends AbstractQueries implements InstallmentQueryInterface
{
    public function execute(): string
    {
        return "bulk-data/v1/customer-extract-history?&includeRevokedBillReceivable=false&customerId={$this->getParams()['customerId']}&startDueDate=1900-11-30&endDueDate=2323-11-30&unitNumber={$this->getParams()['unitNumber']}";
    }
}
