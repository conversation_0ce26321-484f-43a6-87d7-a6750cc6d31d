<?php

declare(strict_types=1);

namespace App\DDD\Infrastructure\Installment\Persistence\Queries;

use App\DDD\Infrastructure\Installment\Persistence\Interfaces\InstallmentQueryInterface;
use App\DDD\Infrastructure\Shared\Abstracts\Queries\AbstractQueries;

class GetBoletoQueries extends AbstractQueries implements InstallmentQueryInterface
{
    public function execute(): string
    {
        return "v1/payment-slip-notification/?billReceivableId={$this->getParams()['billReceivableId']}&installmentId={$this->getParams()['installmentId']}";
    }
}
