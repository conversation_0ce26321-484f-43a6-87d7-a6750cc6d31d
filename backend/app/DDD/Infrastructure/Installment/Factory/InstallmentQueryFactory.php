<?php

declare(strict_types=1);

namespace App\DDD\Infrastructure\Installment\Factory;

use App\DDD\Domain\Installment\ValueObjects\InstallmentQueryTypeEnum;
use App\DDD\Infrastructure\Installment\Persistence\Interfaces\InstallmentQueryInterface;
use App\DDD\Infrastructure\Installment\Persistence\Queries\GetBoletoAtoQueries;
use App\DDD\Infrastructure\Installment\Persistence\Queries\GetBoletoQueries;
use App\DDD\Infrastructure\Installment\Persistence\Queries\GetInstallmentsQueries;
use App\DDD\Infrastructure\Shared\Abstracts\Factories\AbstractQueryFactory;
use App\DDD\Infrastructure\Shared\Interfaces\Queries\QueryFactoryInterface;

class InstallmentQueryFactory extends AbstractQueryFactory implements QueryFactoryInterface
{
    public function create($type): InstallmentQueryInterface
    {
        return match ($type) {
            InstallmentQueryTypeEnum::GET_BOLETO_ATO => new GetBoletoAtoQueries(),
            InstallmentQueryTypeEnum::GET_INSTALLMENTS => new GetInstallmentsQueries(),
            InstallmentQueryTypeEnum::GET_BOLETO => new GetBoletoQueries(),
            default => throw new \InvalidArgumentException('Invalid query type'),
        };
    }
}
