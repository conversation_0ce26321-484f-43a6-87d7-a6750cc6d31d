<?php

declare(strict_types=1);

namespace App\DDD\Infrastructure\Installment\Providers\Config;

use App\DDD\Application\Installment\Interfaces\InstallmentsServiceInterface;
use App\DDD\Application\Installment\Services\InstallmentsService;
use Illuminate\Contracts\Foundation\Application;

class InstallmentsBindings
{
    public function register(Application $app): void
    {
        // $this->registerProcessors($app);
        // $this->registerValidators($app);
        $this->registerServices($app);
        // $this->registerRepositories($app);
        // $this->registerFactories($app);
        // $this->registerEvents($app);
    }

    private function registerServices(Application $app): void
    {
        // Serviço principal de propostas
        $app->singleton(
            InstallmentsServiceInterface::class,
            InstallmentsService::class
        );
    }

    private function registerRepositories(Application $app): void
    {
        // Repositório de propostas
    }

    private function registerProcessors(Application $app): void
    {
        // Processador de propostas
    }

    private function registerValidators(Application $app): void
    {
        // Validador de propostas
    }

    // #TODO IMPLEMENTAR DEPOIS
    // private function registerFactories(Application $app): void
    // {
    //     // Fábrica de propostas
    //     $app->singleton(
    //         ProposalFactoryInterface::class,
    //         ProposalFactory::class
    //     );
    // }

    // private function registerEvents(Application $app): void
    // {
    //     // Dispatcher de eventos
    //     $app->singleton(
    //         ProposalEventDispatcherInterface::class,
    //         function ($app) {
    //             return new ProposalEventDispatcher(
    //                 $app->make('events')
    //             );
    //         }
    //     );

    //     // Registra os listeners de eventos
    //     $this->registerEventListeners($app);
    // }

    // private function registerEventListeners(Application $app): void
    // {
    //     $events = $app->make('events');

    //     $events->listen(
    //         'proposal.created',
    //         [ProposalCreatedListener::class, 'handle']
    //     );

    //     $events->listen(
    //         'proposal.updated',
    //         [ProposalUpdatedListener::class, 'handle']
    //     );

    //     $events->listen(
    //         'proposal.signed',
    //         [ProposalSignedListener::class, 'handle']
    //     );
    // }
}
