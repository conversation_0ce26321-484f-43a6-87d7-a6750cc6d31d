<?php

declare(strict_types=1);

namespace App\DDD\Infrastructure\Installment\Providers;

use App\DDD\Infrastructure\Installment\Providers\Config\InstallmentsBindings;
use App\Providers\ApiServicesProviders\BaseApiDDDServiceProvider;

class InstallmentsServiceProvider extends BaseApiDDDServiceProvider
{
    public function register(): void
    {
        parent::register();
        (new InstallmentsBindings())->register($this->app);
    }
}
