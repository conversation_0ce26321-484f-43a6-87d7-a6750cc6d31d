<?php

declare(strict_types=1);

namespace App\DDD\Infrastructure\User\Providers\Config;

use App\DDD\Application\User\Interfaces\UserServiceInterface;
use App\DDD\Application\User\Services\UserService;
use App\DDD\Domain\User\Validation\UserValidation;
use App\DDD\Infrastructure\User\Cache\UserCacheService;
use App\DDD\Infrastructure\User\Events\UserCreatedEvent;
use App\DDD\Infrastructure\User\Events\UserDeletedEvent;
use App\DDD\Infrastructure\User\Events\UserEventDispatcher;
use App\DDD\Infrastructure\User\Events\UserLoginEvent;
use App\DDD\Infrastructure\User\Events\UserUpdatedEvent;
use App\DDD\Infrastructure\User\Interfaces\UserCacheInterface;
use App\DDD\Infrastructure\User\Interfaces\UserEventDispatcherInterface;
use App\DDD\Infrastructure\User\Interfaces\UserNotificationInterface;
use App\DDD\Infrastructure\User\Interfaces\UserProcessorInterface;
use App\DDD\Infrastructure\User\Interfaces\UserRepositoryInterface;
use App\DDD\Infrastructure\User\Interfaces\UserValidationInterface;
use App\DDD\Infrastructure\User\Notifications\UserNotificationService;
use App\DDD\Infrastructure\User\Processors\UserProcessor;
use App\DDD\Infrastructure\User\Persistence\Repositories\UserRepository;
use Illuminate\Contracts\Foundation\Application;

class UserBindings
{
    /**
     * Bindings simples que não precisam de configuração adicional
     */
    protected const SIMPLE_BINDINGS = [
        UserValidationInterface::class => UserValidation::class,
    ];

    public function register(Application $app): void
    {
        $this->registerSimpleBindings($app);
        $this->registerComplexBindings($app);
        $this->registerOptionalServices($app);
    }

    private function registerSimpleBindings(Application $app): void
    {
        foreach (self::SIMPLE_BINDINGS as $interface => $concrete) {
            $app->singleton($interface, $concrete);
        }
    }

    private function registerComplexBindings(Application $app): void
    {
        $app->singleton(UserRepositoryInterface::class, function ($app) {
            return new UserRepository();
        });

        $app->singleton(UserProcessorInterface::class, function ($app) {
            return new UserProcessor(
                $app->make(UserValidationInterface::class)
            );
        });

        $app->singleton(UserServiceInterface::class, function ($app) {
            return new UserService(
                $app->make(UserRepositoryInterface::class),
                $app->make(UserValidationInterface::class),
                $app->make(UserProcessorInterface::class)
            );
        });
    }

    private function registerOptionalServices(Application $app): void
    {
        $this->registerCacheIfEnabled($app);
        $this->registerEventsIfEnabled($app);
        $this->registerNotificationsIfEnabled($app);
    }

    private function registerCacheIfEnabled(Application $app): void
    {
        if ($app['config']->get('user.cache.enabled', false)) {
            $app->singleton(UserCacheInterface::class, function ($app) {
                return new UserCacheService(
                    $app->make('cache.store') ?? $app->make('cache'),
                    $app->make('config')
                );
            });
        }
    }

    private function registerEventsIfEnabled(Application $app): void
    {
        if ($app['config']->get('user.events.enabled', false)) {
            $app->singleton(UserEventDispatcherInterface::class, function ($app) {
                return new UserEventDispatcher($app->make('events'));
            });

            $this->registerEventListeners($app);
        }
    }

    private function registerNotificationsIfEnabled(Application $app): void
    {
        if ($app['config']->get('user.notifications.enabled', false)) {
            $app->singleton(UserNotificationInterface::class, function ($app) {
                return new UserNotificationService(
                    $app->make('mail.manager'),
                    $app->make('config')
                );
            });
        }
    }

    private function resolveCacheIfAvailable(Application $app): ?UserCacheInterface
    {
        return $app->bound(UserCacheInterface::class)
            ? $app->make(UserCacheInterface::class)
            : null;
    }

    private function resolveEventDispatcherIfAvailable(Application $app): ?UserEventDispatcherInterface
    {
        return $app->bound(UserEventDispatcherInterface::class)
            ? $app->make(UserEventDispatcherInterface::class)
            : null;
    }

    private function resolveOptionalDependencies(Application $app): array
    {
        return [
            'eventDispatcher' => $this->resolveEventDispatcherIfAvailable($app),
            'cache' => $this->resolveCacheIfAvailable($app),
            'notifications' => $app->bound(UserNotificationInterface::class)
                ? $app->make(UserNotificationInterface::class)
                : null,
        ];
    }

    private function registerEventListeners(Application $app): void
    {
        if (! $app['config']->get('user.events.enabled', false)) {
            return;
        }

        $events = $app->make('events');
        $listeners = $app['config']->get('user.events.listeners', []);

        foreach ($listeners as $event => $eventListeners) {
            // Converter strings como 'user.created' para classes de evento
            $eventClass = $this->resolveEventClass($event);

            foreach ($eventListeners as $listener) {
                $events->listen($eventClass, $listener);
            }
        }
    }

    private function resolveEventClass(string $event): string
    {
        // Se já for uma classe completa, retorna-a diretamente
        if (class_exists($event)) {
            return $event;
        }

        // Mapeia strings para classes
        $eventMap = [
            'user.created' => UserCreatedEvent::class,
            'user.updated' => UserUpdatedEvent::class,
            'user.deleted' => UserDeletedEvent::class,
            'user.login' => UserLoginEvent::class,
        ];

        if (! isset($eventMap[$event])) {
            throw new \InvalidArgumentException("Evento não mapeado: {$event}");
        }

        return $eventMap[$event];
    }
}
