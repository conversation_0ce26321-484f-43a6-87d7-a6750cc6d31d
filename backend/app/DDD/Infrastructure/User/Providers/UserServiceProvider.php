<?php

declare(strict_types=1);

namespace App\DDD\Infrastructure\User\Providers;

use App\DDD\Infrastructure\User\Providers\Config\UserBindings;
use App\Providers\ApiServicesProviders\BaseApiDDDServiceProvider;

class UserServiceProvider extends BaseApiDDDServiceProvider
{
    public function register(): void
    {
        parent::register();
        (new UserBindings())->register($this->app);
    }
}
