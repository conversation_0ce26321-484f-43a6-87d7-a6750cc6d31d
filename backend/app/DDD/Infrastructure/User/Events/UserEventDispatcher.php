<?php

declare(strict_types=1);

namespace App\DDD\Infrastructure\User\Events;

use App\DDD\Infrastructure\User\Interfaces\UserEventDispatcherInterface;
use Illuminate\Contracts\Events\Dispatcher;
use Illuminate\Support\Facades\Log;

class UserEventDispatcher implements UserEventDispatcherInterface
{
    private const EVENT_NAMESPACE = 'App\DDD\Infrastructure\User\Events';

    public function __construct(
        private readonly Dispatcher $eventDispatcher
    ) {
    }

    public function dispatch(string $event, array $payload = []): void
    {
        try {
            $eventClass = $this->resolveEventClass($event);
            $eventInstance = new $eventClass($payload);

            $this->eventDispatcher->dispatch($eventInstance);
        } catch (\Exception $e) {
            Log::error("Failed to dispatch user event: {$event}", [
                'payload' => $payload,
                'error' => $e->getMessage(),
            ]);

            throw $e;
        }
    }

    public function listen(string $event, callable|array|string $listener): void
    {
        try {
            $eventClass = $this->resolveEventClass($event);
            $this->eventDispatcher->listen($eventClass, $listener);
        } catch (\Exception $e) {
            Log::error("Failed to register listener for event: {$event}", [
                'error' => $e->getMessage(),
            ]);

            throw $e;
        }
    }

    public function forget(string $event): void
    {
        try {
            $eventClass = $this->resolveEventClass($event);
            $this->eventDispatcher->forget($eventClass);
        } catch (\Exception $e) {
            Log::error("Failed to clear listeners for event: {$event}", [
                'error' => $e->getMessage(),
            ]);

            throw $e;
        }
    }

    public function userCreated(array $userData): void
    {
        $this->dispatch('UserCreatedEvent', [
            'userData' => $userData,
            'timestamp' => now(),
            'triggeredBy' => auth()->id(),
        ]);
    }

    public function userUpdated(array $userData, array $changes): void
    {
        $this->dispatch('UserUpdatedEvent', [
            'userData' => $userData,
            'changes' => $changes,
            'timestamp' => now(),
            'triggeredBy' => auth()->id(),
        ]);
    }

    public function userDeleted(array $userData): void
    {
        $this->dispatch('UserDeletedEvent', [
            'userData' => $userData,
            'timestamp' => now(),
            'triggeredBy' => auth()->id(),
        ]);
    }

    public function userLoggedIn(array $userData): void
    {
        $this->dispatch('UserLoginEvent', [
            'userData' => $userData,
            'timestamp' => now(),
            'ip' => request()->ip(),
            'userAgent' => request()->userAgent(),
        ]);
    }

    private function resolveEventClass(string $event): string
    {
        if (class_exists($event)) {
            return $event;
        }

        $eventClass = self::EVENT_NAMESPACE."\\{$event}";

        if (! class_exists($eventClass)) {
            throw new \InvalidArgumentException("Event class not found: {$eventClass}");
        }

        return $eventClass;
    }

    private function getEventName(string $eventClass): string
    {
        $parts = explode('\\', $eventClass);

        return end($parts);
    }
}
