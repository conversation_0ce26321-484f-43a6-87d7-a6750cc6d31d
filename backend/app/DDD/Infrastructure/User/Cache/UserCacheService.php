<?php

declare(strict_types=1);

namespace App\DDD\Infrastructure\User\Cache;

use App\DDD\Infrastructure\User\Interfaces\UserCacheInterface;
use Illuminate\Contracts\Cache\Repository as CacheRepository;
use Illuminate\Contracts\Config\Repository as Config;
use Illuminate\Support\Facades\Log;

class UserCacheService implements UserCacheInterface
{
    private string $prefix;

    private int $defaultTtl;

    public function __construct(
        private readonly CacheRepository $cache,
        private readonly Config $config
    ) {
        $this->prefix = 'user:';
        // $this->defaultTtl = $config->get('user.cache.ttl', 3600) ?? 3600; // 1 hora por padrão
        $this->defaultTtl = 3600; // 1 hora por padrão
    }

    public function get(string $key, mixed $default = null): mixed
    {
        $key = $this->prefix.$key;

        return $this->cache->get($key, $default);
    }

    public function put(string $key, mixed $value, ?int $ttl = null): bool
    {
        $key = $this->prefix.$key;
        $ttl = $ttl ?? $this->defaultTtl;

        try {
            $this->cache->put($key, $value, $ttl);

            return true;
        } catch (\Exception $e) {
            Log::error('Failed to store in cache', [
                'key' => $key,
                'error' => $e->getMessage(),
            ]);

            return false;
        }
    }

    public function forget(string $key): bool
    {
        $key = $this->prefix.$key;

        return $this->cache->forget($key);
    }

    public function has(string $key): bool
    {
        $key = $this->prefix.$key;

        return $this->cache->has($key);
    }

    public function remember(string $key, int $ttl, callable $callback): mixed
    {
        $key = $this->prefix.$key;
        $ttl = $ttl ?? $this->defaultTtl;

        return $this->cache->remember($key, $ttl, $callback);
    }

    public function flush(): bool
    {
        try {
            // Remove todos os itens que começam com o prefixo
            $pattern = $this->prefix.'*';
            $keys = $this->cache->get($pattern);

            foreach ($keys as $key) {
                $this->cache->forget($key);
            }

            return true;
        } catch (\Exception $e) {
            Log::error('Failed to flush cache', [
                'error' => $e->getMessage(),
            ]);

            return false;
        }
    }
}
