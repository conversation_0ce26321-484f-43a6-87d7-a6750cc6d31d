<?php

declare(strict_types=1);

namespace App\DDD\Infrastructure\User\Processors;

use App\DDD\Application\User\DTOs\CreateUserDto;
use App\DDD\Application\User\Schema\UserSchema;
use App\DDD\Infrastructure\Shared\Abstracts\Processors\AbstractProcessor;
use App\DDD\Infrastructure\Shared\Interfaces\DTO\DtoInterface;
use App\DDD\Infrastructure\User\Interfaces\UserProcessorInterface;
use App\DDD\Infrastructure\User\Interfaces\UserValidationInterface;
use Illuminate\Support\Collection;

class UserProcessor extends AbstractProcessor implements UserProcessorInterface
{
    public function __construct(
        private readonly UserValidationInterface $userValidator
    ) {
        parent::__construct($userValidator);
    }

    public function getSchemaClass(): string
    {
        return UserSchema::class;
    }

    public function getCreateDtoClass(array $data): ?DtoInterface
    {
        return new CreateUserDto($data);
    }

    public function process(array $data): DtoInterface|Collection
    {
        $data = $this->parseData($data);
        $this->validator->isValidateRulesDatabase($data);

        return new CreateUserDto($data);
    }

    // /// CONTINUAR DAQUI IMPLEMENTACAO SCHEMA

    public function parseData(array $data): array
    {
        switch ($data['attributes']['type']) {
            case 'Proposta__c':
                return [
                    'AccountId' => data_get($data, 'Conta__r.Id'),
                    'PersonContactId' => data_get($data, 'Conta__r.PersonContactId'),
                    'Name' => data_get($data, 'Conta__r.Name'),
                    'CPF__c' => data_get($data, 'Conta__r.CPF__c', null),
                    'CNPJ__c' => data_get($data, 'Conta__r.CNPJ__c', null),
                    'Email__c' => data_get($data, 'Conta__r.Email__c'),
                    'FirstName' => data_get($data, 'Conta__r.FirstName', null),
                    'LastName' => data_get($data, 'Conta__r.LastName', null),
                    'TelefoneCelular__c' => data_get($data, 'Conta__r.TelefoneCelular__c', null),
                    'TelefoneCelular2__c' => data_get($data, 'Conta__r.TelefoneCelular2__c', null),
                    'TelefoneComercial__c' => data_get($data, 'Conta__r.TelefoneComercial__c', null),
                    'TelefoneFixo__c' => data_get($data, 'Conta__r.TelefoneFixo__c', null),
                    'EmailAlternativo__c' => data_get($data, 'Conta__r.EmailAlternativo__c', null),
                    'ShippingPostalCode__c' => data_get($data, 'Conta__r.ShippingPostalCode__c', null),
                    'ShippingNeighborhood__c' => data_get($data, 'Conta__r.ShippingNeighborhood__c', null),
                    'ShippingStreet__c' => data_get($data, 'Conta__r.ShippingStreet__c', null),
                    'ShippingCity__c' => data_get($data, 'Conta__r.ShippingCity__c', null),
                    'ShippingState__c' => data_get($data, 'Conta__r.ShippingState__c', null),
                    'ShippingCountry__c' => data_get($data, 'Conta__r.ShippingCountry__c', null),
                    'MembroPatrimonioAfetacao__c' => data_get($data, 'Conta__r.MembroPatrimonioAfetacao__c', null),
                    'Sindico__c' => data_get($data, 'Conta__r.Sindico__c', false),
                    'CreatedDate' => data_get($data, 'Conta__r.CreatedDate', null),
                    'LastModifiedDate' => data_get($data, 'Conta__r.LastModifiedDate', null),
                    'created_at' => now(),
                    'updated_at' => now(),
                ];

            default:
                throw new \InvalidArgumentException('Tipo de dado não suportado');
        }
    }
}
