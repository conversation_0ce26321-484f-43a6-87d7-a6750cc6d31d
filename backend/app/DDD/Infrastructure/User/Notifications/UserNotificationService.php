<?php

declare(strict_types=1);

namespace App\DDD\Infrastructure\User\Notifications;

use App\DDD\Infrastructure\User\Interfaces\UserNotificationInterface;
use Illuminate\Contracts\Config\Repository as Config;
use Illuminate\Contracts\Mail\Factory as MailFactory;
use Illuminate\Support\Facades\Log;

class UserNotificationService implements UserNotificationInterface
{
    public function __construct(
        private readonly MailFactory $mailer,
        private readonly Config $config
    ) {
    }

    public function send(string $to, $template, array $data = []): void
    {
        Log::debug('Enviando notificação para: '.$to);

        try {
            // Verifica se $template é uma string de classe ou uma instância
            $mailInstance = is_string($template) ? new $template($data) : $template;

            $this->mailer
                ->to($to)
                ->send($mailInstance);

            Log::info('Notificação enviada com sucesso', [
                'to' => $to,
                'template' => is_string($template) ? $template : get_class($template),
            ]);
        } catch (\Exception $e) {
            Log::error('Falha ao enviar notificação', [
                'to' => $to,
                'template' => is_string($template) ? $template : get_class($template),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            throw $e;
        }
    }

    public function sendAsync(string $to, string $template, array $data = []): void
    {
        try {
            // Verifica se $template é uma string de classe ou uma instância
            $mailInstance = is_string($template) ? new $template($data) : $template;

            $this->mailer
                ->to($to)
                ->queue($mailInstance);

            Log::info('Async notification queued', [
                'to' => $to,
                'template' => $template,
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to queue notification', [
                'to' => $to,
                'template' => $template,
                'error' => $e->getMessage(),
            ]);

            throw $e;
        }
    }

    public function sendBatch(array $to, string $template, array $data = []): void
    {
        foreach ($to as $recipient) {
            $this->sendAsync($recipient, $template, $data);
        }
    }

    public function notifyUser(int $userId, string $type, array $data = []): void
    {
        // Implementação específica para diferentes tipos de notificação
        $notifications = [
            'welcome' => 'App\DDD\Infrastructure\User\Notifications\Templates\UserWelcomeEmail',
            'password_reset' => 'App\DDD\Infrastructure\User\Notifications\Templates\PasswordResetEmail',
            'profile_updated' => 'App\DDD\Infrastructure\User\Notifications\Templates\ProfileUpdatedEmail',
        ];

        if (! isset($notifications[$type])) {
            throw new \InvalidArgumentException("Notification type '{$type}' not found");
        }

        $this->sendAsync($data['email'], $notifications[$type], $data);
    }
}
