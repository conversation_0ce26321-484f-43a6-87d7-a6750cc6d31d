<?php

declare(strict_types=1);

namespace App\DDD\Infrastructure\User\Notifications\Templates;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class NewUserNotificationEmail extends Mailable
{
    use Queueable;
    use SerializesModels;

    public function __construct(
        private array $data
    ) {
    }

    public function build()
    {
        Log::debug('NewUserNotificationEmail build', $this->data);

        // resources/views/emails/user/admin-notification.blade.php
        return $this->view('emails.user.admin-notification')
            ->with([
                'user' => $this->data['user'],
                'triggered_by' => $this->data['triggered_by'],
                'timestamp' => $this->data['timestamp'],
            ]);
    }
}
