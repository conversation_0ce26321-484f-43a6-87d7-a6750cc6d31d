<?php

declare(strict_types=1);

namespace App\DDD\Infrastructure\User\Notifications\Templates;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class UserWelcomeEmail extends Mailable
{
    use Queueable;
    use SerializesModels;

    public function __construct(
        private array $userData
    ) {
    }

    public function build()
    {
        //  resources/views/emails/user/welcome.blade.php
        // return $this->view('emails.user.welcome')
        //             ->with([
        //                 'name' => $this->userData['name'],
        //                 'email' => $this->userData['email']
        //             ]);

        return $this->subject('Bem-vindo à Cury!')
            ->markdown('emails.user.welcome')
            ->with([
                'name' => $this->userData['name'] ?? 'Cliente',
                'userLogin' => $this->userData['userLogin'] ?? 'userLogin',
                'password' => $this->userData['password'] ?? '********',
            ]);
    }
}
