<?php

declare(strict_types=1);

namespace App\DDD\Infrastructure\User\Webhook\Handlers;

use App\DDD\Infrastructure\Shared\Interfaces\Handlers\WebhookHandlerInterface;
use App\DDD\Infrastructure\SyncData\Exceptions\MissingDataException;
use Illuminate\Foundation\Http\FormRequest;

class User<PERSON>eb<PERSON>ok<PERSON>andler implements WebhookHandlerInterface
{
    public function __construct(
        //    private readonly UserServiceInterface $userService
    ) {
    }

    public function handle(FormRequest $request): mixed
    {
        return null;
    }

    public function handleMissingData(MissingDataException $e, FormRequest $request)
    {
        //
    }
}
