<?php

declare(strict_types=1);

namespace App\DDD\Infrastructure\User\Webhook\Controllers;

use App\DDD\Infrastructure\Shared\Abstracts\Controllers\AbstractController;
use App\DDD\Infrastructure\Shared\Interfaces\Controllers\WebhookControllerInterface;
use App\DDD\Infrastructure\User\Webhook\Handlers\UserWebhookHandler;

class UserWebhookController extends AbstractController implements WebhookControllerInterface
{
    public function __construct(
        private readonly UserWebhookHandler $webhookHandler,
    ) {
    }
}
