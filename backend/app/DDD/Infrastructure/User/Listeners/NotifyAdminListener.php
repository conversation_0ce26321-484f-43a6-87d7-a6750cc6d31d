<?php

declare(strict_types=1);

namespace App\DDD\Infrastructure\User\Listeners;

use App\DDD\Infrastructure\User\Events\UserCreatedEvent;
use App\DDD\Infrastructure\User\Interfaces\UserNotificationInterface;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Support\Facades\Log;

//IMPLEMENTAR
class NotifyAdminListener implements ShouldQueue
{
    public function __construct(
        private readonly UserNotificationInterface $notificationService
    ) {
    }

    public function handle(UserCreatedEvent $event): void
    {
        try {
            $adminEmails = config('user.admin_emails', []);

            Log::info('NotifyAdminListener handle', [
                'user_id' => $event->userData,
            ]);

            foreach ($adminEmails as $adminEmail) {
                $this->notificationService->send(
                    $adminEmail,
                    'App\DDD\Infrastructure\User\Notifications\Templates\NewUserNotificationEmail',
                    [
                        'user' => $event->userData,
                        'triggered_by' => $event->triggeredBy,
                        'timestamp' => now(),
                    ]
                );
            }

            Log::info('Admin notification sent for new user', [
                'user_id' => $event->userData['id'],
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to notify admin about new user', [
                'user_id' => $event->userData['id'],
                'error' => $e->getMessage(),
            ]);
        }
    }

    public function failed(\Exception $exception): void
    {
        Log::error('NotifyAdminListener failed', [
            'error' => $exception->getMessage(),
        ]);
    }
}
