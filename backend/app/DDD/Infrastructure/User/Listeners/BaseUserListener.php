<?php

declare(strict_types=1);

namespace App\DDD\Infrastructure\User\Listeners;

use App\DDD\Infrastructure\User\Interfaces\UserCacheInterface;
use App\DDD\Infrastructure\User\Interfaces\UserNotificationInterface;
use Illuminate\Support\Facades\Log;

abstract class BaseUserListener
{
    public function __construct(
        protected ?UserNotificationInterface $notificationService = null,
        protected ?UserCacheInterface $cacheService = null
    ) {
    }

    protected function logEvent(string $eventType, array $data): void
    {
        Log::info("User event occurred: {$eventType}", [
            'data' => $data,
            'timestamp' => now(),
        ]);
    }

    protected function clearUserCache(string $userId): void
    {
        if ($this->cacheService) {
            $this->cacheService->forget("user:{$userId}");
        }
    }

    protected function sendNotification(string $template, array $data): void
    {
        if ($this->notificationService) {
            // Verifica se o template é um nome ou uma classe
            if (class_exists($template)) {
                // É uma classe
                if (isset($data['email'])) {
                    $this->notificationService->send(
                        $data['email'],
                        $template,
                        $data
                    );
                } elseif (isset($data['Email__c'])) {
                    $this->notificationService->send(
                        $data['Email__c'],
                        $template,
                        $data
                    );
                }
            } else {
                // É um tipo de notificação
                try {
                    if (isset($data['email'])) {
                        $this->notificationService->notifyUser(
                            $data['id'] ?? 0,
                            $template,
                            $data
                        );
                    } elseif (isset($data['Email__c'])) {
                        // Ajuste para campos do Salesforce
                        $data['email'] = $data['Email__c'];
                        $this->notificationService->notifyUser(
                            $data['id'] ?? 0,
                            $template,
                            $data
                        );
                    }
                } catch (\Exception $e) {
                    Log::error('Erro ao enviar notificação: '.$e->getMessage());
                }
            }
        } else {
            Log::warning('Serviço de notificação não disponível');
        }
    }
}
