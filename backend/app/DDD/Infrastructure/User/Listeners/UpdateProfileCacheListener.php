<?php

declare(strict_types=1);

namespace App\DDD\Infrastructure\User\Listeners;

use App\DDD\Infrastructure\User\Events\UserUpdatedEvent;
use App\DDD\Infrastructure\User\Interfaces\UserCacheInterface;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Support\Facades\Log;

class UpdateProfileCacheListener implements ShouldQueue
{
    public function __construct(
        private readonly UserCacheInterface $cacheService
    ) {
    }

    public function handle(UserUpdatedEvent $event): void
    {
        try {
            // Limpa o cache antigo
            $this->cacheService->forget("user:{$event->userData['id']}");

            // Atualiza o cache com os novos dados
            $this->cacheService->put(
                "user:{$event->userData['id']}",
                $event->userData,
                config('user.cache.ttl', 3600)
            );

            // Se houve mudança de email, atualiza o cache por email também
            if (isset($event->changes['email'])) {
                $this->cacheService->forget("user:email:{$event->changes['email']}");
                $this->cacheService->put(
                    "user:email:{$event->userData['email']}",
                    $event->userData,
                    config('user.cache.ttl', 3600)
                );
            }

            Log::info('User profile cache updated', [
                'user_id' => $event->userData['id'],
                'changes' => $event->changes,
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to update user profile cache', [
                'user_id' => $event->userData['id'],
                'error' => $e->getMessage(),
            ]);
        }
    }

    public function failed(\Exception $exception): void
    {
        Log::error('UpdateProfileCacheListener failed', [
            'error' => $exception->getMessage(),
        ]);
    }
}
