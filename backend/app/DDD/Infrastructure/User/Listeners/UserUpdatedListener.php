<?php

declare(strict_types=1);

namespace App\DDD\Infrastructure\User\Listeners;

use App\DDD\Infrastructure\User\Events\UserUpdatedEvent;

class UserUpdatedListener extends BaseUserListener
{
    public function handle(UserUpdatedEvent $event): void
    {
        $this->logEvent('updated', $event->userData);

        // Limpa o cache do usuário
        $this->clearUserCache($event->userData['id']);

        // Notifica sobre alterações importantes
        if ($this->hasImportantChanges($event->userData)) {
            $this->sendNotification(
                'user.profile_updated',
                $event->userData
            );
        }
    }

    private function hasImportantChanges(array $userData): bool
    {
        $importantFields = ['Name'];

        return ! empty(array_intersect(array_keys($userData['changes'] ?? []), $importantFields));
    }
}
