<?php

declare(strict_types=1);

namespace App\DDD\Infrastructure\User\Listeners;

use App\DDD\Infrastructure\User\Events\UserDeletedEvent;

class UserDeletedListener extends BaseUserListener
{
    public function handle(UserDeletedEvent $event): void
    {
        $this->logEvent('deleted', $event->userData);

        // Limpa o cache do usuário
        $this->clearUserCache($event->userData['id']);

        // Envia notificação de confirmação
        $this->sendNotification(
            'user.account_deleted',
            $event->userData
        );
    }
}
