<?php

declare(strict_types=1);

namespace App\DDD\Infrastructure\User\Listeners;

use App\DDD\Infrastructure\User\Events\UserLoginEvent;

class UserLoginListener extends BaseUserListener
{
    public function handle(UserLoginEvent $event): void
    {
        $this->logEvent('login', $event->userData);

        // Verifica se é um login de um dispositivo novo
        if ($this->isNewDevice($event->userData)) {
            $this->sendNotification(
                'user.new_device_login',
                $event->userData
            );
        }

        // Atualiza o cache com informações do último login
        if ($this->cacheService) {
            $this->cacheService->put(
                "user:{$event->userData['id']}:last_login",
                [
                    'timestamp' => now(),
                    'ip' => request()->ip(),
                    'user_agent' => request()->userAgent(),
                ],
                now()->addDays(30)->diffInSeconds()
            );
        }
    }

    private function isNewDevice(array $userData): bool
    {
        // Lógica para verificar se é um novo dispositivo
        // Pode ser implementada usando o user-agent, IP, etc.
        return true; // Implemente sua lógica aqui
    }
}
