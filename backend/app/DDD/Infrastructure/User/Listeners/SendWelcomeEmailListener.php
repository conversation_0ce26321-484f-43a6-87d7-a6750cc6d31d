<?php

declare(strict_types=1);

namespace App\DDD\Infrastructure\User\Listeners;

use App\DDD\Infrastructure\User\Events\UserCreatedEvent;
use App\DDD\Infrastructure\User\Interfaces\UserNotificationInterface;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Support\Facades\Log;

//IMPLEMENTAR
class SendWelcomeEmailListener implements ShouldQueue
{
    public function __construct(
        private readonly UserNotificationInterface $notificationService
    ) {
    }

    public function handle(UserCreatedEvent $event): void
    {
        try {
            $this->notificationService->send(
                $event->userData['email'],
                'App\DDD\Infrastructure\User\Notifications\Templates\UserWelcomeEmail',
                $event->userData
            );

            Log::info('Welcome email sent', [
                'user_id' => $event->userData['id'],
                'email' => $event->userData['email'],
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to send welcome email', [
                'user_id' => $event->userData['id'],
                'error' => $e->getMessage(),
            ]);
        }
    }

    public function failed(\Exception $exception): void
    {
        Log::error('SendWelcomeEmailListener failed', [
            'error' => $exception->getMessage(),
        ]);
    }
}
