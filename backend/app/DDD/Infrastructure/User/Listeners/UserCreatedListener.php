<?php

declare(strict_types=1);

namespace App\DDD\Infrastructure\User\Listeners;

use App\DDD\Infrastructure\User\Events\UserCreatedEvent;
use App\DDD\Infrastructure\User\Interfaces\UserCacheInterface;
use App\DDD\Infrastructure\User\Interfaces\UserNotificationInterface;
use App\DDD\Infrastructure\User\Notifications\Templates\UserWelcomeEmail;
use Illuminate\Support\Facades\Log;

class UserCreatedListener extends BaseUserListener
{
    public function __construct(
        protected ?UserNotificationInterface $notificationService = null,
        protected ?UserCacheInterface $cacheService = null
    ) {
        parent::__construct($notificationService, $cacheService);
    }

    public function handle(UserCreatedEvent $event): void
    {
        $this->logEvent('UserCreated', $event->userData);

        // Limpa cache se existir
        if (isset($event->userData['id'])) {
            $this->clearUserCache((string) $event->userData['id']);
        }

        // Verifica se temos serviço de notificação e email do usuário
        if ($this->notificationService && isset($event->userData['Email__c'])) {
            try {
                // Enviar email para o usuário (ajuste o caminho completo da classe de template)
                $this->notificationService->send(
                    $event->userData['Email__c'],
                    UserWelcomeEmail::class,
                    [
                        'name' => $event->userData['Name'] ?? $event->userData['FirstName'] ?? 'Cliente',
                        'userLogin' => $event->userData['Email__c'],
                        'password' => '******', // Senha temporária, ajuste conforme necessário
                    ]
                );

                // Enviar email para administradores
                $adminEmails = config('user.admin_emails');
                if (is_string($adminEmails)) {
                    $adminEmails = explode(',', $adminEmails);
                }

                if (! empty($adminEmails)) {
                    foreach ($adminEmails as $adminEmail) {
                        $this->notificationService->send(
                            trim($adminEmail),
                            \App\DDD\Infrastructure\User\Notifications\Templates\NewUserNotificationEmail::class,
                            [
                                'user' => $event->userData,
                                'triggered_by' => $event->triggeredBy,
                                'timestamp' => now()->toDateTimeString(),
                            ]
                        );
                    }
                }

                Log::info('Notificações de novo usuário enviadas com sucesso');
            } catch (\Exception $e) {
                Log::error('Erro ao enviar notificações de novo usuário', [
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString(),
                ]);
            }
        } else {
            Log::warning('Não foi possível enviar notificações', [
                'notification_service' => $this->notificationService ? 'disponível' : 'indisponível',
                'user_email' => $event->userData['Email__c'] ?? 'não disponível',
            ]);
        }
    }
}
