<?php

declare(strict_types=1);

namespace App\DDD\Infrastructure\User\Persistence\Repositories;

use App\DDD\Domain\User\Entities\User;
use App\DDD\Infrastructure\Shared\Abstracts\Repositories\AbstractRepository;
use App\DDD\Infrastructure\Shared\Utils\FormatDocs;
use App\DDD\Infrastructure\User\Interfaces\UserRepositoryInterface;
use function strlen;

class UserRepository extends AbstractRepository implements UserRepositoryInterface
{
    public function getModelClass(): string
    {
        return User::class;
    }

    public function exists(int|string $id): bool
    {
        $userLogin = $id;
        $user = User::query()
            ->when(
                strlen($userLogin) <= 15,
                function ($query) use ($userLogin) {
                    return $query->where('CPF__c', FormatDocs::formatCpf($userLogin));
                },
                function ($query) use ($userLogin) {
                    return $query->where('CNPJ__c', FormatDocs::formatCnpj($userLogin));
                }
            )
            ->first();

        if ($user !== null) {
            return true;
        }

        return parent::exists($id);
    }
}
