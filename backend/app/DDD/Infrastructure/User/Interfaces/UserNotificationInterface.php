<?php

declare(strict_types=1);

namespace App\DDD\Infrastructure\User\Interfaces;

interface UserNotificationInterface
{
    /**
     * Envia uma notificação de forma síncrona
     */
    public function send(string $to, string $template, array $data = []): void;

    /**
     * Envia uma notificação de forma assíncrona (queued)
     */
    public function sendAsync(string $to, string $template, array $data = []): void;

    /**
     * Envia uma notificação para múltiplos destinatários
     */
    public function sendBatch(array $to, string $template, array $data = []): void;

    /**
     * Envia uma notificação específica para usuário
     */
    public function notifyUser(int $userId, string $type, array $data = []): void;
}
