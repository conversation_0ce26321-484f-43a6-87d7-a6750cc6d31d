<?php

declare(strict_types=1);

namespace App\DDD\Infrastructure\User\Interfaces;

interface UserEventDispatcherInterface
{
    /**
     * Dispara um evento do usuário
     */
    public function dispatch(string $event, array $payload = []): void;

    /**
     * Registra um listener para um evento
     */
    public function listen(string $event, callable|array|string $listener): void;

    /**
     * Remove um listener registrado
     */
    public function forget(string $event): void;

    /**
     * Dispara evento de criação de usuário
     */
    public function userCreated(array $userData): void;

    /**
     * Dispara evento de atualização de usuário
     */
    public function userUpdated(array $userData, array $changes): void;

    /**
     * Dispara evento de exclusão de usuário
     */
    public function userDeleted(array $userData): void;

    /**
     * Dispara evento de login de usuário
     */
    public function userLoggedIn(array $userData): void;
}
