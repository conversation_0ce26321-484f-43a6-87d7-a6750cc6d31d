<?php

declare(strict_types=1);

namespace App\DDD\Infrastructure\User\Interfaces;

/**
 * Interface para o cache de usuários
 */
interface UserCacheInterface
{
    /**
     * Obtém um valor do cache
     */
    public function get(string $key, mixed $default = null): mixed;

    /**
     * Armazena um valor no cache
     */
    public function put(string $key, mixed $value, ?int $ttl = null): bool;

    /**
     * Remove um valor do cache
     */
    public function forget(string $key): bool;

    /**
     * Verifica se uma chave existe no cache
     */
    public function has(string $key): bool;

    /**
     * Obtém ou armazena um valor no cache
     */
    public function remember(string $key, int $ttl, callable $callback): mixed;

    /**
     * Limpa todo o cache de usuários
     */
    public function flush(): bool;
}
