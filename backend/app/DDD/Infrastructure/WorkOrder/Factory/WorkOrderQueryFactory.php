<?php

declare(strict_types=1);

namespace App\DDD\Infrastructure\WorkOrder\Factory;

use App\DDD\Domain\WorkOrder\ValueObjects\WorkOrderQueryTypeEnum;
use App\DDD\Infrastructure\Shared\Abstracts\Factories\AbstractQueryFactory;
use App\DDD\Infrastructure\Shared\Interfaces\Queries\QueryFactoryInterface;
use App\DDD\Infrastructure\WorkOrder\Persistence\Interfaces\WorkOrderQueryInterface;
use App\DDD\Infrastructure\WorkOrder\Persistence\Queries\GetWorkOrderByDate;
use App\DDD\Infrastructure\WorkOrder\Persistence\Queries\GetWorkOrderByRealEstateProject;

class WorkOrderQueryFactory extends AbstractQueryFactory implements QueryFactoryInterface
{
    public function create($type): WorkOrderQueryInterface
    {
        return match ($type) {
            WorkOrderQueryTypeEnum::GET_WORK_ORDERS => new GetWorkOrderByRealEstateProject(),
            WorkOrderQueryTypeEnum::GET_WORK_ORDER_DATE => new GetWorkOrderByDate(),
            default => throw new \InvalidArgumentException('Invalid query type'),
        };
    }
}
