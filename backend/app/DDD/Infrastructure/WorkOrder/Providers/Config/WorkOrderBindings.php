<?php

declare(strict_types=1);

namespace App\DDD\Infrastructure\WorkOrder\Providers\Config;

use App\DDD\Application\WorkOrder\Interfaces\WorkOrderServiceInterface;
use App\DDD\Application\WorkOrder\Services\WorkOrderService;
use App\DDD\Domain\WorkOrder\Interfaces\WorkOrderValidationInterface;
use App\DDD\Infrastructure\Shared\Abstracts\Bindings\AbstractBindings;
use App\DDD\Infrastructure\Shared\Interfaces\Bindings\BindingsInterface;
use App\DDD\Infrastructure\Shared\Services\NotificationService;
use App\DDD\Infrastructure\WorkOrder\Interfaces\WorkOrderProcessorInterface;
use App\DDD\Infrastructure\WorkOrder\Persistence\Interfaces\WorkOrderRepositoryInterface;
use App\DDD\Infrastructure\WorkOrder\Persistence\Repositories\WorkOrderRepository;
use App\DDD\Infrastructure\WorkOrder\Processors\WorkOrderProcessor;
use App\DDD\Infrastructure\WorkOrder\Validators\WorkOrderServiceValidation;
use Illuminate\Contracts\Foundation\Application;

class WorkOrderBindings extends AbstractBindings implements BindingsInterface
{

    public function registerServices(Application $app): void
    {
        // Binding do repositório
        $app->singleton(
            WorkOrderRepositoryInterface::class,
            WorkOrderRepository::class
        );

        // Binding do processor
        $app->singleton(
            WorkOrderProcessorInterface::class,
            WorkOrderProcessor::class
        );

        // Serviço principal de WorkOrder
        $app->singleton(
            WorkOrderServiceInterface::class,
            function ($app) {
                return new WorkOrderService(
                    $app->make(WorkOrderRepositoryInterface::class),
                    $app->make(WorkOrderValidationInterface::class),
                    $app->make(WorkOrderProcessorInterface::class),
                );
            }
        );
    }


    public function registerValidators(Application $app): void
    {
        $app->singleton(
            WorkOrderValidationInterface::class,
            function ($app) {
                return new WorkOrderServiceValidation($app->make(NotificationService::class));
            }
        );
    }

}
