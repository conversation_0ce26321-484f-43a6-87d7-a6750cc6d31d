<?php

declare(strict_types=1);

namespace App\DDD\Infrastructure\WorkOrder\Providers;

use App\DDD\Infrastructure\WorkOrder\Providers\Config\WorkOrderBindings;
use App\Providers\ApiServicesProviders\BaseApiDDDServiceProvider;

class WorkOrderServiceProvider extends BaseApiDDDServiceProvider
{
    public function register(): void
    {
        parent::register();
        (new WorkOrderBindings())->register($this->app);
    }
}
