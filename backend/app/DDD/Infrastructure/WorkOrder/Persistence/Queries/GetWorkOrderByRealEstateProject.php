<?php

declare(strict_types=1);

namespace App\DDD\Infrastructure\WorkOrder\Persistence\Queries;

use App\DDD\Infrastructure\Shared\Abstracts\Queries\AbstractQueries;
use App\DDD\Infrastructure\WorkOrder\Persistence\Interfaces\WorkOrderQueryInterface;

class GetWorkOrderByRealEstateProject extends AbstractQueries implements WorkOrderQueryInterface
{
    public function execute(): string
    {
        $now = now()->format('Y-m-d\TH:i:s\Z');

        return "SELECT
            Id ,
            CaseId ,
            OwnerId ,
            ServiceTerritoryId ,
            WorkOrderNumber ,
            CreatedDate ,
            CreatedById ,
            LastModifiedDate ,
            LastModifiedById ,
            AccountId ,
            AssetId ,
            Description ,
            StartDate ,
            EndDate ,
            Subject ,
            Status
        FROM
            WorkOrder
        WHERE
            NomeEmpreendimento__c='{$this->getParams()['NomeEmpreendimento__c']}'
        AND
            StartDate > {$now} and Status != 'Cancelado'
        AND
            (Subject='Assistência Técnica - Unidade'
            OR
            Subject='Assistência Técnica - Área Comum')";
    }
}
