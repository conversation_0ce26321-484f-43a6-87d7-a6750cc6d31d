<?php

declare(strict_types=1);

namespace App\DDD\Infrastructure\WorkOrder\Persistence\Queries;

use App\DDD\Infrastructure\Shared\Abstracts\Queries\AbstractQueries;
use App\DDD\Infrastructure\WorkOrder\Persistence\Interfaces\WorkOrderQueryInterface;
use Illuminate\Support\Facades\Log;

class GetWorkOrderByDate extends AbstractQueries implements WorkOrderQueryInterface
{
    public function execute(): string
    {
        return "SELECT
            Id ,
            StartDate
        FROM
            WorkOrder
        WHERE
            NomeEmpreendimento__c='{$this->getParams()['NomeEmpreendimento__c']}'
        AND
            StartDate = {$this->getParams()['startDate']}
        AND
            Subject='Assistência Técnica - Unidade'";
    }
}
