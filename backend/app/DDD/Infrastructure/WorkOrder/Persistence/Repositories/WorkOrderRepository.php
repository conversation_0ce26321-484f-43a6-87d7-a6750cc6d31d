<?php

declare(strict_types=1);

namespace App\DDD\Infrastructure\WorkOrder\Persistence\Repositories;

use App\DDD\Domain\WorkOrder\Entities\WorkOrder;
use App\DDD\Infrastructure\Shared\Abstracts\Repositories\AbstractRepository;
use App\DDD\Infrastructure\WorkOrder\Persistence\Interfaces\WorkOrderRepositoryInterface;

class WorkOrderRepository extends AbstractRepository implements WorkOrderRepositoryInterface
{
    public function getModelClass(): string
    {
        return WorkOrder::class;
    }
}
