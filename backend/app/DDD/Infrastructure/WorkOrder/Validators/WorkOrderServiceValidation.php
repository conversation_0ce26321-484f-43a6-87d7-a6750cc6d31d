<?php

declare(strict_types=1);

namespace App\DDD\Infrastructure\WorkOrder\Validators;

use App\DDD\Application\WorkOrder\Schema\WorkOrderSchema;
use App\DDD\Domain\WorkOrder\Interfaces\WorkOrderValidationInterface;
use App\DDD\Infrastructure\Shared\Abstracts\Validators\AbstractValidator;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\ValidationException;

class WorkOrderServiceValidation extends AbstractValidator implements WorkOrderValidationInterface
{
    public function validate(array $params): bool
    {
        $validator = Validator::make([], []);
        if (! isset($params['records'])) {
            $validator->errors()->add('salesforceData', 'Missing records in Salesforce response');
        }

        if (empty($params['records'])) {
            $validator->errors()->add('salesforceData', 'No records found in Salesforce response');
        }

        if ($validator->fails()) {
            throw new ValidationException($validator);
        }

        return true;
    }

    public function getSchemaClass(): string
    {
        return WorkOrderSchema::class;
    }

}
