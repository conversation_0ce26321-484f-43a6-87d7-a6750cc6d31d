<?php

declare(strict_types=1);

namespace App\DDD\Infrastructure\WorkOrder\Processors;

use App\DDD\Application\Installment\Interfaces\InstallmentsServiceInterface;
use App\DDD\Application\WorkOrder\DTOs\CreateWorkOrderDto;
use App\DDD\Application\WorkOrder\Schema\WorkOrderSchema;
use App\DDD\Domain\WorkOrder\Interfaces\WorkOrderValidationInterface;
use App\DDD\Infrastructure\Shared\Abstracts\Processors\AbstractProcessor;
use App\DDD\Infrastructure\Shared\Interfaces\DTO\DtoInterface;
use App\DDD\Infrastructure\Shared\Interfaces\Services\ServiceLocatorInterface;
use App\DDD\Infrastructure\WorkOrder\Interfaces\WorkOrderProcessorInterface;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;

class WorkOrderProcessor extends AbstractProcessor implements WorkOrderProcessorInterface
{
    /**
     * @param  WorkOrderValidationInterface  $validator  Validador de propostas
     * @param  InstallmentsServiceInterface  $installmentsService  Processador de plantas
     * @param  ServiceLocatorInterface|null  $serviceLocator  Localizador de serviços (opcional)
     */
    public function __construct(
        private readonly WorkOrderValidationInterface $workOrderValidator,
        private readonly InstallmentsServiceInterface $installmentsService,
        ?ServiceLocatorInterface $serviceLocator = null
    ) {
        parent::__construct($workOrderValidator, $serviceLocator);
    }

    /**
     * Retorna a classe do schema a ser utilizado
     */
    public function getSchemaClass(): string
    {
        return WorkOrderSchema::class;
    }

    /**
     * Cria o DTO para a proposta
     */
    protected function createDto(array $data): DtoInterface
    {
        return new CreateWorkOrderDto($data);
    }

    public function process(array $data): Collection
    {
        try {
            $data['attributes']['type'] = 'WorkOrder';
            $processedData = $this->parseData($data);

            if (is_array($processedData) && count($processedData) > 0) {
                return collect($processedData);
            }

            return collect();
        } catch (\Exception $e) {
            Log::error('Erro no processamento principal: '.$e->getMessage(), [
                'exception' => $e,
                'data' => $data,
            ]);

            return collect();
        }
    }

}
