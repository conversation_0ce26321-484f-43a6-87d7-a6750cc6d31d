<?php

declare(strict_types=1);

namespace App\DDD\Infrastructure\RealEstateProject\Providers;

use App\DDD\Infrastructure\RealEstateProject\Providers\Config\RealEstateProjectBindings;
use App\Providers\ApiServicesProviders\BaseApiDDDServiceProvider;

class RealEstateProjectServiceProvider extends BaseApiDDDServiceProvider
{
    public function register(): void
    {
        parent::register();
        (new RealEstateProjectBindings())->register($this->app);
    }
}
