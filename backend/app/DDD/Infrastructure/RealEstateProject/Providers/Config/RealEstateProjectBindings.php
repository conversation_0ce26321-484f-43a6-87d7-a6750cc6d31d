<?php

declare(strict_types=1);

namespace App\DDD\Infrastructure\RealEstateProject\Providers\Config;

use App\DDD\Application\RealEstateProject\Interfaces\RealEstateProjectServiceInterface;
use App\DDD\Application\RealEstateProject\Services\RealEstateProjectService;
use App\DDD\Domain\RealEstateProject\Interfaces\RealEstateProjectValidationInterface;
use App\DDD\Domain\RealEstateProject\Validation\RealEstateProjectValidation;
use App\DDD\Infrastructure\Document\Interfaces\DocumentProcessorInterface;
use App\DDD\Infrastructure\RealEstateProject\Interfaces\RealEstateProjectProcessorInterface;
use App\DDD\Infrastructure\RealEstateProject\Interfaces\RealEstateProjectRepositoryInterface;
use App\DDD\Infrastructure\RealEstateProject\Persistence\Repositories\RealEstateProjectRepository;
use App\DDD\Infrastructure\RealEstateProject\Processors\RealEstateProjectProcessor;
use App\DDD\Infrastructure\Shared\Abstracts\Bindings\AbstractBindings;
use App\DDD\Infrastructure\Shared\Interfaces\Bindings\BindingsInterface;
use App\DDD\Infrastructure\Shared\Interfaces\Extractors\ImageExtractorInterface;
use App\DDD\Infrastructure\Shared\Interfaces\Extractors\VideoExtractorInterface;
use App\DDD\Infrastructure\Shared\Interfaces\Processors\CarouselImageProcessorInterface;
use App\DDD\Infrastructure\Shared\Services\NotificationService;
use Illuminate\Contracts\Foundation\Application;

class RealEstateProjectBindings extends AbstractBindings implements BindingsInterface
{
    public function registerServices(Application $app): void
    {
        // Serviço principal de contratos
        $app->singleton(
            RealEstateProjectServiceInterface::class,
            function ($app) {
                return new RealEstateProjectService(
                    $app->make(RealEstateProjectRepositoryInterface::class),
                    $app->make(RealEstateProjectValidationInterface::class),
                    $app->make(RealEstateProjectProcessorInterface::class),
                );
            }
        );
    }

    public function registerRepositories(Application $app): void
    {
        // Repositório de contratos
        $app->singleton(
            RealEstateProjectRepositoryInterface::class,
            function ($app) {
                return new RealEstateProjectRepository();
            }
        );
    }

    public function registerProcessors(Application $app): void
    {
        // Processador de contratos
        $app->singleton(
            RealEstateProjectProcessorInterface::class,
            function ($app) {
                return new RealEstateProjectProcessor(
                    $app->make(RealEstateProjectValidationInterface::class),
                    $app->make(ImageExtractorInterface::class),
                    $app->make(VideoExtractorInterface::class),
                    $app->make(CarouselImageProcessorInterface::class),
                    $app->make(DocumentProcessorInterface::class),
                );
            }
        );
    }

    public function registerValidators(Application $app): void
    {
        // Validador de contratos
        $app->singleton(
            RealEstateProjectValidationInterface::class,
            function ($app) {
                return new RealEstateProjectValidation(
                    $app->make(NotificationService::class)
                );
            }
        );
    }
}
