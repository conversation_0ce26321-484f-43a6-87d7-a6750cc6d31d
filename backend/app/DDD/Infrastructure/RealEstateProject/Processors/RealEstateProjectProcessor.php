<?php

declare(strict_types=1);

namespace App\DDD\Infrastructure\RealEstateProject\Processors;

use App\DDD\Application\RealEstateProject\DTOs\CreateRealEstateProjectDto;
use App\DDD\Application\RealEstateProject\Schema\RealEstateProjectSchema;
use App\DDD\Domain\RealEstateProject\Interfaces\RealEstateProjectValidationInterface;
use App\DDD\Infrastructure\Document\Interfaces\DocumentProcessorInterface;
use App\DDD\Infrastructure\RealEstateProject\Interfaces\RealEstateProjectProcessorInterface;
use App\DDD\Infrastructure\Shared\Abstracts\Processors\AbstractProcessor;
use App\DDD\Infrastructure\Shared\Extractors\ImageExtractor;
use App\DDD\Infrastructure\Shared\Extractors\VideoExtractor;
use App\DDD\Infrastructure\Shared\Interfaces\DTO\DtoInterface;
use App\DDD\Infrastructure\Shared\Interfaces\Services\ServiceLocatorInterface;
use App\DDD\Infrastructure\Shared\Processors\CarouselImageProcessor;
use InvalidArgumentException;

/**
 * Processador para Empreendimentos
 */
class RealEstateProjectProcessor extends AbstractProcessor implements RealEstateProjectProcessorInterface
{
    /**
     * @param  RealEstateProjectValidationInterface  $validator  Validador de empreendimentos
     * @param  ImageExtractor  $imageExtractor  Extrator de imagens
     * @param  VideoExtractor  $videoExtractor  Extrator de vídeos
     * @param  CarouselImageProcessor  $carouselImageProcessor  Processador de imagens do carrossel
     * @param  DocumentProcessorInterface  $documentProcessor  Processador de documentos
     * @param  ServiceLocatorInterface|null  $serviceLocator  Localizador de serviços (opcional)
     */
    public function __construct(
        private readonly RealEstateProjectValidationInterface $realEstateValidator,
        private readonly ImageExtractor $imageExtractor,
        private readonly VideoExtractor $videoExtractor,
        private readonly CarouselImageProcessor $carouselImageProcessor,
        private readonly DocumentProcessorInterface $documentProcessor,
        ?ServiceLocatorInterface $serviceLocator = null
    ) {
        parent::__construct($realEstateValidator, $serviceLocator);

        // Registra os serviços manualmente
        $this->serviceLocator->register('imageExtractor', $imageExtractor);
        $this->serviceLocator->register('videoExtractor', $videoExtractor);
        $this->serviceLocator->register('carouselImageProcessor', $carouselImageProcessor);
        $this->serviceLocator->register('documentsProcessor', $documentProcessor);
    }

    /**
     * Retorna a classe do schema a ser utilizado
     */
    public function getSchemaClass(): string
    {
        return RealEstateProjectSchema::class;
    }

    /**
     * Cria o DTO para o empreendimento
     */
    protected function createDto(array $data): DtoInterface
    {
        return new CreateRealEstateProjectDto($data);
    }

    /**
     * Registra as estratégias de resolução para cada tipo de objeto
     */
    public function preProcess(array $data): array
    {
        // Estratégia para Proposta__c
        $this->typeResolver->registerStrategy('Proposta__c', function (array $data) {
            $empreendimentoId = data_get($data, ['Empreendimento__c']);
            $empreendimentoData = data_get($data, ['Empreendimento__r']);

            // Se não tiver dados do empreendimento, retorna apenas o ID
            if (empty($empreendimentoData)) {
                return ['id' => $empreendimentoId, 'data' => []];
            }

            // Adiciona o ID ao objeto
            $empreendimentoData['Id'] = $empreendimentoId;

            return ['id' => $empreendimentoId, 'data' => $empreendimentoData];
        });

        // Estratégia para Empreendimento__c
        $this->typeResolver->registerStrategy('Empreendimento__c', function (array $data) {
            $empreendimentoId = data_get($data, ['Id']);

            return ['id' => $empreendimentoId, 'data' => $data];
        });

        return $data;
    }

    // /**
    //  * Processa o empreendimento e transforma em DTO
    //  */
    // public function process(array $data): DtoInterface
    // {
    //     $type = $data['attributes']['type'] ?? null;
    //     if (!$type) {
    //         throw new InvalidArgumentException('Tipo de dados não informado');
    //     }

    //     // Valida os dados do Salesforce
    //     $this->validator->isValidateSalesforceData($data);
    //     // Processa os dados
    //     $parsedData = $this->parseData($data);
    //     // Valida os dados transformados
    //     $this->validator->isValidateRulesDatabase($parsedData);
    //     // Cria e retorna o DTO
    //     return new CreateRealEstateProjectDto($parsedData);
    // }

    /**
     * Processa os dados do empreendimento conforme o tipo
     */
    public function parseData(array $data): array
    {
        $type = $data['attributes']['type'] ?? null;
        if (! $type) {
            throw new InvalidArgumentException('Tipo de dados não informado');
        }

        try {
            // Resolve o empreendimento com base no tipo
            $result = $this->typeResolver->apply($type, $data);
            $empreendimentoId = $result['id'];
            $empreendimentoData = $result['data'];

            // Se não tiver dados suficientes, retorna array vazio
            if (empty($empreendimentoId) || empty($empreendimentoData)) {
                return [];
            }

            // Processa os dados complementares
            return $this->processRealEstateProjectData($empreendimentoData, $empreendimentoId);
        } catch (InvalidArgumentException $e) {
            throw new InvalidArgumentException("Tipo de dado não suportado: {$type}");
        }
    }

    /**
     * Processa os dados do empreendimento, incluindo anexos e mídias
     *
     * @param  array  $data  Dados brutos do empreendimento
     * @param  string  $empreendimentoId  ID do empreendimento
     * @return array Dados processados
     */
    private function processRealEstateProjectData(array $data, string $empreendimentoId): array
    {
        $type = $data['attributes']['type'] ?? null;
        $this->validator->isValidateSalesforceData($data);
        $realEstateProject = $this->transformData($data, $type);

        $srcLogoEmpreendimento = data_get($data, 'LogoEmpreendimento__c');
        $logoEmpreendimento = isset($srcLogoEmpreendimento) ? $this->imageExtractor->extractFirst(
            $srcLogoEmpreendimento,
            $empreendimentoId,
            'Empreendimento__c',
            'LogoEmpreendimento__c'
        ) : null;

        $fotosEmpreendimento = $this->imageExtractor->extract(
            data_get($data, 'Fotos__c'),
            $empreendimentoId,
            'Empreendimento__c',
            'Fotos__c'
        );
        $fotosEmpreendimento = json_encode($fotosEmpreendimento);

        $videosEmpreendimento = $this->videoExtractor->extract(
            data_get($data, 'Videos__c')
        );
        $videosEmpreendimento = json_encode($videosEmpreendimento);

        $videoTourEmpreendimento = $this->videoExtractor->extract(
            data_get($data, 'Video_Tour__c')
        );
        $videoTourEmpreendimento = json_encode($videoTourEmpreendimento);

        $imgCarouselEmpreendimento = $this->carouselImageProcessor->process([
            'table' => 'Empreendimento__c',
            'Id' => $empreendimentoId,
        ]);

        $imgCarouselEmpreendimento = json_encode($imgCarouselEmpreendimento->toArray());
        $realEstateProject['EmpreendimentoId'] = $empreendimentoId;
        $type = $data['attributes']['type'] ?? null;

        $this->validator->isValidateSalesforceData($data);

        $realEstateProject['LogoEmpreendimento__c'] = $logoEmpreendimento;
        $realEstateProject['Fotos__c'] = $fotosEmpreendimento;
        $realEstateProject['Videos__c'] = $videosEmpreendimento;
        $realEstateProject['Video_Tour__c'] = $videoTourEmpreendimento;
        $realEstateProject['imgsCarrossel'] = $imgCarouselEmpreendimento;

        return $realEstateProject;
    }
}
