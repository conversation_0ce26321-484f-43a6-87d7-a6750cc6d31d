<?php

declare(strict_types=1);

namespace App\DDD\Infrastructure\RealEstateProject\Persistence\Repositories;

use App\DDD\Domain\RealEstateProject\Entities\RealEstateProject;
use App\DDD\Infrastructure\RealEstateProject\Interfaces\RealEstateProjectRepositoryInterface;
use App\DDD\Infrastructure\Shared\Abstracts\Repositories\AbstractRepository;

class RealEstateProjectRepository extends AbstractRepository implements RealEstateProjectRepositoryInterface
{
    public function getModelClass(): string
    {
        return RealEstateProject::class;
    }
}
