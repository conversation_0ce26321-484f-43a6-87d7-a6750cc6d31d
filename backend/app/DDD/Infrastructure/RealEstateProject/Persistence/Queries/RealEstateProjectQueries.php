<?php

declare(strict_types=1);

namespace App\DDD\Infrastructure\RealEstateProject\Persistence\Queries;

use App\DDD\Infrastructure\Shared\Abstracts\Queries\AbstractQueries;
use App\DDD\Infrastructure\Shared\Interfaces\Queries\QueryStrategyInterface;

//IMPLEMENTAR
class RealEstateProjectQueries extends AbstractQueries implements QueryStrategyInterface
{
    public function execute(): string
    {
        $query = 'SELECT
              Id,
              CodigoSienge__c,
              Sigma__c,
              Manual__c,
              Empreendimento_novo__c,
              SPE__r.Name,
              SPE__r.CNPJ__c,
              SPE__r.CodigoSiengeSPE__c,
              Name,
              StatusMacro__c,
              Filial__c,
              DataRealizadaHabitese__c,
              EstagioComercializacao__c,
              GOResponsavel__c,
              DataUltimaAtualizacao__c,
              InstalacoesEletricas__c,
              MobilizacaoCanteiro__c,
              InstalacoesHidraulicas__c,
              Fundacao__c,
              Pintura__c,
              Estrutura__c,
              ServicosComplementares__c,
              Alvenaria__c,
              PorcentagemFisicoAcumulado__c,
              AcabamentoInterno__c,
              AcabamentoExterno__c,
              LogoEmpreendimento__c,
              Fotos__c,
              Videos__c,
              DataAGIRealizada__c,
              UltimaAtualizacaoVideoDrone__c,
              DataUltimaAtualizacaoMidia__c,
              DataRealMatriculaIndividualizada__c,
              TerritorioServico__c,
              TerritorioServico__r.OperatingHoursId,
              TerritorioServico__r.IsActive,
              DataEntregaContratualCury__c,
              Video_Tour__c,
              Last_Data_Tour__c,
              CreatedDate,
              LastModifiedDate,
              Sindico__c,
              Sindico__r.Id,
              Sindico__r.Name,
              Sindico__r.LastName,
              Sindico__r.FirstName,
              Sindico__r.Salutation,
              Sindico__r.CodigoSienge__c,
              Sindico__r.PersonContactId,
              Sindico__r.EmailAlternativo__c,
              Sindico__r.TelefoneCelular__c,
              Sindico__r.TelefoneComercial__c,
              Sindico__r.TelefoneFixo__c,
              Sindico__r.CPF__c,
              Sindico__r.CNPJ__c,
              Sindico__r.ShippingPostalCode__c,
              Sindico__r.ShippingNeighborhood__c,
              Sindico__r.ShippingStreet__c,
              Sindico__r.ShippingCity__c,
              Sindico__r.ShippingNumber__c,
              Sindico__r.ShippingState__c,
              Sindico__r.ShippingComplement__c,
              Sindico__r.ShippingCountry__c,
              Sindico__r.MembroPatrimonioAfetacao__c,
              Sindico__r.CreatedDate,
            Sindico__r.LastModifiedDate

          FROM Empreendimento__c';

        if ($this->getParams()['Id'] !== '') {
            $query .= " WHERE Id='{$this->getParams()['Id']}'";
        }

        return $query;
    }
}
