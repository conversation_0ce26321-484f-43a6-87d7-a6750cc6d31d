<?php

declare(strict_types=1);

namespace App\DDD\Infrastructure\Asset\Processors;

use App\DDD\Application\Asset\DTOs\CreateAssetDto;
use App\DDD\Application\Asset\Schema\AssetSchema;
use App\DDD\Application\Installment\Interfaces\InstallmentsServiceInterface;
use App\DDD\Domain\Asset\Interfaces\AssetValidationInterface;
use App\DDD\Infrastructure\Asset\Interfaces\AssetProcessInterface;
use App\DDD\Infrastructure\Shared\Abstracts\Processors\AbstractProcessor;
use App\DDD\Infrastructure\Shared\Interfaces\DTO\DtoInterface;
use App\DDD\Infrastructure\Shared\Interfaces\Services\ServiceLocatorInterface;

class AssetProcessor extends AbstractProcessor implements AssetProcessInterface
{
    /**
     * @param  AssetValidationInterface  $validator  Validador de propostas
     * @param  InstallmentsServiceInterface  $installmentsService  Processador de plantas
     * @param  ServiceLocatorInterface|null  $serviceLocator  Localizador de serviços (opcional)
     */
    public function __construct(
        private readonly AssetValidationInterface $assetValidator,
        private readonly InstallmentsServiceInterface $installmentsService,
        ?ServiceLocatorInterface $serviceLocator = null
    ) {
        parent::__construct($assetValidator, $serviceLocator);
    }

    /**
     * Retorna a classe do schema a ser utilizado
     */
    public function getSchemaClass(): string
    {
        return AssetSchema::class;
    }

    /**
     * Cria o DTO para a proposta
     */
    protected function createDto(array $data): DtoInterface
    {
        return new CreateAssetDto($data);
    }

    /**
     * Processa os dados de proposta
     * Usa o parseData do pai e então inclui dados adicionais de planta
     */
    public function parseData(array $data): array
    {
        $processedData = parent::parseData($data);
        $accountId = data_get($data, 'Conta__r.CodigoSienge__c');
        $unitName = data_get($data, 'Unidade__r.Ativo__r.Name');
        $statusBoletoAto = $this->installmentsService->checkBoletoAto($accountId, $unitName);
        $processedData['statusBoletoAto'] = $statusBoletoAto;

        return $processedData;
    }
}
