<?php

declare(strict_types=1);

namespace App\DDD\Infrastructure\Asset\Persistence\Queries;

use App\DDD\Infrastructure\Shared\Abstracts\Queries\AbstractQueries;
use App\DDD\Infrastructure\Shared\Interfaces\Queries\QueryStrategyInterface;

//IMPLEMENTAR
class GetAssetRetryByProposalQueries extends AbstractQueries implements QueryStrategyInterface
{
    /**
     * Retorna a query para buscar ativos filtrados.
     */
    public function execute(): string
    {
        return "SELECT
            Id,
            ContratoVigente__c,
            Name
        FROM
            Asset
        WHERE
            Status IN ('Escriturado', 'Em distrato', 'Em cessão')
        AND
            AccountId = '{$this->getParams()['AccountId']}'
        ORDER BY CreatedDate DESC";
    }
}
