<?php

declare(strict_types=1);

namespace App\DDD\Infrastructure\Asset\Persistence\Repositories;

use App\DDD\Domain\Asset\Entities\Asset;
use App\DDD\Infrastructure\Asset\Interfaces\AssetRepositoryInterface;
use App\DDD\Infrastructure\Shared\Abstracts\Repositories\AbstractRepository;

class AssetRepository extends AbstractRepository implements AssetRepositoryInterface
{
    public function getModelClass(): string
    {
        return Asset::class;
    }
}
