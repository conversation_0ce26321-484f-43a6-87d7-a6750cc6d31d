<?php

declare(strict_types=1);

namespace App\DDD\Infrastructure\Asset\Providers\Config;

use App\DDD\Application\Asset\Interfaces\AssetServiceInterface;
use App\DDD\Application\Asset\Services\AssetService;
use App\DDD\Application\Installment\Interfaces\InstallmentsServiceInterface;
use App\DDD\Application\Installment\Services\InstallmentsService;
use App\DDD\Domain\Asset\Interfaces\AssetValidationInterface;
use App\DDD\Domain\Asset\Validation\AssetValidation;
use App\DDD\Infrastructure\Asset\Interfaces\AssetProcessInterface;
use App\DDD\Infrastructure\Asset\Interfaces\AssetRepositoryInterface;
use App\DDD\Infrastructure\Asset\Persistence\Repositories\AssetRepository;
use App\DDD\Infrastructure\Asset\Processors\AssetProcessor;
use App\DDD\Infrastructure\Shared\Abstracts\Bindings\AbstractBindings;
use App\DDD\Infrastructure\Shared\Interfaces\Bindings\BindingsInterface;
use App\DDD\Infrastructure\Shared\Services\NotificationService;
use Illuminate\Contracts\Foundation\Application;

class AssetBindings extends AbstractBindings implements BindingsInterface
{
    public function registerServices(Application $app): void
    {
        $app->singleton(
            InstallmentsServiceInterface::class,
            InstallmentsService::class
        );
        // Serviço principal de assets
        $app->singleton(
            AssetServiceInterface::class,
            function ($app) {
                return new AssetService(
                    $app->make(AssetRepositoryInterface::class),
                    $app->make(AssetValidationInterface::class),
                    $app->make(AssetProcessInterface::class)
                );
            }
        );
    }

    public function registerRepositories(Application $app): void
    {
        // Repositório de assets
        $app->singleton(
            AssetRepositoryInterface::class,
            function ($app) {
                return new AssetRepository();
            }
        );
    }

    public function registerProcessors(Application $app): void
    {
        // Processador de assets
        $app->singleton(
            AssetProcessInterface::class,
            function ($app) {
                return new AssetProcessor(
                    $app->make(AssetValidationInterface::class),
                    $app->make(InstallmentsServiceInterface::class)
                );
            }
        );
    }

    public function registerValidators(Application $app): void
    {
        // Validador de assets
        $app->singleton(
            AssetValidationInterface::class,
            function ($app) {
                return new AssetValidation(
                    $app->make(NotificationService::class)
                );
            }
        );
    }
}
