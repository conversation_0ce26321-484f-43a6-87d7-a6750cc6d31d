<?php

declare(strict_types=1);

namespace App\DDD\Infrastructure\Asset\Providers;

use App\DDD\Infrastructure\Asset\Providers\Config\AssetBindings;
use App\Providers\ApiServicesProviders\BaseApiDDDServiceProvider;

class AssetServiceProvider extends BaseApiDDDServiceProvider
{
    public function register(): void
    {
        parent::register();
        (new AssetBindings())->register($this->app);
    }
}
