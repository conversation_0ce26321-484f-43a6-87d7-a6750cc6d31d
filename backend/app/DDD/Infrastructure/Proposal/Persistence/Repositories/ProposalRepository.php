<?php

declare(strict_types=1);

namespace App\DDD\Infrastructure\Proposal\Persistence\Repositories;

use App\DDD\Domain\Proposal\Entities\Proposal;
use App\DDD\Infrastructure\Proposal\Persistence\Interfaces\ProposalRepositoryInterface;
use App\DDD\Infrastructure\Shared\Abstracts\Repositories\AbstractRepository;

class ProposalRepository extends AbstractRepository implements ProposalRepositoryInterface
{
    public function getModelClass(): string
    {
        return Proposal::class;
    }
}
