<?php

declare(strict_types=1);

namespace App\DDD\Infrastructure\Proposal\Persistence\Queries;

use App\DDD\Infrastructure\Shared\Abstracts\Queries\AbstractQueries;
use App\DDD\Infrastructure\Shared\Interfaces\Queries\QueryStrategyInterface;

//IMPLEMENTAR
class ProposalQueries extends AbstractQueries implements QueryStrategyInterface
{
    public function execute(): string
    {
        return "SELECT
            Id,
            StatusAssinatura__c,
            Name,
            Regional__c,
            StatusFinanciamento__c,

            Planta__c,
            CreatedDate,
            LastModifiedDate,
            Conta__c,
            Conta__r.PersonContactId,
            Conta__r.Sindico__c,
            Conta__r.Id,
            Conta__r.Name,
            Conta__r.FirstName,
            Conta__r.LastName,
            Conta__r.Email__c,
            Conta__r.EmailAlternativo__c,
            Conta__r.TelefoneCelular__c,
            Conta__r.TelefoneCelular2__c,
            Conta__r.TelefoneComercial__c,
            Conta__r.TelefoneFixo__c,
            Conta__r.Co<PERSON>ienge__c,
            Conta__r.CPF__c,
            Conta__r.CNPJ__c,
            Conta__r.ShippingPostalCode__c,
            Conta__r.ShippingNeighborhood__c,
            Conta__r.ShippingStreet__c,
            Conta__r.ShippingCity__c,
            Conta__r.ShippingNumber__c,
            Conta__r.ShippingState__c,
            Conta__r.ShippingComplement__c,
            Conta__r.ShippingCountry__c,
            Conta__r.MembroPatrimonioAfetacao__c,
            Conta__r.CreatedDate,
            Conta__r.LastModifiedDate,

            Empreendimento__c,
            Empreendimento__r.Id,
            Empreendimento__r.CodigoSienge__c,
            Empreendimento__r.Sigma__c,
            Empreendimento__r.Manual__c,
            Empreendimento__r.Empreendimento_novo__c,
            Empreendimento__r.SPE__r.Name,
            Empreendimento__r.SPE__r.CNPJ__c,
            Empreendimento__r.SPE__r.CodigoSiengeSPE__c,
            Empreendimento__r.Name,
            Empreendimento__r.StatusMacro__c,
            Empreendimento__r.Filial__c,
            Empreendimento__r.DataRealizadaHabitese__c,
            Empreendimento__r.EstagioComercializacao__c,
            Empreendimento__r.GOResponsavel__c,
            Empreendimento__r.DataUltimaAtualizacao__c,
            Empreendimento__r.InstalacoesEletricas__c,
            Empreendimento__r.MobilizacaoCanteiro__c,
            Empreendimento__r.InstalacoesHidraulicas__c,
            Empreendimento__r.Fundacao__c,
            Empreendimento__r.Pintura__c,
            Empreendimento__r.Estrutura__c,
            Empreendimento__r.ServicosComplementares__c,
            Empreendimento__r.Alvenaria__c,
            Empreendimento__r.PorcentagemFisicoAcumulado__c,
            Empreendimento__r.AcabamentoInterno__c,
            Empreendimento__r.AcabamentoExterno__c,
            Empreendimento__r.LogoEmpreendimento__c,
            Empreendimento__r.Fotos__c,
            Empreendimento__r.Videos__c,
            Empreendimento__r.DataAGIRealizada__c,
            Empreendimento__r.UltimaAtualizacaoVideoDrone__c,
            Empreendimento__r.DataUltimaAtualizacaoMidia__c,
            Empreendimento__r.DataRealMatriculaIndividualizada__c,
            Empreendimento__r.TerritorioServico__c,
            Empreendimento__r.TerritorioServico__r.OperatingHoursId,
            Empreendimento__r.TerritorioServico__r.IsActive,
            Empreendimento__r.DataEntregaContratualCury__c,
            Empreendimento__r.Video_Tour__c,
            Empreendimento__r.Last_Data_Tour__c,
            Empreendimento__r.CreatedDate,
            Empreendimento__r.LastModifiedDate,
            Empreendimento__r.Sindico__c,
            Empreendimento__r.Sindico__r.Id,
            Empreendimento__r.Sindico__r.Name,
            Empreendimento__r.Sindico__r.LastName,
            Empreendimento__r.Sindico__r.FirstName,
            Empreendimento__r.Sindico__r.Salutation,
            Empreendimento__r.Sindico__r.CodigoSienge__c,
            Empreendimento__r.Sindico__r.PersonContactId,
            Empreendimento__r.Sindico__r.EmailAlternativo__c,
            Empreendimento__r.Sindico__r.TelefoneCelular__c,
            Empreendimento__r.Sindico__r.TelefoneComercial__c,
            Empreendimento__r.Sindico__r.TelefoneFixo__c,
            Empreendimento__r.Sindico__r.CPF__c,
            Empreendimento__r.Sindico__r.CNPJ__c,
            Empreendimento__r.Sindico__r.ShippingPostalCode__c,
            Empreendimento__r.Sindico__r.ShippingNeighborhood__c,
            Empreendimento__r.Sindico__r.ShippingStreet__c,
            Empreendimento__r.Sindico__r.ShippingCity__c,
            Empreendimento__r.Sindico__r.ShippingNumber__c,
            Empreendimento__r.Sindico__r.ShippingState__c,
            Empreendimento__r.Sindico__r.ShippingComplement__c,
            Empreendimento__r.Sindico__r.ShippingCountry__c,
            Empreendimento__r.Sindico__r.MembroPatrimonioAfetacao__c,
            Empreendimento__r.Sindico__r.CreatedDate,
            Empreendimento__r.Sindico__r.LastModifiedDate,

            Unidade__c,
            Unidade__r.Name,
            Unidade__r.Ativo__r.Id,
            Unidade__r.Ativo__r.Name,
            Unidade__r.Ativo__r.CreatedDate,
            Unidade__r.Ativo__r.LastModifiedDate,

            Unidade__r.Ativo__r.ContratoVigente__c,
            Unidade__r.Ativo__r.ContratoVigente__r.ContractNumber,
            Unidade__r.Ativo__r.ContratoVigente__r.StatusCarteira__c,
            Unidade__r.Ativo__r.ContratoVigente__r.Status,
            Unidade__r.Ativo__r.ContratoVigente__r.DataCompra__c,
            Unidade__r.Ativo__r.ContratoVigente__r.DataChaves__c,
            Unidade__r.Ativo__r.ContratoVigente__r.CreatedDate,
            Unidade__r.Ativo__r.ContratoVigente__r.LastModifiedDate,

            Unidade__r.Ativo__r.ContratoVigente__r.ContratoComProgramaFidelidade__c,
            Unidade__r.Ativo__r.ContratoVigente__r.DataAdesaoProgramaFidelidade__c,
            Unidade__r.Ativo__r.ContratoVigente__r.DataValidacaoProgramaFidelidade__c,
            Unidade__r.Ativo__r.ContratoVigente__r.SituacaoEntrega__c,
            Unidade__r.Ativo__r.ContratoVigente__r.Campanha__r.Id,


            Repasse__r.NomeCCA__c,
            Repasse__r.AcessoriaCreditoCCA__r.TelefoneCelular__c,
            Repasse__r.Id,
            Repasse__r.Etapa__c

          FROM Proposta__c WHERE Etapa__c != 'Cancelado' AND Name = '{$this->getParams()['pv']}'";
    }
}
