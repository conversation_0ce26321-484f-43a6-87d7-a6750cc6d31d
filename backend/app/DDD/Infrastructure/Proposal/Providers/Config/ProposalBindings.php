<?php

declare(strict_types=1);

namespace App\DDD\Infrastructure\Proposal\Providers\Config;

use App\DDD\Application\Proposal\Interfaces\ProposalServiceInterface;
use App\DDD\Application\Proposal\Services\ProposalService;
use App\DDD\Domain\Document\Interfaces\DocumentValidationInterface;
use App\DDD\Domain\Proposal\Interfaces\PlantaProcessorInterface;
use App\DDD\Domain\Proposal\Interfaces\ProposalValidationInterface;
use App\DDD\Domain\Proposal\Validation\ProposalValidation;
use App\DDD\Domain\Proposal\ValueObjects\Processors\PlantaProcessor;
use App\DDD\Infrastructure\Document\Interfaces\DocumentProcessorInterface;
use App\DDD\Infrastructure\Proposal\Interfaces\ProposalProcessorInterface;
use App\DDD\Infrastructure\Proposal\Persistence\Interfaces\ProposalRepositoryInterface;
use App\DDD\Infrastructure\Proposal\Persistence\Repositories\ProposalRepository;
use App\DDD\Infrastructure\Proposal\Processors\ProposalProcessor;
use App\DDD\Infrastructure\Shared\Abstracts\Bindings\AbstractBindings;
use App\DDD\Infrastructure\Shared\Interfaces\Bindings\BindingsInterface;
use App\DDD\Infrastructure\Shared\Services\NotificationService;
use Illuminate\Contracts\Foundation\Application;

class ProposalBindings extends AbstractBindings implements BindingsInterface
{
    public function registerServices(Application $app): void
    {
        if (! $app->bound(ProposalRepositoryInterface::class)) {
            $app->singleton(
                ProposalRepositoryInterface::class,
                function ($app) {
                    return new ProposalRepository();
                }
            );
        }

        $app->singleton(
            ProposalServiceInterface::class,
            function ($app) {
                return new ProposalService(
                    $app->make(ProposalRepositoryInterface::class),
                    $app->make(ProposalValidationInterface::class),
                    $app->make(ProposalProcessorInterface::class)
                );
            }
        );
    }

    public function registerRepositories(Application $app): void
    {
        $app->singleton(
            ProposalRepositoryInterface::class,
            function ($app) {
                return new ProposalRepository();
            }
        );
    }

    public function registerProcessors(Application $app): void
    {
        $app->singleton(
            PlantaProcessorInterface::class,
            function ($app) {
                return new PlantaProcessor(
                    $app->make(DocumentValidationInterface::class),
                    $app->make(DocumentProcessorInterface::class)
                );
            }
        );
        $app->singleton(
            ProposalProcessorInterface::class,
            function ($app) {
                return new ProposalProcessor(
                    $app->make(ProposalValidationInterface::class),
                    $app->make(PlantaProcessorInterface::class)
                );
            }
        );
    }

    public function registerValidators(Application $app): void
    {
        $app->singleton(
            ProposalValidationInterface::class,
            function ($app) {
                return new ProposalValidation($app->make(NotificationService::class));
            }
        );
    }
}
