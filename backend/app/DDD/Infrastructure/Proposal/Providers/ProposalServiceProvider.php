<?php

declare(strict_types=1);

namespace App\DDD\Infrastructure\Proposal\Providers;

use App\DDD\Infrastructure\Proposal\Providers\Config\ProposalBindings;
use App\Providers\ApiServicesProviders\BaseApiDDDServiceProvider;

class ProposalServiceProvider extends BaseApiDDDServiceProvider
{
    public function register(): void
    {
        parent::register();
        (new ProposalBindings())->register($this->app);
    }
}