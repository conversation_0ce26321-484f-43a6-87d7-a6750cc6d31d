<?php

declare(strict_types=1);

namespace App\DDD\Infrastructure\Proposal\Processors;

use App\DDD\Application\Proposal\DTOs\CreateProposalDto;
use App\DDD\Application\Proposal\Schema\ProposalSchema;
use App\DDD\Domain\Proposal\Interfaces\PlantaProcessorInterface;
use App\DDD\Domain\Proposal\Interfaces\ProposalValidationInterface;
use App\DDD\Infrastructure\Proposal\Interfaces\ProposalProcessorInterface;
use App\DDD\Infrastructure\Shared\Abstracts\Processors\AbstractProcessor;
use App\DDD\Infrastructure\Shared\Interfaces\DTO\DtoInterface;
use App\DDD\Infrastructure\Shared\Interfaces\Services\ServiceLocatorInterface;
use App\DDD\Infrastructure\Shared\Services\ServiceLocator;

class ProposalProcessor extends AbstractProcessor implements ProposalProcessorInterface
{
    /**
     * @param  ProposalValidationInterface  $proposalValidator  Validador de propostas
     * @param  PlantaProcessorInterface  $plantaProcessor  Processador de plantas
     * @param  ServiceLocator|null  $serviceLocator  Localizador de serviços (opcional)
     */
    public function __construct(
        private readonly ProposalValidationInterface $proposalValidator,
        private readonly PlantaProcessorInterface $plantaProcessor,
        ?ServiceLocatorInterface $serviceLocator = null
    ) {
        parent::__construct($proposalValidator, $serviceLocator);
    }

    /**
     * Retorna a classe do schema a ser utilizado
     */
    public function getSchemaClass(): string
    {
        return ProposalSchema::class;
    }

    /**
     * Cria o DTO para a proposta
     */
    protected function createDto(array $data): DtoInterface
    {
        return new CreateProposalDto($data);
    }

    /**
     * Processa os dados de proposta
     * Usa o parseData do pai e então inclui dados adicionais de planta
     */
    public function parseData(array $data): array
    {
        $processedData = parent::parseData($data);
        $processedData['planta'] = $this->getPlantaData($data);
        // dd($processedData['planta']);
        // $plantaData = $this->getPlantaData($data);
        // $processedData['planta'] = json_encode($plantaData);

        return $processedData;
    }

    /**
     * Obtém os dados da planta relacionada à proposta
     */
    public function getPlantaData(array $data): ?array
    {
        $planta_c = data_get($data, 'Planta__c');
        if (! isset($planta_c)) {
            return null;
        }
        $data['attributes']['type'] = 'Planta__c';
        $plantaCollection = $this->plantaProcessor->process($data);

        // Assumindo que o processador retorna uma coleção com pelo menos um item
        if ($plantaCollection->isEmpty()) {
            return null;
        }

        return $plantaCollection[0]->toArray();
    }
}
