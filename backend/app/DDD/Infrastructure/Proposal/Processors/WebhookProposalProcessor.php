<?php

declare(strict_types=1);

namespace App\DDD\Infrastructure\Proposal\Processors;

use App\DDD\Infrastructure\Proposal\Interfaces\ProposalProcessorInterface;

class WebhookProposalProcessor extends ProposalProcessor implements ProposalProcessorInterface
{

    /**
     * Processa os dados de proposta vindo do webhook
     */
    public function parseData(array $data): array
    {

        $processedData = $this->transformData($data, 'proposta');
        if ($this->validator) {
            $this->validator->isValidateSalesforceData($processedData);
        }

        $plantaData = $this->getPlantaData($data);
        $processedData['planta'] = json_encode($plantaData);

        return $processedData;
    }

}
