<?php

declare(strict_types=1);

namespace App\DDD\Infrastructure\Contract\Persistence\Repositories;

use App\DDD\Domain\Contract\Entities\Contract;
use App\DDD\Infrastructure\Contract\Interfaces\ContractRepositoryInterface;
use App\DDD\Infrastructure\Shared\Abstracts\Repositories\AbstractRepository;

class ContractRepository extends AbstractRepository implements ContractRepositoryInterface
{
    public function getModelClass(): string
    {
        return Contract::class;
    }
}
