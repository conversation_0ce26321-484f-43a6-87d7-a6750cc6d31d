<?php

declare(strict_types=1);

namespace App\DDD\Infrastructure\Contract\Persistence\Queries;

use App\DDD\Infrastructure\Shared\Abstracts\Queries\AbstractQueries;
use App\DDD\Infrastructure\Shared\Interfaces\Queries\QueryStrategyInterface;

//IMPLEMENTAR
class ContractQueries extends AbstractQueries implements QueryStrategyInterface
{
    public function execute(): string
    {
        $query = "SELECT
            Id,
            AccountId,
            Proposta__c,
            EmpreendimentoAtivo__c,
            Unidade__r.RootAsset.Id,
            Campanha__c,
            ContractNumber,
            StatusCarteira__c,
            Regional__c,
            Status,
            ContratoComProgramaFidelidade__c,
            DataAdesaoProgramaFidelidade__c,
            DataValidacaoProgramaFidelidade__c,
            SituacaoEntrega__c,
            DataChaves__c,
            DataCompra__c,
            CreatedDate,
            LastModifiedDate,

            Account.PersonContactId,
            Account.CodigoSienge__c,
            Account.Sindico__c,
            Account.Id,
            Account.Name,
            Account.FirstName,
            Account.LastName,
            Account.Email__c,
            Account.EmailAlternativo__c,
            Account.TelefoneCelular__c,
            Account.TelefoneCelular2__c,
            Account.TelefoneComercial__c,
            Account.TelefoneFixo__c,
            Account.CPF__c,
            Account.CNPJ__c,
            Account.ShippingPostalCode__c,
            Account.ShippingNeighborhood__c,
            Account.ShippingStreet__c,
            Account.ShippingCity__c,
            Account.ShippingNumber__c,
            Account.ShippingState__c,
            Account.ShippingComplement__c,
            Account.ShippingCountry__c,
            Account.MembroPatrimonioAfetacao__c,

            Proposta__r.Id,
            Proposta__r.StatusAssinatura__c,
            Proposta__r.Name,
            Proposta__r.Regional__c,
            Proposta__r.StatusFinanciamento__c,
            Proposta__r.Planta__c,
            Proposta__r.Planta__r.Name,
            Proposta__r.Repasse__r.NomeCCA__c,
            Proposta__r.Repasse__r.AcessoriaCreditoCCA__r.TelefoneCelular__c,
            Proposta__r.Repasse__r.Id,
            Proposta__r.Repasse__r.Etapa__c,

            Unidade__r.Name,
            Unidade__r.RootAssetId,
            Unidade__r.RootAsset.Name,
            Unidade__r.RootAsset.CreatedDate,
            Unidade__r.RootAsset.LastModifiedDate
        FROM Contract
        WHERE (Status = 'Escriturado' OR Status = 'Em distrato' OR Status = 'Em cessão')";

        if ($this->params['ContractNumber'] !== '') {
            $query .= " AND ContractNumber = '{$this->params['ContractNumber']}'";
        } else {
            $query .= ' LIMIT 100';
        }

        return $query;
    }

    public function getQueryByAccountAsset(string $accountId, string $assetId): string
    {
        $query = "SELECT
            Id,
            AccountId,
            Proposta__c,
            EmpreendimentoAtivo__c,
            Unidade__r.RootAsset.Id,
            Campanha__c,
            ContractNumber,
            StatusCarteira__c,
            Regional__c,
            Status,
            ContratoComProgramaFidelidade__c,
            DataAdesaoProgramaFidelidade__c,
            DataValidacaoProgramaFidelidade__c,
            SituacaoEntrega__c,
            DataChaves__c,
            DataCompra__c,
            CreatedDate,
            LastModifiedDate,

            Account.PersonContactId,
            Account.CodigoSienge__c,
            Account.Sindico__c,
            Account.Id,
            Account.Name,
            Account.FirstName,
            Account.LastName,
            Account.Email__c,
            Account.EmailAlternativo__c,
            Account.TelefoneCelular__c,
            Account.TelefoneCelular2__c,
            Account.TelefoneComercial__c,
            Account.TelefoneFixo__c,
            Account.CPF__c,
            Account.CNPJ__c,
            Account.ShippingPostalCode__c,
            Account.ShippingNeighborhood__c,
            Account.ShippingStreet__c,
            Account.ShippingCity__c,
            Account.ShippingNumber__c,
            Account.ShippingState__c,
            Account.ShippingComplement__c,
            Account.ShippingCountry__c,
            Account.MembroPatrimonioAfetacao__c,

            Proposta__r.Id,
            Proposta__r.StatusAssinatura__c,
            Proposta__r.Name,
            Proposta__r.Regional__c,
            Proposta__r.StatusFinanciamento__c,
            Proposta__r.Planta__c,
            Proposta__r.Planta__r.Name,
            Proposta__r.Repasse__r.NomeCCA__c,
            Proposta__r.Repasse__r.AcessoriaCreditoCCA__r.TelefoneCelular__c,
            Proposta__r.Repasse__r.Id,
            Proposta__r.Repasse__r.Etapa__c,

            Unidade__r.Name,
            Unidade__r.RootAssetId,
            Unidade__r.RootAsset.Name
        FROM Contract
        WHERE AccountId = '{$accountId}' AND Unidade__r.RootAssetId = '{$assetId}'";

        return $query;
    }

    public function getQueryById(string $id): string
    {
        return "SELECT
                AccountId,
                Proposta__c,
                Unidade__c,
                Campanha__c,
                StatusCarteira__c,
                SituacaoEntrega__c,
                Status,
                ContratoComProgramaFidelidade__c,
                DataAdesaoProgramaFidelidade__c,
                DataValidacaoProgramaFidelidade__c,
                DataChaves__c,
                DataCompra__c,
                EmpreendimentoAtivo__c
            FROM Contract
            WHERE Id = '{$id}'";
    }
}
