<?php

declare(strict_types=1);

namespace App\DDD\Infrastructure\Contract\Processors;

use App\DDD\Application\Contract\DTOs\CreateContractDto;
use App\DDD\Application\Contract\Schema\ContractSchema;
use App\DDD\Domain\Contract\Interfaces\ContractValidationInterface;
use App\DDD\Infrastructure\Contract\Interfaces\ContractProcessorInterface;
use App\DDD\Infrastructure\External\Salesforce\Interfaces\SalesforceServiceInterface;
use App\DDD\Infrastructure\Shared\Abstracts\Processors\AbstractProcessor;
use App\DDD\Infrastructure\Shared\Interfaces\DTO\DtoInterface;
use App\DDD\Infrastructure\Shared\Interfaces\Services\ServiceLocatorInterface;
use App\DDD\Infrastructure\Shared\Services\TypeResolver;
use InvalidArgumentException;

class ContractProcessor extends AbstractProcessor implements ContractProcessorInterface
{
    /**
     * @param  ContractValidationInterface  $validator  Validador para os dados
     * @param  SalesforceServiceInterface  $salesforceService  Serviço de comunicação com Salesforce
     * @param  ServiceLocatorInterface|null  $serviceLocator  Localizador de serviços (opcional)
     */
    public function __construct(
        ContractValidationInterface $validator,
        SalesforceServiceInterface $salesforceService,
        ?ServiceLocatorInterface $serviceLocator = null
    ) {
        parent::__construct($validator, $serviceLocator);

        // Registra o serviço do Salesforce manualmente, pois foi injetado
        $this->serviceLocator->register('salesforceService', $salesforceService);
    }

    /**
     * Retorna a classe do schema a ser utilizado
     */
    public function getSchemaClass(): string
    {
        return ContractSchema::class;
    }

    /**
     * Cria o DTO para a proposta
     */
    protected function createDto(array $data): DtoInterface
    {
        return new CreateContractDto($data);
    }

    /**
     * Registra as estratégias para cada tipo de objeto
     */
    public function preProcess(array $data): array
    {
        // Estratégia para Proposta__c
        $this->typeResolver->registerStrategy('Proposta__c', function (array $data) {
            return $data['Unidade__r.Ativo__r.ContratoVigente__c'];
        });

        // Estratégia para Empreendimento__c
        $this->typeResolver->registerStrategy('Contract', function (array $data) {
            return $data['Id'];
        });

        return $data;
    }

    /**
     * Processa os dados de entrada conforme o tipo
     */
    // public function parseData(array $data): array
    // {
    //     $type = $data['attributes']['type'] ?? null;
    //     if (!$type) {
    //         throw new InvalidArgumentException('Tipo de dados não informado');
    //     }

    //     try {
    //         // Resolve os IDs de operating hours baseado no tipo
    //         $contractInfo = $this->typeResolver->apply($type, $data);

    //         //Caso não exista o operating hours, retorna um array vazio
    //         if ($operatingHoursInfo['operatingHoursId'] === null) {
    //             return [];
    //         }

    //         // Carrega os serviços de agendamento
    //         $scheduleServicesData = $this->getScheduleServices($operatingHoursInfo['operatingHoursId']);
    //         $records = $scheduleServicesData['records'] ?? null;

    //         if ($records === null) {
    //             return [];
    //         }

    //         return $this->processScheduleRecords($records, $operatingHoursInfo['empreendimentoId'], $type);
    //     } catch (InvalidArgumentException $e) {
    //         // Se o tipo não for suportado, lança exceção informativa
    //         throw new InvalidArgumentException("Tipo de dado não suportado: {$type}");
    //     }
    // }
}
