<?php

declare(strict_types=1);

namespace App\DDD\Infrastructure\Contract\Providers\Config;

use App\DDD\Application\Contract\Interfaces\ContractServiceInterface;
use App\DDD\Application\Contract\Services\ContractService;
use App\DDD\Domain\Contract\Interfaces\ContractValidationInterface;
use App\DDD\Domain\Contract\Validation\ContractValidation;
use App\DDD\Infrastructure\Contract\Interfaces\ContractProcessorInterface;
use App\DDD\Infrastructure\Contract\Interfaces\ContractRepositoryInterface;
use App\DDD\Infrastructure\Contract\Persistence\Repositories\ContractRepository;
use App\DDD\Infrastructure\Contract\Processors\ContractProcessor;
use App\DDD\Infrastructure\External\Salesforce\Interfaces\SalesforceServiceInterface;
use App\DDD\Infrastructure\Shared\Abstracts\Bindings\AbstractBindings;
use App\DDD\Infrastructure\Shared\Interfaces\Bindings\BindingsInterface;
use App\DDD\Infrastructure\Shared\Services\NotificationService;
use Illuminate\Contracts\Foundation\Application;

class ContractBindings extends AbstractBindings implements BindingsInterface
{
    public function registerServices(Application $app): void
    {
        // Serviço principal de contratos
        $app->singleton(
            ContractServiceInterface::class,
            function ($app) {
                return new ContractService(
                    $app->make(ContractRepositoryInterface::class),
                    $app->make(ContractValidationInterface::class),
                    $app->make(ContractProcessorInterface::class),
                );
            }
        );
    }

    public function registerRepositories(Application $app): void
    {
        // Repositório de contratos
        $app->singleton(
            ContractRepositoryInterface::class,
            function ($app) {
                return new ContractRepository();
            }
        );
    }

    public function registerProcessors(Application $app): void
    {
        // Processador de contratos
        $app->singleton(
            ContractProcessorInterface::class,
            function ($app) {
                return new ContractProcessor(
                    $app->make(ContractValidationInterface::class),
                    $app->make(SalesforceServiceInterface::class),
                );
            }
        );
    }

    public function registerValidators(Application $app): void
    {
        // Validador de contratos
        $app->singleton(
            ContractValidationInterface::class,
            function ($app) {
                return new ContractValidation(
                    $app->make(NotificationService::class)
                );
            }
        );
    }
}
