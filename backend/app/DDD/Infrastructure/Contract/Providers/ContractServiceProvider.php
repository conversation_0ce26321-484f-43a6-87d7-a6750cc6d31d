<?php

declare(strict_types=1);

namespace App\DDD\Infrastructure\Contract\Providers;

use App\DDD\Infrastructure\Contract\Providers\Config\ContractBindings;
use App\Providers\ApiServicesProviders\BaseApiDDDServiceProvider;

class ContractServiceProvider extends BaseApiDDDServiceProvider
{
    public function register(): void
    {
        parent::register();
        (new ContractBindings())->register($this->app);
    }
}
