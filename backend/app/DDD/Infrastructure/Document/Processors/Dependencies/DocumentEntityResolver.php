<?php

declare(strict_types=1);

namespace App\DDD\Infrastructure\Document\Processors\Dependencies;

use Illuminate\Support\Collection;

/**
 * Classe auxiliar para resolver IDs de entidades relacionadas a documentos
 */
class DocumentEntityResolver
{
    /**
     * @var array Dados de contexto
     */
    private array $contextData;

    /**
     * Define os dados de contexto para resolução de IDs
     *
     * @param  array  $data  Dados de contexto
     */
    public function setContextData(array $data): self
    {
        $this->contextData = $data;

        return $this;
    }

    /**
     * Formata IDs internos de documentos para uso em consultas SQL
     *
     * @param  array  $idDocs  Documentos com IDs internos
     * @return string String formatada de IDs no formato (id1,id2,id3)
     */
    public function formatInternalDocumentIds(array $idDocs): string
    {
        $ids = Collection::make($idDocs)
            ->pluck('IdInterno__c')
            ->filter()
            ->map(fn ($id) => "'{$id}'")
            ->join(',');

        return ! empty($ids) ? "({$ids})" : "('')";
    }

    /**
     * Obtém um array de IDs de documentos internos
     *
     * @param  array  $idDocs  Documentos com IDs internos
     * @return array Lista de IDs internos
     */
    public function getInternalDocumentIds(array $idDocs): array
    {
        return Collection::make($idDocs)
            ->pluck('IdInterno__c')
            ->filter()
            ->all();
    }

    /**
     * Obtém o ID do ativo (asset) com base no tipo de documento
     *
     * @param  string  $documentType  Tipo de documento
     * @return string|null ID do ativo
     */
    public function getAssetId(string $documentType): ?string
    {
        switch ($documentType) {
            case 'Proposta__c':
                return data_get($this->contextData, 'Unidade__r.Ativo__r.Id');
            case 'Contract':
                return data_get($this->contextData, 'Unidade__r.RootAssetId');
            default:
                return null;
        }
    }

    /**
     * Obtém o ID da conta com base no tipo de documento
     *
     * @param  string  $documentType  Tipo de documento
     * @return string|null ID da conta
     */
    public function getAccountId(string $documentType): ?string
    {
        switch ($documentType) {
            case 'Account':
                return data_get($this->contextData, 'Id');
            case 'Proposta__c':
                return data_get($this->contextData, 'Conta__c');
            case 'Contract':
                return data_get($this->contextData, 'AccountId');
            default:
                return null;
        }
    }

    /**
     * Obtém o ID da proposta com base no tipo de documento
     *
     * @param  string  $documentType  Tipo de documento
     * @return string|null ID da proposta
     */
    public function getProposalId(string $documentType): ?string
    {
        switch ($documentType) {
            case 'Proposta__c':
                return data_get($this->contextData, 'Id');
            default:
                return null;
        }
    }

    /**
     * Obtém o ID do contrato com base no tipo de documento
     *
     * @param  string  $documentType  Tipo de documento
     * @return string|null ID do contrato
     */
    public function getContractId(string $documentType): ?string
    {
        switch ($documentType) {
            case 'Contract':
                return data_get($this->contextData, 'Id');
            default:
                return null;
        }
    }

    /**
     * Obtém o ID do empreendimento com base no tipo de documento
     *
     * @param  string  $documentType  Tipo de documento
     * @return string|null ID do empreendimento
     */
    public function getEmpreendimentoId(string $documentType): ?string
    {
        switch ($documentType) {
            case 'Empreendimento__c':
                return data_get($this->contextData, 'Id');
            default:
                return null;
        }
    }
}
