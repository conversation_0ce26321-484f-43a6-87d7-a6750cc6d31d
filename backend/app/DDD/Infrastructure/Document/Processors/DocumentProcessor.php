<?php

declare(strict_types=1);

namespace App\DDD\Infrastructure\Document\Processors;

use App\DDD\Application\Document\DTOs\CreateDocumentDto;
use App\DDD\Application\Document\DTOs\UploadFileDocumentDto;
use App\DDD\Application\Document\Interfaces\DocumentServiceInterface;
use App\DDD\Application\Document\Schema\DocumentSchema;
use App\DDD\Domain\Document\Interfaces\DocumentValidationInterface;
use App\DDD\Domain\Document\Services\DocumentQueriesService;
use App\DDD\Domain\Document\ValueObjects\DocumentQueryTypeEnum;
use App\DDD\Infrastructure\Document\Factory\DocumentQueryFactory;
use App\DDD\Infrastructure\Document\Interfaces\DocumentProcessorInterface;
use App\DDD\Infrastructure\Document\Processors\Dependencies\DocumentEntityResolver;
use App\DDD\Infrastructure\External\Salesforce\Interfaces\SalesforceServiceInterface;
use App\DDD\Infrastructure\Shared\Abstracts\Processors\AbstractProcessor;
use App\DDD\Infrastructure\Shared\Interfaces\DTO\DtoInterface;
use App\DDD\Infrastructure\Shared\Interfaces\Services\storageServiceInterface;
use App\DDD\Infrastructure\Shared\Services\ServiceLocator;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;

/**
 * Processador para documentos
 */
class DocumentProcessor extends AbstractProcessor implements DocumentProcessorInterface
{
    /**
     * @var array Dados atuais sendo processados
     */
    private array $data = [];

    /**
     * @var bool Indica se deve usar fila para upload
     */
    private bool $useQueue = true;

    /**
     * @var bool Indica se deve criar imagens dos documentos
     */
    private bool $createImage = false;

    /**
     * @var DocumentQueryFactory Fábrica de consultas de documentos
     */
    private DocumentQueryFactory $queryFactory;

    /**
     * @var DocumentServiceInterface|null Serviço de documentos
     */
    private ?DocumentServiceInterface $documentService = null;

    /**
     * Construtor
     *
     * @param  DocumentValidationInterface|null  $validator  Validador de documentos
     * @param  storageServiceInterface  $storageService  Serviço de arquivos
     * @param  SalesforceServiceInterface  $salesforceService  Serviço do Salesforce
     * @param  DocumentEntityResolver  $entityResolver  Resolvedor de entidades
     * @param  ServiceLocator|null  $serviceLocator  Localizador de serviços (opcional)
     */
    public function __construct(
        ?DocumentValidationInterface $validator,
        private readonly storageServiceInterface $storageService,
        private readonly SalesforceServiceInterface $salesforceService,
        private readonly DocumentEntityResolver $entityResolver,
        ?ServiceLocator $serviceLocator = null
    ) {
        parent::__construct($validator, $serviceLocator);

        $this->queryFactory = new DocumentQueryFactory();

        // Registra serviços no localizador de serviços
        if ($serviceLocator) {
            $serviceLocator->register('storageService', $storageService);
            $serviceLocator->register('salesforceService', $salesforceService);
        }
    }

    /**
     * Define o serviço de documentos.
     * Este método é usado para quebrar a dependência cíclica.
     */
    public function setDocumentService(DocumentServiceInterface $documentService): void
    {
        $this->documentService = $documentService;
    }

    /**
     * @inheritdoc
     */
    public function getSchemaClass(): string
    {
        return DocumentSchema::class;
    }

    /**
     * @inheritdoc
     */
    public function setUseQueue(bool $useQueue): self
    {
        $this->useQueue = $useQueue;

        return $this;
    }

    /**
     * @inheritdoc
     */
    public function setCreateImage(bool $createImage): self
    {
        $this->createImage = $createImage;

        return $this;
    }

    /**
     * @inheritdoc
     */
    public function isValidImage(string $url): bool
    {
        // Implementação básica - pode ser expandida conforme necessário
        $headers = get_headers($url, 1);

        return isset($headers['Content-Type']) && str_contains($headers['Content-Type'], 'image');
    }

    /**
     * @inheritdoc
     */
    public function process(array $data): Collection
    {
        try {
            $processedDocs = $this->parseData($data);

            if (is_array($processedDocs) && count($processedDocs) > 0) {
                return collect($processedDocs);
            }

            return collect();
        } catch (\Exception $e) {
            Log::error('Erro no processamento principal: '.$e->getMessage(), [
                'exception' => $e,
                'data' => $data,
            ]);

            return collect();
        }
    }

    /**
     * @inheritdoc
     */
    public function parseData(array $data): array
    {

        try {
            $this->data = $data;

            // Configura o resolvedor de entidades com os dados de contexto
            $this->entityResolver->setContextData($this->data);

            // Valida entrada de dados
            if (! isset($this->data['attributes']['type']) || ! isset($this->data['Id'])) {
                Log::warning('Dados insuficientes para processamento de documentos', [
                    'data' => $this->data,
                ]);

                return [];
            }

            $params = [
                'table' => $this->data['attributes']['type'],
                'id' => $this->data['Id'],
            ];

            // Configura o processamento de imagens apenas para plantas
            if ($this->data['attributes']['type'] === 'Planta__c') {
                $this->createImage = true;
            } else {
                $this->createImage = false;
            }

            // Obtém os documentos
            $documents = $this->getDocuments($params);

            $records = data_get($documents, 'records', []);

            if (empty($records)) {
                return [];
            }

            // Processa os documentos
            return $this->processDocumentRecords($records, $params);
        } catch (\Exception $e) {
            Log::error('Erro ao processar documentos: '.$e->getMessage(), [
                'exception' => $e,
                'data' => $data,
            ]);

            return [];
        }
    }

    /**
     * Processa os registros de documentos
     *
     * @param  array  $records  Registros de documentos
     * @param  array  $params  Parâmetros de contexto
     * @return array Array de DTOs de documentos processados
     */
    private function processDocumentRecords(array $records, array $params): array
    {
        $processedDocs = [];
        $idDocs = [];

        foreach ($records as $document) {
            try {
                // Verifica se o validator existe antes de usá-lo
                if ($this->validator) {
                    $this->validator->isValidateSalesforceData($document);
                }

                if (isset($document['Link__c']) && $document['Link__c'] !== null) {
                    $processedDoc = $this->processLinkedDocument($document, $params);
                    if ($processedDoc) {
                        $processedDocs[] = $processedDoc;
                    }
                } else {
                    $idDocs[] = $this->formatDocumentData($document, '');
                }
            } catch (\Exception $e) {
                Log::warning('Erro ao processar documento: '.$e->getMessage(), [
                    'documentId' => $document['Id'] ?? 'unknown',
                    'exception' => $e,
                ]);
            }
        }

        // Processa documentos internos se necessário
        if (! empty($idDocs)) {
            try {
                // Utiliza o processamento direto sem depender de consultas adicionais
                $internalDocs = $this->processDocumentsDirectly($idDocs, $params);
                if (! empty($internalDocs)) {
                    $processedDocs = array_merge($processedDocs, $internalDocs);
                }
            } catch (\Exception $e) {
                Log::error('Erro ao processar documentos internos: '.$e->getMessage(), [
                    'exception' => $e,
                ]);
            }
        }

        return $processedDocs;
    }

    /**
     * Resolve os dados do documento para criar um DTO
     *
     * @param  array  $documentData  Dados do documento
     * @return DtoInterface DTO do documento
     */
    private function resolveDataDocument(array $documentData): DtoInterface
    {
        try {
            $document = $this->extractDocumentData($documentData);

            // Verifica se o validator existe antes de usá-lo
            if ($this->validator) {
                $this->validator->isValidateRulesDatabase($document);
            }

            return new CreateDocumentDto($document);
        } catch (\Exception $e) {
            Log::error('Erro ao resolver dados do documento: '.$e->getMessage(), [
                'documentId' => $documentData['Id'] ?? 'unknown',
                'exception' => $e,
            ]);

            // Se ocorrer um erro, tenta criar o DTO com os dados originais
            return new CreateDocumentDto($documentData);
        }
    }

    /**
     * Extrai dados do documento com base no tipo de documento
     *
     * @param  array  $documentData  Dados brutos do documento
     * @return array Dados estruturados do documento
     */
    private function extractDocumentData(array $documentData): array
    {
        $documentType = $this->data['attributes']['type'];

        $result = [
            'DocumentId' => $documentData['Id'],
            'AssetId' => $this->entityResolver->getAssetId($documentType),
            'AccountId' => $this->entityResolver->getAccountId($documentType),
            'EmpreendimentoId' => $this->entityResolver->getEmpreendimentoId($documentType),
            'ContractId' => $this->entityResolver->getContractId($documentType),
            'ProposalId' => $this->entityResolver->getProposalId($documentType),
            'Subgrupo__c' => $documentData['Subgrupo__c'],
            'TituloArquivo__c' => $documentData['TituloArquivo__c'],
            'IdInterno__c' => $documentData['IdInterno__c'],
            'Link__c' => $documentData['Link__c'],
            'VisivelCliente__c' => $documentData['VisivelCliente__c'],
            'LinkS3' => $documentData['LinkS3'] ?? null,
        ];

        // Adiciona o Name se existir, ou gera a partir do título do arquivo
        $result['Name'] = $documentData['Name'] ?? $this->storageService->processFileName($documentData['TituloArquivo__c'] ?? '');

        return $result;
    }

    /**
     * Processa um documento com link
     *
     * @param  array  $document  Dados do documento
     * @param  array  $params  Parâmetros de contexto
     * @return DtoInterface|null DTO do documento processado
     */
    private function processLinkedDocument(array $document, array $params): ?DtoInterface
    {
        try {
            $filename = $this->storageService->generateFilename($params['table'], $params['id'], $document);
            $linkS3 = $this->handleFileUploadQueue($document['Link__c'], $filename, $document['Id']);
            $documentFormattedData = $this->formatDocumentData($document, $filename, $linkS3);

            return $this->resolveDataDocument($documentFormattedData);
        } catch (\Exception $e) {
            Log::error('Erro ao processar documento com link: '.$e->getMessage(), [
                'documentId' => $document['Id'] ?? 'unknown',
                'exception' => $e,
            ]);

            return null;
        }
    }

    /**
     * Gerencia o upload de arquivo com ou sem fila
     *
     * @param  string  $file  URL ou ID do arquivo
     * @param  string  $filename  Nome do arquivo
     * @param  string  $documentId  ID do documento
     * @return string|null URL do arquivo no S3
     */
    private function handleFileUploadQueue(string $file, string $filename, string $documentId): ?string
    {
        try {
            // Verifica se o documentService está definido quando useQueue é true
            if ($this->useQueue && ! $this->documentService) {
                Log::warning('DocumentService não está definido. Upload com fila não será realizado.');

                return $this->storageService->uploadFileWithoutQueue($filename, $file, $this->createImage);
            }

            if ($this->useQueue) {
                // Usa fila para processamento assíncrono
                $this->storageService->uploadFileWithQueue(
                    $documentId,
                    $filename,
                    $file,
                    $this->documentService,
                    new UploadFileDocumentDto(['LinkS3' => null]),
                    'LinkS3',
                    $this->createImage
                );

                return null;
            } else {
                // Processamento síncrono
                return $this->storageService->uploadFileWithoutQueue($filename, $file, $this->createImage);
            }
        } catch (\Exception $e) {
            Log::error('Erro no upload de arquivo: '.$e->getMessage(), [
                'documentId' => $documentId,
                'filename' => $filename,
                'exception' => $e,
            ]);

            return null;
        }
    }

    /**
     * Formata os dados do documento
     *
     * @param  array  $document  Dados do documento
     * @param  string  $filename  Nome do arquivo
     * @param  string|null  $linkS3  URL do arquivo no S3
     * @return array Dados formatados do documento
     */
    private function formatDocumentData(array $document, string $filename, ?string $linkS3 = null): array
    {
        try {
            $tituloArquivo = data_get($document, 'TituloArquivo__c', '');
            $name = $this->storageService->processFileName($tituloArquivo);

            return [
                'Id' => $document['Id'],
                'Link__c' => $filename,
                'Subgrupo__c' => data_get($document, 'Subgrupo__c'),
                'TituloArquivo__c' => $tituloArquivo,
                'VisivelCliente__c' => data_get($document, 'VisivelCliente__c'),
                'IdInterno__c' => data_get($document, 'IdInterno__c'),
                'LinkS3' => $linkS3,
                'Name' => $name,
            ];
        } catch (\Exception $e) {
            Log::error('Erro ao formatar dados do documento: '.$e->getMessage(), [
                'documentId' => $document['Id'] ?? 'unknown',
                'exception' => $e,
            ]);

            // Retorna dados mínimos em caso de erro
            return [
                'Id' => $document['Id'] ?? 'unknown',
                'Link__c' => $filename,
                'Subgrupo__c' => data_get($document, 'Subgrupo__c', ''),
                'TituloArquivo__c' => data_get($document, 'TituloArquivo__c', ''),
                'VisivelCliente__c' => data_get($document, 'VisivelCliente__c', false),
                'IdInterno__c' => data_get($document, 'IdInterno__c', ''),
                'LinkS3' => $linkS3,
                'Name' => data_get($document, 'Name', 'documento'),
            ];
        }
    }

    /**
     * Processa documentos diretamente sem consulta adicional
     *
     * @param  array  $idDocs  Array de documentos com IDs internos
     * @param  array  $params  Parâmetros de contexto
     * @return array Array de DTOs de documentos processados
     */
    private function processDocumentsDirectly(array $idDocs, array $params): array
    {
        $processedDtos = [];

        foreach ($idDocs as $docData) {
            try {
                // Gera um nome de arquivo baseado nos dados disponíveis
                $filename = $this->generateFilenameFromDocData($docData, $params);

                // Adiciona uma URL S3 falsa apenas para fins de teste/desenvolvimento
                // Em produção, isso seria substituído pelo upload real
                $linkS3 = null;
                if (! $this->useQueue) {
                    $linkS3 = $this->generateS3Link($filename);
                }

                // Formata o documento e cria o DTO
                $formattedDocument = $this->formatDocumentData($docData, $filename, $linkS3);
                $processedDtos[] = $this->resolveDataDocument($formattedDocument);
            } catch (\Exception $e) {
                Log::error('Erro ao processar documento diretamente: '.$e->getMessage(), [
                    'exception' => $e,
                    'documentId' => $docData['Id'] ?? 'unknown',
                ]);
            }
        }

        return $processedDtos;
    }

    /**
     * Gera um nome de arquivo a partir dos dados do documento
     *
     * @param  array  $docData  Dados do documento
     * @param  array  $params  Parâmetros de contexto
     * @return string Nome do arquivo
     */
    private function generateFilenameFromDocData(array $docData, array $params): string
    {
        // Tente usar o serviço de armazenamento se disponível
        if (method_exists($this->storageService, 'generateFilename')) {
            return $this->storageService->generateFilename($params['table'], $params['id'], $docData);
        }

        // Caso contrário, crie um nome de arquivo baseado nos dados disponíveis
        $baseDir = $params['table'].'/'.$params['id'].'/';
        $fileName = $this->storageService->processFileName($docData['TituloArquivo__c'] ?? 'documento');

        return $baseDir.$fileName.'.pdf';
    }

    /**
     * Gera um link S3 para o arquivo
     *
     * @param  string  $filename  Nome do arquivo
     * @return string Link S3
     */
    private function generateS3Link(string $filename): string
    {
        return config('services.s3.url').$filename;
    }

    /**
     * Obtém documentos do Salesforce
     *
     * @param  array  $params  Parâmetros da consulta
     * @return array Dados de documentos
     */
    private function getDocuments(array $params): array
    {
        try {
            $queryType = DocumentQueryTypeEnum::GET_IDS_DOCUMENTS;
            $queryService = new DocumentQueriesService($params, $this->queryFactory, $queryType);
            $paramsQuery = [
                'params' => $params,
                'queryType' => $queryService,
            ];

            return $this->salesforceService->queryObject('query', $paramsQuery);
        } catch (\Exception $e) {
            Log::error('Erro ao obter documentos: '.$e->getMessage(), [
                'exception' => $e,
                'params' => $params,
            ]);

            return ['records' => []];
        }
    }

    /**
     * Obtém dados de IDs de documentos
     *
     * @param  array  $params  Parâmetros da consulta
     * @return array Dados de documentos
     */
    private function getIdsDocument(array $params): array
    {
        try {
            // Log dos IDs para ajudar na depuração
            if (isset($params['ids'])) {
                Log::debug('IDs para consulta: '.json_encode($params['ids']));
            }

            $queryType = DocumentQueryTypeEnum::GET_FILES_DOCUMENTS;
            $queryService = new DocumentQueriesService($params, $this->queryFactory, $queryType);

            $query = $queryService->execute();

            $paramsQuery = [
                'params' => $params,
                'queryType' => $queryService,
            ];

            $result = $this->salesforceService->queryObject('query', $paramsQuery);

            // Log do resultado para depuração
            if (isset($result['error'])) {
                Log::error('Erro na consulta de documentos: '.json_encode($result['error']));
            }

            return $result;
        } catch (\Exception $e) {
            Log::error('Erro ao obter dados de IDs de documentos: '.$e->getMessage(), [
                'exception' => $e,
                'params' => $params,
            ]);

            return ['records' => []];
        }
    }
}
