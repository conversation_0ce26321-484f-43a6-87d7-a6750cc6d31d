<?php

declare(strict_types=1);

namespace App\DDD\Infrastructure\Document\Persistence\Queries;

use App\DDD\Domain\Document\ValueObjects\SubgrupoEnum;
use App\DDD\Infrastructure\Document\Persistence\Interfaces\DocumentQueryInterface;
use App\DDD\Infrastructure\Shared\Abstracts\Queries\AbstractQueries;

class GetIdDocumentQueries extends AbstractQueries implements DocumentQueryInterface
{
    public function execute(): string
    {
        $subgruposString = "'".implode("', '", SubgrupoEnum::toArray())."'";
        $query = "SELECT
            Id,
            Link__c,
            Subgrupo__c,
            TituloArquivo__c,
            VisivelCliente__c,
            IdInterno__c
        FROM
            Documento__c
        WHERE
            {$this->getParams()['table']} = '{$this->getParams()['id']}'
        AND
            VisivelCliente__c=true";

        $query .= ($this->getParams()['table'] === 'Empreendimento__c') ? " AND Subgrupo__c IN ({$subgruposString}) " : ' ';

        return $query;
    }
}
