<?php

declare(strict_types=1);

namespace App\DDD\Infrastructure\Document\Persistence\Queries;

use App\DDD\Infrastructure\Document\Persistence\Interfaces\DocumentQueryInterface;
use App\DDD\Infrastructure\Shared\Abstracts\Queries\AbstractQueries;

class GetDocumentFilesQueries extends AbstractQueries implements DocumentQueryInterface
{
    public function execute(): string
    {
        return '
            SELECT
                Id,
                Title,
                FileType,
                (SELECT Id, VersionNumber FROM ContentVersions)
            FROM
                ContentDocument
            WHERE
                Id IN ('.implode(',', $this->getParams()['ids']).')';
    }
}
