<?php

declare(strict_types=1);

namespace App\DDD\Infrastructure\Document\Persistence\Repositories;

use App\DDD\Domain\Document\Entities\Document;
use App\DDD\Infrastructure\Document\Persistence\Interfaces\DocumentRepositoryInterface;
use App\DDD\Infrastructure\Shared\Abstracts\Repositories\AbstractRepository;

class DocumentRepository extends AbstractRepository implements DocumentRepositoryInterface
{
    public function getModelClass(): string
    {
        return Document::class;
    }
}
