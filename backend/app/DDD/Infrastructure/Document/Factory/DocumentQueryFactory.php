<?php

declare(strict_types=1);

namespace App\DDD\Infrastructure\Document\Factory;

use App\DDD\Domain\Document\ValueObjects\DocumentQueryTypeEnum;
use App\DDD\Infrastructure\Document\Persistence\Interfaces\DocumentQueryInterface;
use App\DDD\Infrastructure\Document\Persistence\Queries\GetDocumentFilesQueries;
use App\DDD\Infrastructure\Document\Persistence\Queries\GetIdDocumentQueries;
use App\DDD\Infrastructure\Shared\Abstracts\Factories\AbstractQueryFactory;
use App\DDD\Infrastructure\Shared\Interfaces\Queries\QueryFactoryInterface;

class DocumentQueryFactory extends AbstractQueryFactory implements QueryFactoryInterface
{
    public function create($type): DocumentQueryInterface
    {
        return match ($type) {
            DocumentQueryTypeEnum::GET_IDS_DOCUMENTS => new GetIdDocumentQueries(),
            DocumentQueryTypeEnum::GET_FILES_DOCUMENTS => new GetDocumentFilesQueries(),
            default => throw new \InvalidArgumentException('Invalid query type'),
        };
    }
}
