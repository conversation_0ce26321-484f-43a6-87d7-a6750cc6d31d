<?php

declare(strict_types=1);

namespace App\DDD\Infrastructure\Document\Providers\Config;

use App\DDD\Application\Document\Interfaces\DocumentServiceInterface;
use App\DDD\Application\Document\Services\DocumentService;
use App\DDD\Domain\Document\Interfaces\DocumentValidationInterface;
use App\DDD\Domain\Document\Validation\DocumentValidation;
use App\DDD\Infrastructure\Document\Interfaces\DocumentProcessorInterface;
use App\DDD\Infrastructure\Document\Persistence\Interfaces\DocumentRepositoryInterface;
use App\DDD\Infrastructure\Document\Persistence\Repositories\DocumentRepository;
use App\DDD\Infrastructure\Document\Processors\Dependencies\DocumentEntityResolver;
use App\DDD\Infrastructure\Document\Processors\DocumentProcessor;
use App\DDD\Infrastructure\External\Salesforce\Interfaces\SalesforceServiceInterface;
use App\DDD\Infrastructure\Shared\Abstracts\Bindings\AbstractBindings;
use App\DDD\Infrastructure\Shared\Interfaces\Bindings\BindingsInterface;
use App\DDD\Infrastructure\Shared\Interfaces\Services\StorageServiceInterface;
use App\DDD\Infrastructure\Shared\Services\NotificationService;
use Illuminate\Contracts\Foundation\Application;

class DocumentsBindings extends AbstractBindings implements BindingsInterface
{
    /**
     * Registra serviços de suporte como EntityResolver
     */
    public function registerSupportServices(Application $app): void
    {
        // Registrar o DocumentEntityResolver explicitamente
        $app->singleton(DocumentEntityResolver::class, function ($app) {
            return new DocumentEntityResolver();
        });
    }

    public function registerServices(Application $app): void
    {
        $app->singleton(
            DocumentServiceInterface::class,
            function ($app) {
                return new DocumentService(
                    $app->make(DocumentRepositoryInterface::class),
                    $app->make(DocumentValidationInterface::class),
                    $app->make(DocumentProcessorInterface::class)
                );
            }
        );
    }

    public function registerRepositories(Application $app): void
    {
        // Repositório de documentos
        $app->singleton(
            DocumentRepositoryInterface::class,
            function ($app) {
                return new DocumentRepository();
            }
        );
    }

    public function registerProcessors(Application $app): void
    {
        // Processador de documentos
        $app->singleton(
            DocumentProcessorInterface::class,
            function ($app) {
                $processor = new DocumentProcessor(
                    validator: $app->make(DocumentValidationInterface::class),
                    storageService: $app->make(StorageServiceInterface::class),
                    salesforceService: $app->make(SalesforceServiceInterface::class),
                    entityResolver: $app->make(DocumentEntityResolver::class)
                );

                $app->resolving(DocumentServiceInterface::class, function ($service) use ($processor) {
                    $processor->setDocumentService($service);

                    return $service;
                });

                return $processor;
            }
        );
    }

    public function registerValidators(Application $app): void
    {
        $app->singleton(
            DocumentValidationInterface::class,
            function ($app) {
                return new DocumentValidation($app->make(NotificationService::class));
            }
        );
    }
}
