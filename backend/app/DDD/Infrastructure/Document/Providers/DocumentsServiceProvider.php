<?php

declare(strict_types=1);

namespace App\DDD\Infrastructure\Document\Providers;

use App\DDD\Infrastructure\Document\Providers\Config\DocumentsBindings;
use App\Providers\ApiServicesProviders\BaseApiDDDServiceProvider;

class DocumentsServiceProvider extends BaseApiDDDServiceProvider
{
    public function register(): void
    {
        parent::register();
        (new DocumentsBindings())->register($this->app);
    }
}
