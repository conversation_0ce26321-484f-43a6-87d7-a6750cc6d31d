<?php

declare(strict_types=1);

namespace App\DDD\Infrastructure\Shared\Html;

class MinifyHtml
{
    public function apply($html)
    {
        if (! isset($html)) {
            return null;
        }
        $search = [
            '/\>[^\S ]+/s',
            '/[^\S ]+\</s',
            '/(\s)+/s',
            '/<!--(.|\s)*?-->/',
        ];
        $replace = [
            '>',
            '<',
            '\1',
            '',
        ];

        return preg_replace($search, $replace, $html);
    }
}
