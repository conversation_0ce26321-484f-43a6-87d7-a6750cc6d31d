<?php

declare(strict_types=1);

namespace App\DDD\Infrastructure\Shared\Http\Responses;

use App\DDD\Infrastructure\Shared\Interfaces\Response\ResponseInterface;

class DefaultResponse implements ResponseInterface
{
    /**
     * @var array
     */
    private $parameters;

    /**
     * @var int
     */
    private $code;

    /**
     * @param  array|null|string|object|int|float|bool  $data  Dados de retorno
     * @param  int  $code  HTTP Code response
     */
    public function __construct(
        $data = null,
        int $code = 200,
        ?array $errors = null
    ) {
        $this->parameters = [
            'request' => asset(request()->path(), true),
            'method' => strtoupper(request()->method()),
            'data' => $data,
            'errors' => $errors,
        ];

        $this->code = $code;
    }

    /**
     * Retorna o array de parametros dessa classe
     */
    public function toArray(): array
    {
        return $this->parameters;
    }

    /**
     * Método para pegar algum parametro declarado na classe,
     * retorna null se não existir
     *
     * @param  string  $parameter
     */
    public function getCode(): int
    {
        return $this->code ?? 200;
    }
}
