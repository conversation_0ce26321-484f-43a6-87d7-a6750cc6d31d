<?php

declare(strict_types=1);

namespace App\DDD\Infrastructure\Shared\Extractors;

use App\DDD\Infrastructure\Shared\Interfaces\Extractors\ImageExtractorInterface;
use App\DDD\Infrastructure\Shared\Interfaces\Services\StorageServiceInterface;
use App\Jobs\ConvertAndUploadImageJob;

/**
 * Classe responsável por extrair imagens de conteúdo HTML
 */
class ImageExtractor implements ImageExtractorInterface
{
    /**
     * @var StorageServiceInterface Serviço de arquivos
     */
    protected StorageServiceInterface $storageService;

    /**
     * Construtor
     *
     * @param  StorageServiceInterface  $storageService  Serviço de arquivos
     */
    public function __construct(StorageServiceInterface $storageService)
    {
        $this->storageService = $storageService;
    }

    /**
     * @inheritdoc
     */
    public function extractFirst(string $imageTag, string $id, string $entity, string $field): string|null
    {

        $images = $this->extract($imageTag, $id, $entity, $field);

        return $images ? $images[0] : null;
    }

    /**
     * @inheritdoc
     */
    public function extract(?string $html, string $objid, string $obj, string $childobject): ?array
    {
        if ($html === null) {
            return null;
        }

        $pattern = '/<img[^>]+src="([^"]+)"/';
        preg_match_all($pattern, $html, $matches);

        if (empty($matches[1])) {
            return null;
        }

        return array_map(function ($url) use ($objid, $obj, $childobject) {
            return $this->cleanAndBuildUrl($url, $objid, $obj, $childobject);
        }, $matches[1]);
    }

    /**
     * Limpa e constrói a URL da imagem
     *
     * @param  string  $url  URL original
     * @param  string  $objid  ID do objeto
     * @param  string  $obj  Nome do objeto
     * @param  string  $childobject  Nome do objeto filho/campo
     * @return string URL processada
     */
    private function cleanAndBuildUrl(string $url, string $objid, string $obj, string $childobject): string
    {
        $url = str_replace('amp;', '', $url);
        $parsedUrl = parse_url($url);
        $query = $parsedUrl['query'] ?? '';
        parse_str($query, $params);

        if (isset($params['refid'])) {
            $baseURL = config('services.salesforce.base_url_file');
            $url = "{$baseURL}{$obj}/{$objid}/richTextImageFields/{$childobject}/{$params['refid']}";
            $filename = sprintf('%s/%s', $obj, "{$objid}/{$childobject}/{$params['refid']}");
            $this->convertAndUploadImage($filename, $url);

            return $filename;
        }

        return $url;
    }

    /**
     * Envia uma imagem para conversão e upload através de uma fila
     *
     * @param  string  $filename  Nome do arquivo
     * @param  string  $url  URL da imagem
     */
    private function convertAndUploadImage(string $filename, string $url): void
    {
        ConvertAndUploadImageJob::dispatch($filename, $url, true)->onQueue('files::images');
    }
}
