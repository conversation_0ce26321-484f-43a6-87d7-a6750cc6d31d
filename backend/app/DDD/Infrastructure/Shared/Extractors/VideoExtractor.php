<?php

declare(strict_types=1);

namespace App\DDD\Infrastructure\Shared\Extractors;

use App\DDD\Infrastructure\Shared\Interfaces\Extractors\VideoExtractorInterface;

/**
 * Classe responsável por extrair vídeos de conteúdo HTML
 */
class VideoExtractor implements VideoExtractorInterface
{
    /**
     * @inheritdoc
     */
    public function extract(?string $html): ?array
    {
        if ($html === null) {
            return null;
        }

        $ids = [];
        $urls = $this->extractUrls($html);

        if (empty($urls)) {
            return null;
        }

        foreach ($urls as $url) {
            $url = trim($url);
            $videoId = $this->extractVideoId($url);

            if ($videoId) {
                $ids[] = $videoId;
            }
        }

        return empty($ids) ? null : $ids;
    }

    /**
     * Extrai URLs de um conteúdo HTML
     *
     * @param  string  $html  Conteúdo HTML
     * @return array Array de URLs
     */
    private function extractUrls(string $html): array
    {
        $pattern = '/<a[^>]+href="([^"]+)"/';
        preg_match_all($pattern, $html, $matches);

        return isset($matches[1]) ? array_map('trim', $matches[1]) : [];
    }

    /**
     * Extrai o ID de um vídeo de uma URL
     *
     * @param  string  $url  URL do vídeo
     * @return string|null ID do vídeo ou null se não for encontrado
     */
    private function extractVideoId(string $url): ?string
    {
        // YouTube shortlink
        if (preg_match('/youtu\.be\/([^\?\&\"\'>]+)/', $url, $match)) {
            return $match[1];
        }

        // YouTube standard URL
        if (preg_match('/youtube\.com\/.*[?&]v=([^\?\&\"\'>]+)/', $url, $match)) {
            return $match[1];
        }

        // YouTube embed URL
        if (preg_match('/youtube\.com\/embed\/([^\?\&\"\'>]+)/', $url, $match)) {
            return $match[1];
        }

        return null;
    }
}
