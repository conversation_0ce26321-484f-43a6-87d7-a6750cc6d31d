<?php

declare(strict_types=1);

namespace App\DDD\Infrastructure\Shared\Notifications;

use App\DDD\Infrastructure\Shared\Abstracts\Exception\AbstractValidationException;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Facades\Log;

class ValidationErrorNotification extends Notification implements ShouldQueue
{
    use Queueable;

    protected $exception;

    protected string $validationContext;

    protected array $additionalData;

    protected array $trace;

    public function __construct(
        $exception,
        string $validationContext,
        array $additionalData = [],
        array $trace = []
    ) {
        $this->exception = $exception;
        $this->validationContext = $validationContext;
        $this->additionalData = $additionalData;
        $this->trace = count($trace) > 0 ? $trace : ['no-trace'];
    }

    public function via($notifiable)
    {
        return ['mail'];
    }

    public function toMail($notifiable)
    {
        // Log para debug
        Log::debug('Preparing validation error email');

        // Obtém o timestamp atual uma única vez para usar em todo o email
        $timestamp = now()->format('d/m/Y H:i:s');

        $mailMessage = (new MailMessage())
            ->error()
            ->subject("Erro de validação: {$this->validationContext}")
            ->line("Ocorreu um erro de validação no contexto: {$this->validationContext}");

        // Adiciona a mensagem de erro com base no tipo de exceção
        if ($this->exception instanceof AbstractValidationException) {
            $mailMessage->line("Erro: {$this->exception->asString()}");
        } else {
            $mailMessage->line("Erro: {$this->exception->getMessage()}");
        }

        $mailMessage->line("Data/Hora: {$timestamp}");

        // Adiciona erros de validação se disponíveis
        if (! empty($this->additionalData['validation_errors'])) {
            $mailMessage->line('Erros de validação:');
            foreach ($this->additionalData['validation_errors'] as $error) {
                $mailMessage->line("- {$error}");
            }
        }

        // Adiciona informações sobre a exceção original (se disponível)
        if (! empty($this->additionalData['original_exception'])) {
            $originalEx = $this->additionalData['original_exception'];
            $mailMessage->line("Exceção original: {$originalEx['message']}")
                ->line("Arquivo: {$originalEx['file']}:{$originalEx['line']}");
        }

        // Adiciona os dados que falharam na validação
        if (! empty($this->additionalData['data'])) {
            $mailMessage->line('Dados que falharam na validação:')
                ->line(json_encode($this->additionalData['data'], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
        }

        // Adiciona as regras que foram aplicadas
        if (! empty($this->additionalData['rules'])) {
            $mailMessage->line('Regras de validação aplicadas:')
                ->line(json_encode($this->additionalData['rules'], JSON_PRETTY_PRINT));
        }

        $mailMessage->line('Trace:')
            ->line(json_encode($this->trace, JSON_PRETTY_PRINT));

        // Log para confirmar que o email foi preparado
        Log::debug('Email prepared successfully');

        return $mailMessage;
    }
}
