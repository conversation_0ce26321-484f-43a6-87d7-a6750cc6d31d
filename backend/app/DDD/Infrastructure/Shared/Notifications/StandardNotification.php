<?php

declare(strict_types=1);

namespace App\DDD\Infrastructure\Shared\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class StandardNotification extends Notification
{
    use Queueable;

    protected string $subject;
    protected string $context;
    protected array $contextData;

    public function __construct(
        string $subject,
        string $context,
        array $contextData
    ) {
        $this->subject = $subject;
        $this->context = $context;
        $this->contextData = $contextData;
    }

    public function via($notifiable): array
    {
        return ['mail'];
    }

    public function toMail($notifiable): MailMessage
    {
        $mailMessage = new MailMessage();
        $mailMessage->subject($this->subject);
        $mailMessage->greeting('Olá!');

        if (isset($this->contextData['data'])) {
            // Verifica se há um erro nos dados
            if (isset($this->contextData['data']['error'])) {
                // Destaca a mensagem de erro com formatação em negrito e cor vermelha
                $mailMessage->line('**❌ OCORREU UM ERRO ❌**');
                $mailMessage->line('');
                $mailMessage->line("**Mensagem de erro:** {$this->contextData['data']['error']}");
                $mailMessage->line('--------------------------------------------------');
                $mailMessage->line(' ');
            } else {
                $mailMessage->line("Detalhes da {$this->context}:");
                $mailMessage->line('');

                foreach ($this->contextData['data'] as $section => $data) {
                    // Adiciona cabeçalho da seção
                    $mailMessage->line("# {$section}");

                    if (is_array($data)) {
                        foreach ($data as $key => $value) {
                            if (is_array($value)) {
                                $mailMessage->line("**{$key}:**");
                                foreach ($value as $subKey => $subValue) {
                                    $mailMessage->line("- {$subKey}: {$subValue}");
                                }
                            } else {
                                $mailMessage->line("**{$key}:** {$value}");
                            }
                        }
                    } else {
                        $mailMessage->line($data);
                    }
                    $mailMessage->line('--------------------------------------------------');
                    $mailMessage->line(' ');
                }
            }
        }

        // Adiciona informações do ambiente e timestamp
        $mailMessage->line('---');
        $mailMessage->line("**Ambiente:** {$this->contextData['environment']}");
        $mailMessage->line("**Data/Hora:** {$this->contextData['timestamp']}");

        return $mailMessage;
    }
}
