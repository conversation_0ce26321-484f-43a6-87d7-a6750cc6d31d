<?php

declare(strict_types=1);

namespace App\DDD\Infrastructure\Shared\Notifications\Providers;

use App\DDD\Infrastructure\Shared\Abstracts\Bindings\AbstractBindings;
use App\DDD\Infrastructure\Shared\Interfaces\Bindings\BindingsInterface;
use App\DDD\Infrastructure\Shared\Services\NotificationService;
use Illuminate\Contracts\Foundation\Application;

class NotificationBinding extends AbstractBindings implements BindingsInterface
{
    public function registerServices(Application $app): void
    {
        $app->singleton(NotificationService::class, function ($app) {
            return new NotificationService();
        });
    }
}
