<?php

declare(strict_types=1);

namespace App\DDD\Infrastructure\Shared\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Facades\Log;

class ErrorNotification extends Notification
{
    use Queueable;

    private $exception;

    private $route;

    private $requestData;

    private $trace;

    public function __construct($exception, $route, $requestData, $trace = [])
    {
        $this->exception = $exception;
        $this->route = $route;
        $this->requestData = $requestData;
        $this->trace = count($trace) > 0 ? $trace : 'no-trace';
    }

    public function via($notifiable)
    {
        Log::debug('ErrorNotification::via chamado');

        return ['mail'];
    }

    public function toMail($notifiable)
    {
        $email = $notifiable->routeNotificationFor('mail');

        Log::debug('Endereço de e-mail do destinatário:', ['email' => $email]);

        return (new MailMessage())
            ->error()
            ->subject('Erro na aplicação: '.class_basename($this->exception))
            ->line('Ocorreu um erro na aplicação:')
            ->line('Rota: '.$this->route)
            ->line('Erro: '.$this->exception->getMessage())
            ->line('Data/Hora: '.now()->format('d/m/Y H:i:s'))
            ->line('Dados da requisição:')
            ->line(json_encode($this->requestData, JSON_PRETTY_PRINT))
            ->line('Trace:')
            ->line(json_encode($this->trace, JSON_PRETTY_PRINT));
    }
}
