<?php

declare(strict_types=1);

namespace App\DDD\Infrastructure\Shared\Services;

use App\DDD\Infrastructure\Shared\Image\ImageCompress;
use App\DDD\Infrastructure\Shared\Interfaces\DTO\DtoInterface;
use App\DDD\Infrastructure\Shared\Interfaces\Services\FilesServiceInterface;
use App\DDD\Infrastructure\Shared\Interfaces\Services\ServiceInterface;
use App\Jobs\ConvertAndUploadFileJob;
use App\Jobs\UploadFileJob;
use Aws\S3\S3Client;
use Exception;
use finfo;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class FilesService implements FilesServiceInterface
{
    private $imageCompress;
    private $s3;

    private $commonExtensions = [
        'pdf',
        'doc',
        'docx',
        'xls',
        'xlsx',
        'txt',
        'jpg',
        'jpeg',
        'png',
        'gif',
        'zip',
        'rar',
        'mp3',
        'mp4',
        'avi',
        'mov',
        'ppt',
        'pptx',
        'csv',
        'webp',
    ];

    public function __construct(ImageCompress $imageCompress)
    {
        $this->imageCompress = $imageCompress;
    }

    public function getimg($src)
    {
        try {
            $imageContent = $this->urlImg($src);
            if (empty($imageContent)) {
                Log::error('Conteúdo da imagem vazio de: '.$src);
                return null;
            }

            $mime_type = $this->determineContentType($imageContent, basename($src));

            if (! str_starts_with($mime_type, 'image/')) {
                Log::error('O conteúdo não parece ser uma imagem válida: '.$mime_type);
                return null;
            }

            return $this->convertToWebP($imageContent);
        } catch (Exception $e) {
            Log::error('Erro ao obter ou processar imagem: '.$e->getMessage());
            return null;
        }
    }

    public function urlImg($url)
    {
        try {
            $response = Http::timeout(60)->get($url);
            if ($response->successful()) {
                return $response->body();
            }
            Log::error('Falha ao obter imagem da URL: '.$url);
            return null;
        } catch (Exception $e) {
            Log::error('Erro ao obter imagem da URL: '.$e->getMessage());
            return null;
        }
    }

    public function urlpdf($url)
    {
        try {
            $response = Http::timeout(600)
                ->connectTimeout(600)
                ->get($url);

            if ($response->successful()) {
                return $response->body();
            }
            Log::error('Falha ao obter PDF da URL: '.$url);
            return null;
        } catch (Exception $e) {
            Log::error('Erro ao obter PDF da URL: '.$e->getMessage());
            return null;
        }
    }

    public function determineContentType($file, string $filename): string
    {
        try {
            if (is_string($file)) {
                $finfo = new finfo(FILEINFO_MIME_TYPE);
                $mime_type = $finfo->buffer($file);
                if ($mime_type) {
                    return $mime_type;
                }
            }

            // Fallback para extensão do arquivo
            $extension = pathinfo($filename, PATHINFO_EXTENSION);
            switch (strtolower($extension)) {
                case 'pdf':
                    return 'application/pdf';
                case 'jpg':
                case 'jpeg':
                    return 'image/jpeg';
                case 'png':
                    return 'image/png';
                case 'gif':
                    return 'image/gif';
                case 'webp':
                    return 'image/webp';
                default:
                    return 'application/octet-stream';
            }
        } catch (Exception $e) {
            Log::warning('Erro ao determinar tipo de conteúdo: '.$e->getMessage());
            return 'application/octet-stream';
        }
    }

    public function convertToWebP($imageContent)
    {
        try {
            $webpImage = $this->imageCompress->convertToWebP($imageContent);
            if ($webpImage === null) {
                Log::error('Falha ao converter imagem para WebP ');
                return $imageContent;
            }

            return $webpImage;
        } catch (Exception $e) {
            Log::error('Erro ao obter ou processar imagem: '.$e->getMessage());
            return null;
        }
    }

    public function uploadPdfS3($filename, $file, $createImage = false)
    {
        if (preg_match('/drive\.google\.com/', $file)) {
            $file = $this->getPdfDrive($file);
        } else {
            $file = $this->showpdfsales($file);
        }

        if (! $file) {
            Log::error('Falha ao obter o conteúdo do PDF');
            return null;
        }

        $mime_type = $this->determineContentType($file, $filename);

        if (! str_ends_with(strtolower($filename), '.pdf') && ! str_starts_with($mime_type, 'image/')) {
            $filename .= '.pdf';
        }

        // Verifica o tamanho do arquivo e comprime se for maior que 1MB
        $fileSize = strlen($file);
        $maxSize = 1024 * 1024; // 1MB em bytes

        Log::debug('uploadPdfS3 181');

        if ($fileSize > $maxSize && $mime_type === 'application/pdf') {
            Log::info("PDF maior que 1MB detectado ({$this->formatFileSize($fileSize)}). Iniciando compressão para: {$filename}");

            $compressedFile = $this->imageCompress->compressPdf($file);

            if ($compressedFile !== null) {
                $compressedSize = strlen($compressedFile);
                $reduction = round((($fileSize - $compressedSize) / $fileSize) * 100, 2);
                Log::info("PDF comprimido com sucesso. Tamanho original: {$this->formatFileSize($fileSize)}, Tamanho comprimido: {$this->formatFileSize($compressedSize)} (redução de {$reduction}%)");
                $file = $compressedFile;
            } else {
                Log::warning("Falha na compressão do PDF. Usando arquivo original: {$filename}");
            }
        }

        if ($createImage && ! empty($file) && is_string($file)) {
            $pdfToImage = $this->imageCompress->convertPdfToWebP($file);
            $this->uploadFileS3($filename.'.webp', $pdfToImage);
        }

        return $this->uploadFileS3($filename, $file);
    }

    public function getPdfDrive($viewUrl)
    {
        preg_match('/\/d\/(.+?)(\/|$)/', $viewUrl, $matches);
        $fileId = $matches[1] ?? '';

        if (empty($fileId)) {
            abort(404, 'Não foi possível extrair o ID do arquivo da URL fornecida.');
        }

        $downloadUrl = 'https://drive.google.com/uc?export=download&id='.$fileId;
        $response = Http::timeout(600)
            ->connectTimeout(600)
            ->get($downloadUrl);

        if ($response->successful()) {
            return $response->body();
        }

        abort(404, 'Não foi possível baixar o arquivo.');
    }

    public function showpdfsales($id)
    {
        $baseURL = config('services.salesforce.base_url_file');
        $pdfPath = "{$baseURL}ContentVersion/{$id}/VersionData";
        $pdfPath = "https://curyconstrutora.file.force.com/sfc/servlet.shepherd/document/download/{$id}?operationContext=S1";

        return $this->urlpdf($pdfPath);
    }

    public function uploadFileS3($filename, $file)
    {
        if ($file === null) {
            Log::error('Conteúdo do arquivo é nulo para: '.$filename);
            return null;
        }

        try {
            $this->s3 = new S3Client([
                'version' => 'latest',
                'region' => config('filesystems.disks.s3.region'),
                'credentials' => [
                    'key' => config('filesystems.disks.s3.key'),
                    'secret' => config('filesystems.disks.s3.secret'),
                ],
            ]);

            if (! empty($file) && is_string($file)) {
                $result = $this->s3->putObject([
                    'Bucket' => config('filesystems.disks.s3.bucket'),
                    'Key' => 'files/'.$filename,
                    'Body' => $file,
                    'ACL' => 'public-read',
                ]);

                return $result['ObjectURL'];
            }

            return null;
        } catch (Exception $e) {
            Log::error('Erro ao fazer upload para S3: '.$e->getMessage());
            return null;
        }
    }

    public function processFileName($fileName)
    {
        $parts = explode('.', $fileName);
        if (count($parts) > 1) {
            $lastPart = strtolower(end($parts));
            if (in_array($lastPart, $this->commonExtensions)) {
                array_pop($parts);
            }
            $fileName = implode('.', $parts);
        }
        $fileName = $this->removeAccents($fileName);
        $fileName = strtolower($fileName);
        $fileName = preg_replace('/[^a-z0-9\/]+/', '_', $fileName);
        $fileName = str_replace('/', '_', $fileName);
        $fileName = preg_replace('/_+/', '_', $fileName);
        $fileName = trim($fileName, '_');
        if (empty($fileName)) {
            $fileName = 'file';
        }

        return $fileName;
    }

    private function removeAccents($string)
    {
        if (! preg_match('/[\x80-\xff]/', $string)) {
            return $string;
        }

        $chars = [
            // Decompositions for Latin-1 Supplement
            chr(195).chr(128) => 'A', chr(195).chr(129) => 'A',
            chr(195).chr(130) => 'A', chr(195).chr(131) => 'A',
            chr(195).chr(132) => 'A', chr(195).chr(133) => 'A',
            chr(195).chr(135) => 'C', chr(195).chr(136) => 'E',
            chr(195).chr(137) => 'E', chr(195).chr(138) => 'E',
            chr(195).chr(139) => 'E', chr(195).chr(140) => 'I',
            chr(195).chr(141) => 'I', chr(195).chr(142) => 'I',
            chr(195).chr(143) => 'I', chr(195).chr(145) => 'N',
            chr(195).chr(146) => 'O', chr(195).chr(147) => 'O',
            chr(195).chr(148) => 'O', chr(195).chr(149) => 'O',
            chr(195).chr(150) => 'O', chr(195).chr(153) => 'U',
            chr(195).chr(154) => 'U', chr(195).chr(155) => 'U',
            chr(195).chr(156) => 'U', chr(195).chr(157) => 'Y',
            chr(195).chr(159) => 's', chr(195).chr(160) => 'a',
            chr(195).chr(161) => 'a', chr(195).chr(162) => 'a',
            chr(195).chr(163) => 'a', chr(195).chr(164) => 'a',
            chr(195).chr(165) => 'a', chr(195).chr(167) => 'c',
            chr(195).chr(168) => 'e', chr(195).chr(169) => 'e',
            chr(195).chr(170) => 'e', chr(195).chr(171) => 'e',
            chr(195).chr(172) => 'i', chr(195).chr(173) => 'i',
            chr(195).chr(174) => 'i', chr(195).chr(175) => 'i',
            chr(195).chr(177) => 'n', chr(195).chr(178) => 'o',
            chr(195).chr(179) => 'o', chr(195).chr(180) => 'o',
            chr(195).chr(181) => 'o', chr(195).chr(182) => 'o',
            chr(195).chr(182) => 'o', chr(195).chr(185) => 'u',
            chr(195).chr(186) => 'u', chr(195).chr(187) => 'u',
            chr(195).chr(188) => 'u', chr(195).chr(189) => 'y',
            chr(195).chr(191) => 'y',
        ];

        return strtr($string, $chars);
    }

    private function formatFileSize($size)
    {
        if ($size < 1024) {
            return $size . ' bytes';
        } elseif ($size < 1024 * 1024) {
            return round($size / 1024, 2) . ' KB';
        } elseif ($size < 1024 * 1024 * 1024) {
            return round($size / (1024 * 1024), 2) . ' MB';
        } else {
            return round($size / (1024 * 1024 * 1024), 2) . ' GB';
        }
    }
}
