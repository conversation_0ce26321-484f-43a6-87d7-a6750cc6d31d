<?php

declare(strict_types=1);

namespace App\DDD\Infrastructure\Shared\Services;

use App\DDD\Infrastructure\Document\Interfaces\DocumentProcessorInterface;
use App\DDD\Infrastructure\External\Salesforce\Interfaces\SalesforceServiceInterface;
use App\DDD\Infrastructure\Shared\Extractors\ImageExtractor;
use App\DDD\Infrastructure\Shared\Extractors\VideoExtractor;
use App\DDD\Infrastructure\Shared\Interfaces\Services\ServiceLocatorInterface;
use App\DDD\Infrastructure\Shared\Processors\CarouselImageProcessor;
use Illuminate\Contracts\Container\Container;

/**
 * Classe responsável por localizar e disponibilizar serviços para a aplicação
 * Implementa o padrão Service Locator para evitar chamadas diretas ao container
 */
class ServiceLocator implements ServiceLocatorInterface
{
    /**
     * Mapeamento de nomes de serviço para suas interfaces ou classes concretas
     */
    private const SERVICE_MAP = [
        'storageService' => StorageService::class,
        'imageExtractor' => ImageExtractor::class,
        'videoExtractor' => VideoExtractor::class,
        'salesforceService' => SalesforceServiceInterface::class,
        'documentsProcessor' => DocumentProcessorInterface::class,
        'carouselImageProcessor' => CarouselImageProcessor::class,
    ];

    /**
     * @var Container Container de injeção de dependência
     */
    private Container $container;

    /**
     * @var array Cache de serviços já instanciados
     */
    private array $services = [];

    /**
     * Cria uma nova instância do ServiceLocator
     */
    public function __construct(?Container $container = null)
    {
        $this->container = $container ?? app();
    }

    /**
     * Obtém um serviço pelo nome
     *
     * @param  string  $serviceName  Nome do serviço
     * @return mixed Instância do serviço
     *
     * @throws \InvalidArgumentException Se o serviço não existir
     */
    public function get(string $serviceName): mixed
    {
        // Retorna do cache se já tiver sido instanciado
        if (isset($this->services[$serviceName])) {
            return $this->services[$serviceName];
        }

        // Verifica se o serviço está mapeado
        if (! isset(self::SERVICE_MAP[$serviceName])) {
            throw new \InvalidArgumentException("Serviço '{$serviceName}' não encontrado");
        }

        // Instancia o serviço do container
        $service = $this->container->make(self::SERVICE_MAP[$serviceName]);

        // Armazena em cache
        $this->services[$serviceName] = $service;

        return $service;
    }

    /**
     * Registra um serviço no ServiceLocator
     *
     * @param  string  $serviceName  Nome do serviço
     * @param  mixed  $instance  Instância do serviço
     */
    public function register(string $serviceName, mixed $instance): void
    {
        $this->services[$serviceName] = $instance;
    }
}
