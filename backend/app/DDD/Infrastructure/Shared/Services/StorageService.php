<?php

declare(strict_types=1);

namespace App\DDD\Infrastructure\Shared\Services;

use App\DDD\Infrastructure\Shared\Image\ImageCompress;
use App\DDD\Infrastructure\Shared\Interfaces\DTO\DtoInterface;
use App\DDD\Infrastructure\Shared\Interfaces\Services\ServiceInterface;
use App\DDD\Infrastructure\Shared\Interfaces\Services\StorageServiceInterface;
use App\Jobs\ConvertAndUploadFileJob;
use App\Jobs\UploadFileJob;
use Aws\S3\S3Client;
use Exception;
use finfo;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

/**
 * Serviço para gerenciamento de arquivos
 */
class StorageService implements StorageServiceInterface
{
    /**
     * @var S3Client|null Cliente S3
     */
    private ?S3Client $s3 = null;

    /**
     * @var array Extensões de arquivo comuns
     */
    private array $commonExtensions = [
        'pdf',
        'doc',
        'docx',
        'xls',
        'xlsx',
        'txt',
        'jpg',
        'jpeg',
        'png',
        'gif',
        'zip',
        'rar',
        'mp3',
        'mp4',
        'avi',
        'mov',
        'ppt',
        'pptx',
        'csv',
        'webp',
    ];

    /**
     * Construtor
     *
     * @param  ImageCompress  $imageCompress  Serviço de compressão de imagens
     */
    public function __construct(
        private readonly ImageCompress $imageCompress
    ) {
    }

    /**
     * @inheritdoc
     */
    public function determineContentType($file, string $filename): string
    {
        try {
            $finfo = new finfo(FILEINFO_MIME_TYPE);

            if (is_resource($file)) {
                return $finfo->buffer(stream_get_contents($file, -1, 0));
            } elseif (is_string($file)) {
                if (file_exists($file)) {
                    return $finfo->file($file);
                } else {
                    return $finfo->buffer($file);
                }
            }

            // Determina o tipo pelo nome do arquivo
            $extension = strtolower(pathinfo($filename, PATHINFO_EXTENSION));
            $mime_map = [
                'pdf' => 'application/pdf',
                'jpg' => 'image/jpeg',
                'jpeg' => 'image/jpeg',
                'png' => 'image/png',
                'gif' => 'image/gif',
                'webp' => 'image/webp',
            ];

            if (isset($mime_map[$extension])) {
                return $mime_map[$extension];
            }

            return 'application/octet-stream';
        } catch (Exception $e) {
            Log::warning('Erro ao determinar tipo de conteúdo: '.$e->getMessage());

            return 'application/octet-stream';
        }
    }

    /**
     * @inheritdoc
     */
    public function convertToWebP(string $imageContent): ?string
    {
        try {
            $webpImage = $this->imageCompress->convertToWebP($imageContent);
            if ($webpImage === null) {
                Log::error('Falha ao converter imagem para WebP');

                return $imageContent;
            }

            return $webpImage;
        } catch (Exception $e) {
            Log::error('Erro ao processar imagem: '.$e->getMessage());

            return null;
        }
    }

    /**
     * @inheritdoc
     */
    public function uploadPdfS3(string $filename, string $file, bool $createImage = false): ?string
    {
        // Processa o conteúdo do arquivo
        if (preg_match('/drive\.google\.com/', $file)) {
            $file = $this->getPdfDrive($file);
        } else {
            $file = $this->showpdfsales($file);
        }

        if (! $file) {
            Log::error('Falha ao obter o conteúdo do PDF');

            return null;
        }

        // Determina o tipo e ajusta a extensão se necessário
        $mime_type = $this->determineContentType($file, $filename);
        if (! str_ends_with(strtolower($filename), '.pdf') && ! str_starts_with($mime_type, 'image/')) {
            $filename .= '.pdf';
        }

        // Verifica o tamanho do arquivo e comprime se for maior que 1MB
        $fileSize = strlen($file);
        $maxSize = 1024 * 1024; // 1MB em bytes

        if ($fileSize > $maxSize && $mime_type === 'application/pdf') {
            Log::info("PDF maior que 1MB detectado ({$this->formatFileSize($fileSize)}). Iniciando compressão para: {$filename}");

            $compressedFile = $this->imageCompress->compressPdf($file);

            if ($compressedFile !== null) {
                $compressedSize = strlen($compressedFile);
                $reduction = round((($fileSize - $compressedSize) / $fileSize) * 100, 2);
                Log::info("PDF comprimido com sucesso. Tamanho original: {$this->formatFileSize($fileSize)}, Tamanho comprimido: {$this->formatFileSize($compressedSize)} (redução de {$reduction}%)");
                $file = $compressedFile;
            } else {
                Log::warning("Falha na compressão do PDF. Usando arquivo original: {$filename}");
            }
        }

        // Cria uma imagem do PDF se solicitado
        if ($createImage && ! empty($file) && is_string($file)) {
            $pdfToImage = $this->imageCompress->convertPdfToWebP($file, 2000);
            $this->uploadFileS3($filename.'.webp', $pdfToImage);
        }

        // Faz o upload do arquivo original
        return $this->uploadFileS3($filename, $file);
    }

    /*
        public function uploadPdfS3(string $filename, string $file, bool $createImage = false): ?string
        {
            $fileContent = null;

            // Verifica se o arquivo é uma URL ou já é o conteúdo
            if (filter_var($file, FILTER_VALIDATE_URL) || preg_match('/^[A-Za-z0-9]{15,18}$/', $file)) {
                // É uma URL ou ID do Salesforce
                if (preg_match('/drive\.google\.com/', $file)) {
                    $fileContent = $this->getPdfDrive($file);
                } else {
                    // Para URLs do Salesforce, retornamos a URL diretamente
                    // ao invés de tentar obter o conteúdo
                    return $file;
                }
            } else {
                // Assumimos que já é o conteúdo do arquivo
                $fileContent = $file;
            }

            if (!$fileContent) {
                Log::error('Falha ao obter o conteúdo do PDF');
                return null;
            }

            // Continua com o processamento normal
            // ...
        }
    */
    /**
     * @inheritdoc
     */
    public function getPdfDrive(string $viewUrl): string
    {
        // Extrai o ID do arquivo da URL
        preg_match('/\/d\/(.+?)(\/|$)/', $viewUrl, $matches);
        $fileId = $matches[1] ?? '';

        if (empty($fileId)) {
            abort(404, 'Não foi possível extrair o ID do arquivo da URL fornecida.');
        }

        // Obtém o conteúdo do arquivo
        $downloadUrl = 'https://drive.google.com/uc?export=download&id='.$fileId;
        $response = Http::timeout(600)
            ->connectTimeout(600)
            ->get($downloadUrl);

        if ($response->successful()) {
            return $response->body();
        }

        abort(404, 'Não foi possível baixar o arquivo.');
    }

    /**
     * @inheritdoc
     */
    public function showpdfsales(string $id): string
    {
        return "https://curyconstrutora.file.force.com/sfc/servlet.shepherd/document/download/{$id}?operationContext=S1";
    }

    /**
     * @inheritdoc
     */
    public function uploadFileS3(string $filename, string $file): ?string
    {
        if ($file === null) {
            Log::error('Conteúdo do arquivo é nulo para: '.$filename);

            return null;
        }

        try {
            // Inicializa o cliente S3 se ainda não foi inicializado
            if ($this->s3 === null) {
                $this->s3 = new S3Client([
                    'version' => 'latest',
                    'region' => config('filesystems.disks.s3.region'),
                    'credentials' => [
                        'key' => config('filesystems.disks.s3.key'),
                        'secret' => config('filesystems.disks.s3.secret'),
                    ],
                ]);
            }

            // Faz o upload do arquivo para o S3
            if (! empty($file) && is_string($file)) {
                $result = $this->s3->putObject([
                    'Bucket' => config('filesystems.disks.s3.bucket'),
                    'Key' => 'files/'.$filename,
                    'Body' => $file,
                    'ACL' => 'public-read',
                ]);

                return $result['ObjectURL'];
            }

            return null;
        } catch (Exception $e) {
            Log::error('Erro ao fazer upload para S3: '.$e->getMessage());

            return null;
        }
    }

    /**
     * @inheritdoc
     */
    public function getExtensionFromMimeType(string $mime_type): string
    {
        $mime_map = [
            'image/jpeg' => 'jpg',
            'image/pjpeg' => 'jpg',
            'image/webp' => 'webp',
            'image/png' => 'png',
            'image/gif' => 'gif',
            'application/pdf' => 'pdf',
        ];

        return $mime_map[$mime_type] ?? 'pdf';
    }

    /**
     * @inheritdoc
     */
    public function deleteFileS3(string $filename): ?array
    {
        try {
            // Inicializa o cliente S3 se ainda não foi inicializado
            if ($this->s3 === null) {
                $this->s3 = new S3Client([
                    'version' => 'latest',
                    'region' => config('filesystems.disks.s3.region'),
                    'credentials' => [
                        'key' => config('filesystems.disks.s3.key'),
                        'secret' => config('filesystems.disks.s3.secret'),
                    ],
                ]);
            }

            // Prepara o objeto para exclusão
            $objects = [
                ['Key' => $filename],
            ];

            // Exclui o objeto do S3
            $result = $this->s3->deleteObjects([
                'Bucket' => config('filesystems.disks.s3.bucket'),
                'Delete' => [
                    'Objects' => $objects,
                ],
            ]);

            return $result['Deleted'] ?? null;
        } catch (Exception $e) {
            Log::error('Erro ao deletar arquivo do S3: '.$e->getMessage());

            return null;
        }
    }

    /**
     * @inheritdoc
     */
    public function processFileName(string $fileName): string
    {
        // Remove a extensão se for uma extensão comum
        $parts = explode('.', $fileName);
        if (count($parts) > 1) {
            $lastPart = strtolower(end($parts));
            if (in_array($lastPart, $this->commonExtensions)) {
                array_pop($parts);
            }
            $fileName = implode('.', $parts);
        }

        // Processa o nome para torná-lo seguro para uso como nome de arquivo
        $fileName = $this->removeAccents($fileName);
        $fileName = strtolower($fileName);
        $fileName = preg_replace('/[^a-z0-9\/]+/', '_', $fileName);
        $fileName = str_replace('/', '_', $fileName);
        $fileName = preg_replace('/_+/', '_', $fileName);
        $fileName = trim($fileName, '_');

        // Garante que o nome do arquivo não fique vazio
        if (empty($fileName)) {
            $fileName = 'file';
        }

        return $fileName;
    }

    /**
     * Remove acentos e caracteres especiais
     *
     * @param  string  $string  String a ser processada
     * @return string String sem acentos
     */
    private function removeAccents(string $string): string
    {
        if (! preg_match('/[\x80-\xff]/', $string)) {
            return $string;
        }

        $chars = [
            'à' => 'a',
            'á' => 'a',
            'â' => 'a',
            'ã' => 'a',
            'ä' => 'a',
            'ç' => 'c',
            'è' => 'e',
            'é' => 'e',
            'ê' => 'e',
            'ë' => 'e',
            'ì' => 'i',
            'í' => 'i',
            'î' => 'i',
            'ï' => 'i',
            'ñ' => 'n',
            'ò' => 'o',
            'ó' => 'o',
            'ô' => 'o',
            'õ' => 'o',
            'ö' => 'o',
            'ù' => 'u',
            'ú' => 'u',
            'û' => 'u',
            'ü' => 'u',
            'ý' => 'y',
            'ÿ' => 'y',
            'À' => 'A',
            'Á' => 'A',
            'Â' => 'A',
            'Ã' => 'A',
            'Ä' => 'A',
            'Ç' => 'C',
            'È' => 'E',
            'É' => 'E',
            'Ê' => 'E',
            'Ë' => 'E',
            'Ì' => 'I',
            'Í' => 'I',
            'Î' => 'I',
            'Ï' => 'I',
            'Ñ' => 'N',
            'Ò' => 'O',
            'Ó' => 'O',
            'Ô' => 'O',
            'Õ' => 'O',
            'Ö' => 'O',
            'Ù' => 'U',
            'Ú' => 'U',
            'Û' => 'U',
            'Ü' => 'U',
            'Ý' => 'Y',
        ];

        return strtr($string, $chars);
    }

    /**
     * @inheritdoc
     */
    public function generateFilename(string $table, string $id, array $document): string
    {
        // Cria o nome base do arquivo
        $baseFilename = sprintf(
            '%s/%s/%s',
            $table,
            $id,
            $this->processFileName($document['TituloArquivo__c'] ?? 'documento')
        );

        // Casos especiais por tipo de tabela
        if ($table === 'Planta__c') {
            return $baseFilename.'.pdf';
        }

        // Outros casos, determina a extensão pelo conteúdo do arquivo
        $file = $document['Link__c'] ?? $document['IdInterno__c'] ?? null;

        if (! $file) {
            return $baseFilename.'.pdf';
        }

        return $this->getFilenameWithExtension($file, $baseFilename);
    }

    /**
     * Obtém o nome do arquivo com a extensão correta
     *
     * @param  string  $file  URL ou ID do arquivo
     * @param  string  $baseFilename  Nome base do arquivo
     * @return string Nome do arquivo com extensão
     */
    private function getFilenameWithExtension(string $file, string $baseFilename): string
    {
        $fileContent = $this->getFileContent($file);
        $contentType = $this->determineContentType($fileContent, $baseFilename);
        $extension = $this->getExtensionFromMimeType($contentType);

        return $baseFilename.'.'.$extension;
    }

    /**
     * Obtém o conteúdo de um arquivo
     *
     * @param  string  $file  URL ou ID do arquivo
     * @return string Conteúdo do arquivo
     */
    private function getFileContent(string $file): string
    {
        if (preg_match('/drive\.google\.com/', $file)) {
            return $this->getPdfDrive($file);
        }

        return $this->showpdfsales($file);
    }

    /**
     * @inheritdoc
     */
    public function handleFileUpload(string $filename, string $file, bool $useQueue, bool $createImage): void
    {
        if ($useQueue) {
            // Usa uma fila para processar o upload
            ConvertAndUploadFileJob::dispatch($filename, $file, $createImage)
                ->onQueue('files')
                ->delay(now());
        } else {
            // Processa o upload imediatamente
            $this->uploadPdfS3($filename, $file, $createImage);
        }
    }

    /**
     * @inheritdoc
     */
    public function uploadFileWithQueue(
        string $id,
        string $filename,
        string $file,
        ServiceInterface $service,
        DtoInterface $dto,
        string $fieldName = 'LinkS3',
        bool $createImage = false
    ): void {
        // Usa uma fila para processar o upload e atualizar os dados
        UploadFileJob::dispatch($id, $filename, $file, $service, $dto, $fieldName, $createImage)
            ->onQueue('files')
            ->delay(now());
    }

    /**
     * @inheritdoc
     */
    public function uploadFileWithoutQueue(string $filename, string $file, bool $createImage): ?string
    {
        // Processa o upload imediatamente
        return $this->uploadPdfS3($filename, $file, $createImage);
    }

    /**
     * Formata o tamanho do arquivo em formato legível
     */
    private function formatFileSize(int $size): string
    {
        if ($size < 1024) {
            return $size . ' bytes';
        } elseif ($size < 1024 * 1024) {
            return round($size / 1024, 2) . ' KB';
        } elseif ($size < 1024 * 1024 * 1024) {
            return round($size / (1024 * 1024), 2) . ' MB';
        } else {
            return round($size / (1024 * 1024 * 1024), 2) . ' GB';
        }
    }
}
