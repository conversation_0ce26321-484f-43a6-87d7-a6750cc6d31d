<?php

declare(strict_types=1);

namespace App\DDD\Infrastructure\Shared\Services;

use App\DDD\Infrastructure\Shared\Interfaces\Services\ErrorCacheServiceInterface;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class ErrorCacheService implements ErrorCacheServiceInterface
{
    /**
     * Período padrão de throttling (em segundos)
     */
    const DEFAULT_THROTTLE_PERIOD = 3600; // 1 hora

    /**
     * Verifica se um erro específico já foi reportado recentemente
     *
     * @param  string  $route  Rota da API onde o erro ocorreu
     * @param  string  $errorMessage  Mensagem de erro
     * @param  array  $context  Dados de contexto (podem ser quaisquer dados)
     * @return bool True se o erro já foi reportado recentemente (não deve enviar email)
     */
    public static function isErrorRecentlyReported(string $route, string $errorMessage, array $context = []): bool
    {
        // Normaliza a mensagem de erro
        $normalizedError = self::normalizeErrorMessage($errorMessage);

        // Extrai identificadores estáveis do contexto
        $contextIdentifiers = self::extractContextIdentifiers($context);

        // Preserva o hash do payload original se existir
        if (isset($context['raw_payload_hash'])) {
            $contextIdentifiers['raw_payload_hash'] = $context['raw_payload_hash'];
        }

        // Cria uma assinatura do erro
        $errorKey = self::createErrorKey($route, $normalizedError, $contextIdentifiers);

        // Verifica se este erro está em cache
        $isCached = Cache::has($errorKey);

        Log::debug('Verificando cache de erro:', [
            'error_key' => $errorKey,
            'route' => $route,
            'normalized_error' => $normalizedError,
            'context_identifiers' => $contextIdentifiers,
            'is_cached' => $isCached,
        ]);

        return $isCached;
    }

    /**
     * Registra um erro para evitar duplicações de email
     *
     * @param  string  $route  Rota da API onde o erro ocorreu
     * @param  string  $errorMessage  Mensagem de erro
     * @param  array  $context  Dados de contexto (podem ser quaisquer dados)
     * @param  int  $throttlePeriod  Tempo em segundos para suprimir emails duplicados
     */
    public static function markErrorAsReported(
        string $route,
        string $errorMessage,
        array $context = [],
        int $throttlePeriod = self::DEFAULT_THROTTLE_PERIOD
    ): void {
        // Normaliza a mensagem de erro
        $normalizedError = self::normalizeErrorMessage($errorMessage);

        // Extrai identificadores estáveis do contexto
        $contextIdentifiers = self::extractContextIdentifiers($context);

        // Preserva o hash do payload original se existir
        if (isset($context['raw_payload_hash'])) {
            $contextIdentifiers['raw_payload_hash'] = $context['raw_payload_hash'];
        }

        // Cria a chave do erro
        $errorKey = self::createErrorKey($route, $normalizedError, $contextIdentifiers);

        // Armazena no cache
        Cache::put($errorKey, now()->format('Y-m-d H:i:s'), $throttlePeriod);

        // Registra todas as chaves de erro para limpeza futura
        $allKeys = Cache::get('all_error_keys', []);
        if (! in_array($errorKey, $allKeys)) {
            $allKeys[] = $errorKey;
            Cache::put('all_error_keys', $allKeys, 86400 * 30); // 30 dias
        }

        Log::info('Erro marcado como reportado:', [
            'error_key' => $errorKey,
            'throttle_period' => $throttlePeriod,
            'expires_at' => now()->addSeconds($throttlePeriod)->format('Y-m-d H:i:s'),
        ]);
    }

    /**
     * Normaliza a mensagem de erro para comparação consistente
     */
    private static function normalizeErrorMessage(string $errorMessage): string
    {
        // Remove datas/horas no formato dd/mm/yyyy hh:mm:ss
        $normalized = preg_replace('/\d{2}\/\d{2}\/\d{4}\s\d{2}:\d{2}:\d{2}/', '', $errorMessage);

        // Remove IDs específicos que podem variar
        $normalized = preg_replace('/\b[0-9a-f]{8}\b-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-\b[0-9a-f]{12}\b/', '<uuid>', $normalized);

        // Remove números específicos mantendo a estrutura
        $normalized = preg_replace('/\b(id|código|numero|number)\s*[:=]?\s*\d+\b/i', '$1:<número>', $normalized);

        // Remove números de requisição/solicitação
        $normalized = preg_replace('/\b(requisição|solicitação|pedido)\s+#?\d+\b/i', '$1:<número>', $normalized);

        // Remove linhas de código específicas
        $normalized = preg_replace('/\bline\s+\d+\b/i', 'line:<número>', $normalized);

        // Normaliza espaços em branco
        return trim(preg_replace('/\s+/', ' ', $normalized));
    }

    /**
     * Extrai identificadores estáveis do contexto para uso na chave do cache
     * Funciona com qualquer estrutura de dados
     */
    private static function extractContextIdentifiers(array $context): array
    {
        $identifiers = [];

        // Processamento recursivo de arrays
        foreach ($context as $key => $value) {
            // Pula o processamento do raw_payload_hash para preservar seu valor exato
            if ($key === 'raw_payload_hash') {
                continue;
            }

            // Ignora valores nulos ou vazios
            if (empty($value) && $value !== 0) {
                continue;
            }

            // Para valores escalares, processa de acordo com padrões conhecidos
            if (is_scalar($value)) {
                // Se for string, normaliza padrões conhecidos
                if (is_string($value)) {
                    // Para IDs com padrão específico, mantém apenas o padrão
                    if (preg_match('/^([A-Z]+-)\d+$/', $value, $matches)) {
                        $identifiers[$key] = $matches[1].'<número>';
                    }
                    // Para UUIDs, substitui por um placeholder
                    elseif (preg_match('/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i', $value)) {
                        $identifiers[$key] = '<uuid>';
                    }
                    // Para emails, substitui apenas o domínio
                    elseif (filter_var($value, FILTER_VALIDATE_EMAIL)) {
                        $identifiers[$key] = '<email>';
                    }
                    // Para outros strings, mantém apenas se não for muito longo
                    elseif (strlen($value) < 100) {
                        $identifiers[$key] = $value;
                    }
                }
                // Para números, mantém o tipo mas não o valor específico
                elseif (is_numeric($value)) {
                    $identifiers[$key] = is_float($value) ? '<float>' : '<int>';
                }
                // Para booleanos, mantém o valor
                elseif (is_bool($value)) {
                    $identifiers[$key] = $value;
                }
            }
            // Para arrays, processa recursivamente mas limita a profundidade
            elseif (is_array($value) && count($identifiers) < 10) {
                $recursiveResult = self::extractContextIdentifiers($value);
                if (! empty($recursiveResult)) {
                    $identifiers[$key] = $recursiveResult;
                }
            }
            // Para objetos, usa apenas o nome da classe
            elseif (is_object($value)) {
                $identifiers[$key] = get_class($value);
            }
        }

        return $identifiers;
    }

    /**
     * Cria uma chave de cache única para o erro
     */
    private static function createErrorKey(string $route, string $normalizedError, array $contextIdentifiers): string
    {
        // Extrai o hash do payload original se existir
        $payloadHash = '';
        if (isset($contextIdentifiers['raw_payload_hash'])) {
            $payloadHash = $contextIdentifiers['raw_payload_hash'];
            // Remove do array para não duplicar na serialização
            unset($contextIdentifiers['raw_payload_hash']);
        }

        // Limita o tamanho da chave para evitar problemas
        $errorBase = substr($normalizedError, 0, 100);
        $routeBase = substr($route, 0, 50);

        // Serializa o contexto de forma determinística
        $contextHash = md5(json_encode($contextIdentifiers, JSON_UNESCAPED_SLASHES));

        // Concatena os elementos essenciais, incluindo o hash do payload se disponível
        $errorSignature = $routeBase.'|'.$errorBase.'|'.$contextHash;
        if (! empty($payloadHash)) {
            $errorSignature .= '|'.$payloadHash;
        }

        // Cria um hash MD5 como chave de cache
        return 'error_email:'.md5($errorSignature);
    }

    /**
     * Limpa o cache de erros (útil para testes)
     */
    public static function clearErrorCache(): void
    {
        $keys = Cache::get('all_error_keys', []);
        foreach ($keys as $key) {
            Cache::forget($key);
        }
        Cache::forget('all_error_keys');
        Log::info('Cache de erros limpo');
    }

    /**
     * Verifica se deve enviar um email de erro e registra se enviado
     * Método de conveniência que combina verificação e registro
     */
    public static function shouldSendErrorEmail(
        string $route,
        string $errorMessage,
        array $context = [],
        int $throttlePeriod = self::DEFAULT_THROTTLE_PERIOD
    ): bool {
        Log::debug('ErrorCacheService::shouldSendErrorEmail chamado', [
            'route' => $route,
            'error_message' => $errorMessage,
            'context_keys' => array_keys($context),
            'has_payload_hash' => isset($context['raw_payload_hash']),
        ]);

        // Normaliza a mensagem para debug
        $normalizedError = self::normalizeErrorMessage($errorMessage);
        $contextIdentifiers = self::extractContextIdentifiers($context);

        // Preserva o hash do payload original se existir
        if (isset($context['raw_payload_hash'])) {
            $contextIdentifiers['raw_payload_hash'] = $context['raw_payload_hash'];
        }

        $errorKey = self::createErrorKey($route, $normalizedError, $contextIdentifiers);

        Log::debug('Informações de cache calculadas', [
            'normalized_error' => $normalizedError,
            'error_key' => $errorKey,
            'throttle_period' => $throttlePeriod,
            'payload_hash_used' => isset($context['raw_payload_hash']),
        ]);

        if (self::isErrorRecentlyReported($route, $errorMessage, $context)) {
            Log::info('Email de erro suprimido (já enviado recentemente)', [
                'route' => $route,
                'error' => $errorMessage,
                'error_key' => $errorKey,
            ]);

            return false;
        }

        // Marca como reportado para evitar duplicatas
        self::markErrorAsReported($route, $errorMessage, $context, $throttlePeriod);

        Log::info('Email de erro será enviado', [
            'route' => $route,
            'error' => $errorMessage,
            'throttle_period' => $throttlePeriod.' segundos',
            'error_key' => $errorKey,
        ]);

        return true;
    }
}
