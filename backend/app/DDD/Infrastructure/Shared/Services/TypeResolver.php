<?php

declare(strict_types=1);

namespace App\DDD\Infrastructure\Shared\Services;

/**
 * Classe responsável por resolver tipos de objeto e suas estratégias
 * Implementa o padrão Strategy para processamento baseado em tipo
 */
class TypeResolver
{
    /**
     * @var array Estratégias registradas para cada tipo
     */
    private array $strategies = [];

    /**
     * Registra uma estratégia para um tipo específico
     *
     * @param  string  $type  Tipo do objeto
     * @param  callable  $strategy  Função de estratégia
     */
    public function registerStrategy(string $type, callable $strategy): void
    {
        $this->strategies[$type] = $strategy;
    }

    /**
     * Verifica se existe uma estratégia para o tipo especificado
     *
     * @param  string  $type  Tipo do objeto
     */
    public function hasStrategy(string $type): bool
    {
        return isset($this->strategies[$type]);
    }

    /**
     * Aplica a estratégia registrada para o tipo aos dados
     *
     * @param  string  $type  Tipo do objeto
     * @param  array  $data  Dados a serem processados
     * @return array Dados transformados pela estratégia
     *
     * @throws \InvalidArgumentException Se o tipo não tiver estratégia registrada
     */
    public function apply(string $type, array $data): array
    {
        if (! $this->hasStrategy($type)) {
            throw new \InvalidArgumentException("Não há estratégia registrada para o tipo '{$type}'");
        }

        $strategy = $this->strategies[$type];
        $result = $strategy($data);

        // Verifica se o resultado é um array
        if (! is_array($result)) {
            // Log para depuração
            if (function_exists('Log::debug')) {
                \Illuminate\Support\Facades\Log::debug('A estratégia para o tipo '.$type.' retornou um valor não-array: '.gettype($result));
            }

            // Converte para array se for string ou outro tipo
            if (is_string($result) || is_numeric($result)) {
                return ['id' => $result, 'data' => []];
            } else {
                // Caso o resultado seja null ou outro tipo não suportado
                return ['id' => null, 'data' => []];
            }
        }

        return $result;
    }

    /**
     * Tenta aplicar uma estratégia para o tipo, ou retorna os dados originais
     * se não houver estratégia registrada
     *
     * @param  string  $type  Tipo do objeto
     * @param  array  $data  Dados a serem processados
     * @return array Dados processados ou originais
     */
    public function tryApply(string $type, array $data): array
    {
        if ($this->hasStrategy($type)) {
            return $this->apply($type, $data);
        }

        return $data;
    }
}
