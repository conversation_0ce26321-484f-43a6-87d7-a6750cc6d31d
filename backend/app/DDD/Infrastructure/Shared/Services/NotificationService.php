<?php

declare(strict_types=1);

namespace App\DDD\Infrastructure\Shared\Services;

use App\DDD\Infrastructure\Shared\Models\AdminSystem;
use App\DDD\Infrastructure\Shared\Notifications\ErrorNotification;
use App\DDD\Infrastructure\Shared\Notifications\StandardNotification;
use App\DDD\Infrastructure\Shared\Notifications\ValidationErrorNotification;
use Illuminate\Support\Facades\Log;
use Throwable;

class NotificationService
{
    /**
     * Trata o erro e envia email, respeitando throttling para evitar duplicatas
     *
     * @param  Throwable  $exception  A exceção que ocorreu
     * @param  mixed  $request  O objeto Request
     * @param  int  $statusCode  Código de status HTTP
     * @param  string|null  $customMessage  Mensagem personalizada (opcional)
     * @return array Informações de erro formatadas
     */
    public function handleErrorWithEmailThrottling(
        Throwable $exception,
        $request,
        int $statusCode = 500,
        ?array $trace = [],
        ?string $customMessage = null
    ): array {
        // Obtém o tipo de erro baseado na classe da exceção
        $errorType = class_basename($exception);
        if ($errorType === 'Exception' && str_contains($exception->getMessage(), 'Missing required fields')) {
            $errorType = 'ValidationException';
        }

        // Obtém o método em que o erro ocorreu, se disponível no trace
        $method = '';
        $trace = $exception->getTrace();
        if (!empty($trace) && isset($trace[0]['function'])) {
            $method = $trace[0]['function'];
        }

        // Constrói a mensagem de erro completa
        $errorDescription = $customMessage ?? $exception->getMessage() . '<br><br>' . $exception->getTraceAsString();
        $route = $request->route() ? $request->route()->uri() : 'API Endpoint';
        $requestData = $request->all();

        // Tenta enviar a notificação
        $this->notifyError($exception, $route, $requestData, $trace);

        // Retorna informações de erro formatadas
        return [
            'error' => $errorType . ($method ? " method {$method}" : ''),
            'message' => $errorDescription,
            'code' => $statusCode,
            'request' => $this->sanitizeRequestData($requestData),
            'trace' => $trace,
        ];
    }

    /**
     * Notifica administradores sobre erros com tratamento de throttling
     *
     * @param  Throwable  $exception  A exceção que ocorreu
     * @param  string  $route  A rota onde o erro ocorreu
     * @param  array  $requestData  Os dados da requisição (serão sanitizados)
     * @return bool Se a notificação foi enviada com sucesso
     */
    public function notifyError(
        Throwable $exception,
        string $route,
        array $requestData,
        ?array $trace = [],
    ): bool {
        $originalErrorMessage = $exception->getMessage();

        // Cria um identificador específico para o payload se contiver chaves como 'pv'
        $specificPayloadId = '';
        if (isset($requestData['pv'])) {
            $specificPayloadId = 'pv_' . $requestData['pv'];
        } elseif (isset($requestData['id'])) {
            $specificPayloadId = 'id_' . $requestData['id'];
        } elseif (!empty($requestData)) {
            // Se não tiver chaves específicas mas tiver dados, gera uma hash
            $specificPayloadId = 'payload_' . md5(json_encode($requestData));
        }

        // Define o contexto para cache
        $context = [
            'request_data' => $requestData,
            'exception_class' => get_class($exception),
            'exception_code' => $exception->getCode(),
            'method' => request()->method(),
        ];

        // Adiciona o identificador específico do payload ao contexto
        if (!empty($specificPayloadId)) {
            Log::debug('Usando identificador específico de payload', [
                'specific_payload_id' => $specificPayloadId,
            ]);
            $context['specific_payload_id'] = $specificPayloadId;
        }

        $cacheKey = $this->generateCacheKey($route, $originalErrorMessage, $specificPayloadId);
        Log::debug('Cache key gerada: ' . $cacheKey);

        // Verifica cache para evitar duplicações usando a chave personalizada
        if ($this->shouldSendErrorEmail($cacheKey)) {
            try {
                $this->sendErrorNotification($exception, $route, $this->sanitizeRequestData($requestData), $trace);
                $this->markErrorAsSent($cacheKey);

                return true;
            } catch (Throwable $emailError) {
                Log::error('Erro ao enviar email de notificação: ' . $emailError->getMessage());

                return false;
            }
        }

        return false;
    }

    /**
     * Gera uma chave de cache única baseada na rota, mensagem de erro e payload específico
     */
    private function generateCacheKey(string $route, string $error, string $specificPayloadId = ''): string
    {
        // Normaliza a mensagem de erro (versão simplificada)
        $normalizedError = preg_replace('/\s+/', ' ', trim($error));
        $normalizedRoute = substr($route, 0, 50);

        if (!empty($specificPayloadId)) {
            return 'error_email:' . md5($normalizedRoute . '|' . $normalizedError . '|' . $specificPayloadId);
        } else {
            return 'error_email:' . md5($normalizedRoute . '|' . $normalizedError);
        }
    }

    /**
     * Verifica se um email de erro deve ser enviado
     */
    private function shouldSendErrorEmail(string $cacheKey): bool
    {
        if (\Illuminate\Support\Facades\Cache::has($cacheKey)) {
            Log::info('Email de erro suprimido (já enviado recentemente)', [
                'cache_key' => $cacheKey,
            ]);

            return false;
        }

        Log::info('Email de erro será enviado', [
            'cache_key' => $cacheKey,
        ]);

        return true;
    }

    /**
     * Marca um erro como já enviado
     */
    private function markErrorAsSent(string $cacheKey, int $throttlePeriod = 3600): void
    {
        \Illuminate\Support\Facades\Cache::put($cacheKey, now()->format('Y-m-d H:i:s'), $throttlePeriod);
        Log::info('Erro marcado como reportado', [
            'cache_key' => $cacheKey,
            'expires_at' => now()->addSeconds($throttlePeriod)->format('Y-m-d H:i:s'),
        ]);
    }

    /**
     * Notifica administradores sobre erros de validação
     *
     * @param  Throwable  $exception  A exceção de validação
     * @param  array  $data  Os dados que falharam na validação
     * @param  array  $validationErrors  Lista de erros de validação
     * @param  Throwable|null  $originalException  Exceção original (se existir)
     * @param  string  $context  Contexto da validação
     * @return bool Se a notificação foi enviada com sucesso
     */
    public function notifyValidationError(
        Throwable $exception,
        array $data,
        array $validationErrors = [],
        ?Throwable $originalException = null,
        string $context = 'Data Validation'
    ): bool {
        try {
            // Sanitiza dados sensíveis
            $sanitizedData = $this->sanitizeRequestData($data);

            // Cria contexto para a notificação
            $contextData = [
                'data' => $sanitizedData,
                'validation_errors' => $validationErrors,
                'rules' => [],
                'original_exception' => $originalException ? [
                    'message' => $originalException->getMessage(),
                    'file' => $originalException->getFile(),
                    'line' => $originalException->getLine(),
                ] : null,
            ];

            Log::debug('Sending validation error notification');
            $trace = isset($originalException) ? $originalException->getTrace() : [];
            $this->sendErrorNotification($exception, $context, $contextData, $trace);

            return true;
        } catch (Throwable $notificationError) {
            Log::error('Falha ao enviar notificação de erro de validação: ' . $notificationError->getMessage());
            Log::error('Detalhes do erro: ' . $notificationError->getTraceAsString());

            return false;
        }
    }

    /**
     * Envia notificação de erro para administradores
     *
     * @param  Throwable  $exception  A exceção que ocorreu
     * @param  string  $context  O contexto onde ocorreu o erro
     * @param  array  $contextData  Dados adicionais de contexto
     */
    private function sendErrorNotification(
        Throwable $exception,
        string $context,
        array $contextData,
        ?array $trace = []
    ): void {
        try {
            $adminEmails = config('user.admin_emails', '<EMAIL>');
            $adminEmails = is_array($adminEmails) ? implode(',', array_filter($adminEmails)) : $adminEmails;
            Log::debug('Enviando notificação para: ' . json_encode($adminEmails));

            $admin = new AdminSystem($adminEmails);

            // Decide qual tipo de notificação enviar com base no tipo de exceção
            $isValidationError = str_contains(get_class($exception), 'Validation') ||
                str_contains($exception->getMessage(), 'validation');

            if ($isValidationError) {
                $notification = new ValidationErrorNotification(
                    $exception,
                    $context,
                    $contextData,
                    $trace
                );
            } else {
                $notification = new ErrorNotification(
                    $exception,
                    $context,
                    $contextData,
                    $trace
                );
            }

            Log::debug('Enviando notificação diretamente para o admin');
            $admin->notify($notification);
            Log::debug('Notificação enviada com sucesso');
        } catch (Throwable $notificationError) {
            Log::error('Falha ao enviar notificação: ' . $notificationError->getMessage());
            Log::error($notificationError->getTraceAsString());

            throw $notificationError; // Re-throw para tratamento superior
        }
    }

    /**
     * Remove informações sensíveis dos dados da requisição
     *
     * @param  array  $data  Os dados a serem sanitizados
     * @return array Dados sanitizados
     */
    public function sanitizeRequestData(array $data): array
    {
        $sanitized = $data;
        $sensitiveFields = ['password', 'senha', 'token', 'secret', 'credit_card', 'cartao', 'api_key'];

        foreach ($sanitized as $key => &$value) {
            if (is_string($key) && in_array(strtolower($key), $sensitiveFields)) {
                $value = '******';
            } elseif (is_array($value)) {
                $value = $this->sanitizeRequestData($value);
            }
        }

        return $sanitized;
    }

    /**
     * Obtém a mensagem de erro completa, incluindo mensagens de exceções internas
     *
     * @param  Throwable  $exception  A exceção
     * @return string Mensagem de erro completa
     */
    public function getFullErrorMessage(Throwable $exception): string
    {
        $message = $exception->getMessage();

        if (config('app.debug')) {
            $message .= ' in ' . $exception->getFile() . ':' . $exception->getLine();
        }

        // Verifica se há uma exceção anterior (interna)
        if ($previous = $exception->getPrevious()) {
            $message .= ' | Caused by: ' . $this->getFullErrorMessage($previous);
        }

        return $message;
    }

    /**
     * Formata os dados da proposta para exibição no email
     *
     * @param array|string $data Dados brutos da proposta ou mensagem de erro
     * @return array Dados formatados
     */
    private function formatProposalData(array|string $data): array
    {
        // Se $data for uma string, retorna um array com a chave 'error'
        if (is_string($data)) {
            return ['error' => $data];
        }

        $formattedData = [];

        // Formata dados do Cliente
        if (isset($data['user'])) {
            $formattedData['Cliente'] = [
                'Id' => $data['user']['Id'] ?? '',
                'Nome' => $this->decodeUtf8($data['user']['Name'] ?? ''),
                'Email' => $data['user']['Email__c'] ?? '',
                'CPF' => $data['user']['CPF__c'] ?? '',
                'CNPJ' => $data['user']['CNPJ__c'] ?? '',
                'Cod Sienge' => $data['user']['CodigoSienge__c'] ?? '',
            ];
        }

        // Formata dados da Proposta
        if (isset($data['proposta'])) {
            $formattedData['Proposta'] = [
                'Id' => $data['proposta']['Id'] ?? '',
                'Número (PV)' => $data['proposta']['Name'] ?? '',
                'Status Financiamento' => $this->decodeUtf8($data['proposta']['StatusFinanciamento__c'] ?? ''),
                'Data de Criação' => $this->formatDate($data['proposta']['CreatedDate'] ?? ''),
            ];
        }

        // // Formata dados do Asset (Unidade)
        if (isset($data['proposta']['Unidade__r'])) {
            $asset = $data['proposta']['Unidade__r']['Ativo__r'] ?? [];
            $formattedData['Asset'] = [
                'Id' => $asset['Id'] ?? '',
                'Nome' => $this->decodeUtf8($asset['Name'] ?? ''),
            ];
        }

        // Formata dados do Contrato
        if (isset($data['contracts'])) {
            $formattedData['Contrato'] = [
                'Id' => $data['contracts']['Id'] ?? $data['contracts']['ContractId'] ?? '',
                'Número' => $data['contracts']['ContractNumber'] ?? '',
                'Status' => $this->decodeUtf8($data['contracts']['Status'] ?? ''),
                'Status Carteira' => $this->decodeUtf8($data['contracts']['StatusCarteira__c'] ?? ''),
                'Situação Entrega' => $this->decodeUtf8($data['contracts']['SituacaoEntrega__c'] ?? ''),
                'Data da Compra' => $this->formatDate($data['contracts']['DataCompra__c'] ?? ''),
                'Contrato Com Programa Fidelidade' => $data['contracts']['ContratoComProgramaFidelidade__c'] ?? '',
                'Data Adesão Programa Fidelidade' => $this->formatDate($data['contracts']['DataAdesaoProgramaFidelidade__c'] ?? ''),
                'Data Chaves' => $this->formatDate($data['contracts']['DataChaves__c'] ?? ''),
            ];
        }

        // Formata dados do Empreendimento
        if (isset($data['empreendimento'])) {
            $formattedData['Empreendimento'] = [
                'Id' => $data['empreendimento']['Id'] ?? '',
                'Nome' => $this->decodeUtf8($data['empreendimento']['Name'] ?? ''),
            ];
        }

        return $formattedData;
    }


    /**
     * Decodifica strings UTF-8 com caracteres especiais
     */
    private function decodeUtf8(string $text): string
    {
        return json_decode("\"{$text}\"") ?? $text;
    }

    /**
     * Formata data para o padrão brasileiro
     */
    private function formatDate(?string $date): string
    {
        if (!$date)
            return '';
        return date('d/m/Y', strtotime($date));
    }

    /**
     * Envia notificação sobre criação de proposta
     *
     * @param string $subject Assunto da notificação
     * @param array|string $data Dados da proposta ou mensagem de erro
     * @param string $context Contexto da notificação
     * @return bool Se a notificação foi enviada com sucesso
     */
    public function sendCreateProposalNotification(
        string $subject,
        array|string $data,
        string $context = 'Criação de proposta'
    ): bool {
        try {
            $adminEmails = config('user.admin_emails', '<EMAIL>');
            $adminEmails = is_array($adminEmails) ? implode(',', array_filter($adminEmails)) : $adminEmails;
            Log::debug('Enviando notificação de proposta para: ' . json_encode($adminEmails));

            $admin = new AdminSystem($adminEmails);

            // Formata os dados antes de enviar
            $formattedData = $this->formatProposalData($data);

            // Se for uma mensagem de erro, ajusta o assunto para indicar isso
            if (isset($formattedData['error'])) {
                $subject = "[ERRO] {$subject}";
                $context = "Erro na {$context}";
            }

            // Cria o contexto para a notificação
            $contextData = [
                'subject' => $subject,
                'data' => $formattedData,
                'timestamp' => now()->format('d/m/Y H:i:s'),
                'environment' => config('app.env')
            ];

            // Envia a notificação
            $notification = new StandardNotification(
                $subject,
                $context,
                $contextData
            );

            $admin->notify($notification);
            Log::debug('Notificação de proposta enviada com sucesso');

            return true;
        } catch (Throwable $notificationError) {
            Log::error('Falha ao enviar notificação de proposta: ' . $notificationError->getMessage());
            Log::error($notificationError->getTraceAsString());

            return false;
        }
    }
}
