<?php

declare(strict_types=1);

namespace App\DDD\Infrastructure\Shared\Processors;

use App\DDD\Infrastructure\External\Salesforce\Interfaces\SalesforceServiceInterface;
use App\DDD\Infrastructure\Shared\Abstracts\Processors\AbstractProcessor;
use App\DDD\Infrastructure\Shared\Interfaces\DTO\DtoInterface;
use App\DDD\Infrastructure\Shared\Interfaces\Extractors\ImageExtractorInterface;
use App\DDD\Infrastructure\Shared\Interfaces\Processors\CarouselImageProcessorInterface;
use App\DDD\Infrastructure\Shared\Processors\Queries\GetImagesCarousel;
use App\DDD\Infrastructure\Shared\Services\ServiceLocator;
use Illuminate\Support\Collection;

/**
 * Processador para imagens de carrossel
 */
class CarouselImageProcessor extends AbstractProcessor implements CarouselImageProcessorInterface
{
    /**
     * @var ImageExtractorInterface Extrator de imagens
     */
    private readonly ImageExtractorInterface $imageExtractor;

    /**
     * @var SalesforceServiceInterface Serviço do Salesforce
     */
    private readonly SalesforceServiceInterface $salesforceService;

    /**
     * Construtor
     *
     * @param  ImageExtractorInterface  $imageExtractor  Extrator de imagens
     * @param  SalesforceServiceInterface  $salesforceService  Serviço do Salesforce
     * @param  ServiceLocator|null  $serviceLocator  Localizador de serviços (opcional)
     */
    public function __construct(
        ImageExtractorInterface $imageExtractor,
        SalesforceServiceInterface $salesforceService,
        ?ServiceLocator $serviceLocator = null
    ) {
        parent::__construct(null, $serviceLocator);

        $this->imageExtractor = $imageExtractor;
        $this->salesforceService = $salesforceService;

        // Registra serviços no localizador
        if ($serviceLocator) {
            $serviceLocator->register('imageExtractor', $imageExtractor);
            $serviceLocator->register('salesforceService', $salesforceService);
        }
    }

    /**
     * @inheritdoc
     */
    public function getSchemaClass(): string
    {
        // Esta implementação não precisa de um schema específico
        return '';
    }

    /**
     * @inheritdoc
     */
    public function process(array $data): DtoInterface|Collection
    {
        // Valida se os parâmetros necessários estão presentes
        if (! isset($data['table']) || ! isset($data['Id'])) {
            return collect([]);
        }

        $params = [
            'table' => $data['table'],
            'id' => $data['Id'],
        ];

        $carouselData = $this->getImagesCarousel($params);

        return collect($this->parseData($carouselData));
    }

    /**
     * @inheritdoc
     */
    public function parseData(array $data): array
    {
        $imgsCarrossel = [];

        // Verifica se existem registros de slides de carrossel
        if (isset($data['records'][0]['cloudx_cms__Carousel_Slides__r']['records'])) {
            foreach ($data['records'][0]['cloudx_cms__Carousel_Slides__r']['records'] as $img) {
                $arrayImage = $this->imageExtractor->extract(
                    $img['Imagem__c'],
                    $img['Id'],
                    'cloudx_cms__SS_Carousel_Slide__c',
                    'Imagem__c'
                );

                if ($arrayImage && count($arrayImage) > 0) {
                    $imgsCarrossel[] = $arrayImage[0];
                }
            }
        }

        return $imgsCarrossel;
    }

    /**
     * Obtém imagens de carrossel do Salesforce
     *
     * @param  array  $params  Parâmetros da consulta
     * @return array Dados de imagens de carrossel
     */
    private function getImagesCarousel(array $params): array
    {
        $paramsQuery = [
            'params' => $params,
            'queryType' => new GetImagesCarousel(),
        ];

        return $this->salesforceService->queryObject('query', $paramsQuery);
    }
}
