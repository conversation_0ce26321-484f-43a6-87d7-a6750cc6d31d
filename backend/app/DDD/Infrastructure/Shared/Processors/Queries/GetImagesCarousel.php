<?php

declare(strict_types=1);

namespace App\DDD\Infrastructure\Shared\Processors\Queries;

use App\DDD\Infrastructure\Shared\Abstracts\Queries\AbstractQueries;
use App\DDD\Infrastructure\Shared\Interfaces\Queries\QueryStrategyInterface;

class GetImagesCarousel extends AbstractQueries implements QueryStrategyInterface
{
    public function execute(): string
    {
        return "SELECT Id, (SELECT Id, Imagem__c, Posicao__c, Name
             FROM
                cloudx_cms__Carousel_Slides__r
             ORDER BY
                 Posicao__c)
             FROM
                 cloudx_cms__SS_Carousel__c
             WHERE
                 {$this->getParams()['table']}.Id='{$this->getParams()['id']}'";
    }
}
