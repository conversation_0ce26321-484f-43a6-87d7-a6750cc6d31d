<?php

declare(strict_types=1);

namespace App\DDD\Infrastructure\Shared\Abstracts\Controllers;

use App\DDD\Infrastructure\Shared\Http\Responses\DefaultResponse;
use App\DDD\Infrastructure\Shared\Interfaces\Controllers\ControllerInterface;
use App\DDD\Infrastructure\Shared\Interfaces\Response\ResponseInterface;
use App\DDD\Infrastructure\Shared\Interfaces\Services\ServiceInterface;
use App\DDD\Infrastructure\Shared\Services\NotificationService;
use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\Response;
use Throwable;

abstract class AbstractController extends Controller implements ControllerInterface
{
    protected ?NotificationService $notificationService = null;

    public function __construct(
        ?NotificationService $notificationService,
        private readonly ServiceInterface $service,
    ) {
        $this->notificationService = $notificationService;
    }

    protected function response(ResponseInterface $response): JsonResponse
    {
        return response()->json($response->toArray(), $response->getCode());
    }

    protected function errorResponse(string $error, ?string $message = null, int $status = Response::HTTP_BAD_REQUEST): JsonResponse
    {
        $return =  $this->response(new DefaultResponse(
            null,
            $status,
            ['error' => $error, 'message' => $message]
        ));
        return $return;
    }

    public function index(?Request $request): JsonResponse
    {
        return $this->response(new DefaultResponse(['message' => 'abstract controller index']));
    }

    public function show(?Request $request): JsonResponse
    {
        return $this->response(new DefaultResponse(['message' => 'abstract controller show']));
    }

    public function store(Request $request): JsonResponse
    {
        return $this->response(new DefaultResponse(['message' => 'abstract controller store']));
    }

    public function update(Request $request): JsonResponse
    {
        return $this->response(new DefaultResponse(['message' => 'abstract controller update']));
    }

    public function destroy(Request $request): JsonResponse
    {
        return $this->response(new DefaultResponse(['message' => 'abstract controller destroy']));
    }

    /**
     * Helper method to handle errors and send notifications
     *
     * @param  mixed  $request
     */
    public function handleError(Throwable $exception, $request, int $statusCode, ?string $customMessage = null): JsonResponse
    {
        // Ensure notificationService is initialized
        if ($this->notificationService === null) {
            $this->notificationService = app(NotificationService::class);
        }

        // Handle the error with the notification service
        $errorData = $this->notificationService->handleErrorWithEmailThrottling(
            $exception,
            $request,
            $statusCode,
            $exception->getTrace(),
            $customMessage
        );

        // Return formatted error response
        return $this->response(new DefaultResponse(
            null,
            $statusCode,
            $errorData,
        ));
    }
}
