<?php

declare(strict_types=1);

namespace App\DDD\Infrastructure\Shared\Abstracts\Persistence;

use App\DDD\Infrastructure\Shared\Interfaces\DTO\DtoInterface;
use App\DDD\Infrastructure\Shared\Interfaces\Enum\ServiceTypeEnumInterface;
use App\DDD\Infrastructure\Shared\Interfaces\Factories\ServiceFactoryInterface;
use App\DDD\Infrastructure\Shared\Interfaces\Persistence\PersistenceServiceInterface;
use App\DDD\Infrastructure\Shared\Interfaces\Services\ServiceInterface;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Throwable;

abstract class AbstractPersistenceService implements PersistenceServiceInterface
{
    public function __construct(
        protected readonly ServiceFactoryInterface $serviceFactory
    ) {
    }

    /**
     * Get the enum class that defines the service types
     *
     * @return string The fully qualified class name of the enum
     */
    abstract protected function getServiceTypeEnum(): string;

    /**
     * Persist all DTOs from the provided collection or array
     *
     * @throws Throwable
     */
    public function persistAll(array|Collection $dtos): array
    {
        try {
            DB::beginTransaction();

            $result = [];
            $enumClass = $this->getServiceTypeEnum();

            foreach ($enumClass::cases() as $serviceType) {
                if (! $serviceType instanceof ServiceTypeEnumInterface) {
                    continue;
                }

                $serviceInstance = $this->serviceFactory->makeService($serviceType);
                $dto = $dtos[$serviceType->getValue()];
                $result[$serviceType->getValue()] = $this->persistData($serviceInstance, $dto);
            }

            DB::commit();

            return $result;
        } catch (Throwable $e) {
            DB::rollBack();
            Log::error('Erro ao persistir dados', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            // Chama o hook de erro para permitir tratamento personalizado
            $this->onPersistenceError($e, $dtos);

            throw $e;
        }
    }

    private function persistData(ServiceInterface $serviceInstance, DtoInterface|Collection|null $data): Model|Collection|null
    {
        // Verifica se é uma Collection do Laravel
        if ($data instanceof Collection) {
            if ($data->count() > 0) {
                return $serviceInstance->checkAndCreateOrUpdate($data);
            }
        }
        // Verifica se é um DTO individual
        elseif ($data instanceof DtoInterface) {
            return $serviceInstance->checkAndCreateOrUpdate($data);
        }
        // Verifica se é um array
        elseif (is_array($data) && ! empty($data)) {
            return $serviceInstance->checkAndCreateOrUpdate(new Collection($data));
        }

        return null;
    }

    /**
     * Hook method called when an error occurs during persistence
     */
    protected function onPersistenceError(Throwable $exception, array|Collection $dtos): void
    {
        // Por padrão não faz nada, mas pode ser sobrescrito pelas classes filhas
    }
}
