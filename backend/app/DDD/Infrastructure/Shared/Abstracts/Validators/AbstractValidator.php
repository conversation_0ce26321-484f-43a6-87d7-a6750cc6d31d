<?php

declare(strict_types=1);

namespace App\DDD\Infrastructure\Shared\Abstracts\Validators;

use App\DDD\Infrastructure\Shared\Abstracts\Exception\AbstractValidationException;
use App\DDD\Infrastructure\Shared\Interfaces\Validators\ValidatorDataInterface;
use App\DDD\Infrastructure\Shared\Services\NotificationService;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Throwable;

/**
 * Classe base para validadores de dados
 */
abstract class AbstractValidator extends Validator implements ValidatorDataInterface
{
    /**
     * Regras de validação para banco de dados
     */
    protected array $rules_database = [];

    /**
     * Regras de validação para Salesforce
     */
    protected array $rules_salesforce = [];

    /**
     * Mensagem de erro para validação de banco de dados
     */
    protected string $messages_database = '';

    /**
     * Mensagem de erro para validação do Salesforce
     */
    protected string $messages_salesforce = '';

    /**
     * Serviço de notificação
     */
    protected ?NotificationService $notificationService = null;

    /**
     * Construtor
     *
     * @param  NotificationService|null  $notificationService  Serviço de notificação
     */
    public function __construct(?NotificationService $notificationService = null)
    {
        $this->notificationService = $notificationService;
        $this->buildValidationRules();
    }

    /**
     * Obtém as regras de validação para banco de dados
     */
    public function rules_database(): array
    {
        return $this->rules_database;
    }

    /**
     * Obtém as regras de validação para Salesforce
     */
    public function rules_salesforce(): array
    {
        return $this->rules_salesforce;
    }

    /**
     * Obtém a mensagem de erro para validação de banco de dados
     */
    public function messages_database(): string
    {
        return $this->messages_database;
    }

    /**
     * Obtém a mensagem de erro para validação do Salesforce
     */
    public function messages_salesforce(): string
    {
        return $this->messages_salesforce;
    }

    /**
     * Constrói as regras de validação com base no Schema
     */
    protected function buildValidationRules(): void
    {
        $schema = $this->getSchemaClass();
        if ($schema !== '') {
            $defaultRule = 'nullable';

            foreach ($schema::FIELDS as $field => $rules) {
                $this->rules_database[$field] = $rules ?: $defaultRule;
            }

            $this->messages_database = $schema::MESSAGES_DATABASE;
            $this->messages_salesforce = $schema::MESSAGES_SALESFORCE;
        }
    }

    /**
     * Valida dados do Salesforce
     *
     * @param  array  &$data  Dados a serem validados
     * @return bool Resultado da validação
     *
     * @throws AbstractValidationException Se os dados forem inválidos
     */
    public function isValidateSalesforceData(array &$data): bool
    {
        try {
            $this->validateRequiredFieldsSalesforce($data);
        } catch (\InvalidArgumentException $e) {
            $validator = Validator::make([], []);
            $errorMessage = $this->messages_salesforce() . $e->getMessage();
            $validator->errors()->add('global', $errorMessage);
            $exception = new AbstractValidationException($errorMessage);

            // Inicializa o serviço de notificação se necessário
            if ($this->notificationService === null) {
                $this->notificationService = app(NotificationService::class);
            }

            // Notifica o erro
            $this->notificationService->notifyValidationError(
                $exception,
                $data,
                [$errorMessage],
                $e,
                'Salesforce Required Fields Validation'
            );

            throw $exception;
        }

        // Continua com a validação existente
        $validator = null;

        try {
            $validator = Validator::make($data, $this->rules_salesforce);
            if ($validator->fails()) {
                $errorMessage = $this->messages_salesforce() . implode(', ', $validator->errors()->all());
                $validator->errors()->add('global', $errorMessage);
                $exception = new AbstractValidationException($validator->errors()->all()[0]);

                // Usa o serviço de notificação
                if ($this->notificationService === null) {
                    $this->notificationService = app(NotificationService::class);
                }

                $this->notificationService->notifyValidationError(
                    $exception,
                    $data,
                    $validator->errors()->all(),
                    null,
                    'Salesforce Data Validation'
                );

                throw $exception;
            }

            return true;
        } catch (AbstractValidationException $e) {
            if ($this->notificationService === null) {
                $this->notificationService = app(NotificationService::class);
            }
            $this->notificationService->notifyValidationError($e, $data, [], null, 'Salesforce Data Validation');

            throw $e;
        } catch (Throwable $e) {
            if (!$validator) {
                $validator = Validator::make([], []);
            }
            $errorMessage = $this->messages_salesforce() . $e->getMessage();
            $validator->errors()->add('global', $errorMessage);
            $exception = new AbstractValidationException($validator->errors()->all()[0]);

            // Inicializa o serviço de notificação se necessário
            if ($this->notificationService === null) {
                $this->notificationService = app(NotificationService::class);
            }

            // Notifica o erro
            $this->notificationService->notifyValidationError(
                $exception,
                $data,
                [$errorMessage],
                $e,
                'Salesforce Data Validation'
            );

            throw $exception;
        }
    }

    /**
     * Valida dados contra regras de banco de dados
     *
     * @param  array  &$data  Dados a serem validados
     * @return bool Resultado da validação
     *
     * @throws AbstractValidationException Se os dados forem inválidos
     */
    public function isValidateRulesDatabase(array &$data): bool
    {
        // Verifica campos obrigatórios primeiro
        try {
            $this->validateRequiredFieldsDatabase($data);
        } catch (\InvalidArgumentException $e) {
            $validator = Validator::make([], []);
            $errorMessage = $this->messages_database() . $e->getMessage();
            $validator->errors()->add('global', $errorMessage);
            $exception = new AbstractValidationException($errorMessage);

            // Inicializa o serviço de notificação se necessário
            if ($this->notificationService === null) {
                $this->notificationService = app(NotificationService::class);
            }

            // Notifica o erro
            $this->notificationService->notifyValidationError(
                $exception,
                $data,
                [$errorMessage],
                null,
                'Database Required Fields Validation'
            );

            throw $exception;
        }

        $validator = null;

        try {
            $validator = Validator::make($data, $this->rules_database);

            if ($validator->fails()) {
                $validator->errors()->add('global', $this->messages_database() . implode(', ', $validator->errors()->all()));
                $exception = new AbstractValidationException($validator->errors()->all()[0]);

                if ($this->notificationService === null) {
                    $this->notificationService = app(NotificationService::class);
                }

                $this->notificationService->notifyValidationError(
                    $exception,
                    $data,
                    $validator->errors()->all(),
                    null,
                    'Database Validation'
                );

                throw $exception;
            }

            return true;
        } catch (AbstractValidationException $e) {
            throw $e;
        } catch (\Exception $e) {
            if (!$validator) {
                $validator = Validator::make([], []);
            }
            $validator->errors()->add('global', $this->messages_database() . $e->getMessage());
            $exception = new AbstractValidationException($validator->errors()->all()[0]);

            if ($this->notificationService === null) {
                $this->notificationService = app(NotificationService::class);
            }

            $this->notificationService->notifyValidationError(
                $exception,
                $data,
                [$e->getMessage()],
                $e,
                'Database Validation'
            );

            throw $exception;
        }
    }

    /**
     * Valida campos obrigatórios do banco de dados
     *
     * @param  array  &$data  Dados a serem validados
     *
     * @throws \InvalidArgumentException Se campos obrigatórios estiverem faltando
     */
    public function validateRequiredFieldsDatabase(array &$data): void
    {
        $schemaClass = $this->getSchemaClass();
        if ($schemaClass !== '') {
            $requiredFields = $schemaClass::getRequiredFieldsDatabase();
            $type = data_get($data, 'attributes.type');
            if (isset($type) && isset($requiredFields[$type])) {
                $requiredFields = $requiredFields[$type];
            }

            // Encontra campos obrigatórios faltantes
            $missingFields = [];

            foreach ($requiredFields as $field) {
                // Verifica se $field é um valor escalar antes de usar array_key_exists
                if (!is_scalar($field)) {
                    Log::warning('Campo obrigatório não é um tipo escalar válido', ['field' => $field]);

                    continue;
                }

                if (!array_key_exists($field, $data) || $data[$field] === null || $data[$field] === '') {
                    $missingFields[] = $field;
                }
            }

            if (!empty($missingFields)) {
                throw new \InvalidArgumentException(
                    'Missing required database fields: ' . implode(', ', $missingFields)
                );
            }
        }
    }

    /**
     * Valida campos obrigatórios do Salesforce
     *
     * @param  array  &$data  Dados a serem validados
     *
     * @throws \InvalidArgumentException Se campos obrigatórios estiverem faltando
     */
    public function validateRequiredFieldsSalesforce(array &$data): void
    {
        $schemaClass = $this->getSchemaClass();
        if ($schemaClass !== '') {
            $type = data_get($data, 'attributes.type');

            $requiredFields = $schemaClass::getRequiredFieldsSalesforce();
            $missingFields = [];

            if (isset($type) && isset($requiredFields[$type])) {
                $requiredFields = $requiredFields[$type];
                $missingFields = $this->checkNestedFields($data, $requiredFields);
            }

            if (!empty($missingFields)) {
                throw new \InvalidArgumentException(
                    'Missing required Salesforce fields: ' . implode(', ', $missingFields)
                );
            }
        }
    }

    /**
     * Verifica campos aninhados nos dados
     *
     * @param  array  $data  Dados a serem verificados
     * @param  array  $fields  Campos a serem verificados
     * @return array Campos faltantes
     */
    private function checkNestedFields(array $data, array $fields): array
    {
        $missingFields = [];

        foreach ($fields as $field) {
            $keys = explode('.', $field);
            $current = $data;

            foreach ($keys as $key) {
                if (!is_array($current) || !array_key_exists($key, $current)) {
                    $missingFields[] = $field;

                    break;
                }
                $current = $current[$key];
            }
        }

        return $missingFields;
    }

    /**
     * Obtém a classe do schema a ser utilizado para validação
     * Deve ser implementado pelas classes concretas
     *
     * @return string Nome completo da classe do schema
     */
    abstract public function getSchemaClass(): string;
}
