<?php

declare(strict_types=1);

namespace App\DDD\Infrastructure\Shared\Abstracts\Bindings;

use App\DDD\Infrastructure\Shared\Interfaces\Bindings\BindingsInterface;
use Illuminate\Contracts\Foundation\Application;

abstract class AbstractBindings implements BindingsInterface
{
    public function register(Application $app): void
    {
        $this->registerSupportServices($app);
        $this->registerFactories($app);
        $this->registerQueries($app);
        $this->registerValidators($app);
        $this->registerStrategies($app);
        $this->registerHandlers($app);
        $this->registerRepositories($app);
        $this->registerProcessors($app);
        $this->registerServices($app);
        $this->registerOptionalDependencies($app);
    }

    public function registerSupportServices(Application $app): void
    {
    }

    public function registerServices(Application $app): void
    {
    }

    public function registerFactories(Application $app): void
    {
    }

    public function registerQueries(Application $app): void
    {
    }

    public function registerValidators(Application $app): void
    {
    }

    public function registerStrategies(Application $app): void
    {
    }

    public function registerHandlers(Application $app): void
    {
    }

    public function registerRepositories(Application $app): void
    {
    }

    public function registerProcessors(Application $app): void
    {
    }

    public function registerOptionalDependencies(Application $app): void
    {
    }
}
