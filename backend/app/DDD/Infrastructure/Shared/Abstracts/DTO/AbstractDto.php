<?php

declare(strict_types=1);

namespace App\DDD\Infrastructure\Shared\Abstracts\DTO;

use App;
use App\DDD\Infrastructure\Shared\Interfaces\DTO\DtoInterface;
use App\DDD\Infrastructure\Shared\Interfaces\Validators\ValidatorDataInterface;

abstract class AbstractDto implements DtoInterface
{
    public array $data;

    protected static array $attributes = [];

    abstract protected static function getClass(array $data = []): self;

    abstract public static function getSchemaClass(): string;

    public function __construct(array $data)
    {
        $this->data = $data;
        $this->initializeFromSchema($data);
    }

    public function initializeFromSchema(array $data): void
    {
        if(static::getSchemaClass() != ''){
            foreach (static::getSchemaClass()::getFillable() as $field) {
                self::$attributes[$field] = $data[$field] ?? null;
            }
        }
    }

    protected static function create(array $data, bool $validate = true)
    {
        $instance = static::getClass($data);
        if ($validate) {
            self::getValidator()->isValidateRulesDatabase($data);
        }

        return $instance;
    }

    private static function getValidator(): ValidatorDataInterface
    {
        return App::make(ValidatorDataInterface::class);
    }

    public static function fromArray(array $data): self
    {
        return static::create($data, true);
    }

    public static function fromWebhook(array $data): self
    {
        // Delegate Salesforce validation to validator
        // self::getValidator()->validateRequiredFieldsSalesforce($data);
        // self::getValidator()->isValidateSalesforceData($data);

        return static::create($data, false);
    }

    /**
     * Converte o DTO para array.
     */
    public function toArray(): array
    {
        return $this->data;
        // dd($this->data, static::getSchemaClass()::getFillable());
        // return array_intersect_key(
        //     $this->data,
        //     array_flip(static::getSchemaClass()::getFillable())
        // );
    }

    public function toJson(): string
    {
        return json_encode($this->toArray());
    }

    public function __get(string $name)
    {
        return $this->data[$name] ?? null;
    }

    public function __set(string $name, $value): void
    {
        if(static::getSchemaClass() != ''){
            if (in_array($name, static::getSchemaClass()::getFillable())) {
                $this->data[$name] = $value;
            }
        }
    }
}
