<?php

declare(strict_types=1);

namespace App\DDD\Infrastructure\Shared\Abstracts\Processors;

use App\DDD\Infrastructure\Shared\Interfaces\DTO\DtoInterface;
use App\DDD\Infrastructure\Shared\Interfaces\Processors\ProcessorsInterface;
use App\DDD\Infrastructure\Shared\Interfaces\Validators\ValidatorDataInterface;
use App\DDD\Infrastructure\Shared\Services\ServiceLocator;
use App\DDD\Infrastructure\Shared\Services\TypeResolver;
use Illuminate\Support\Collection;
use InvalidArgumentException;

/**
 * Classe abstrata base para processadores de dados
 */
abstract class AbstractProcessor implements ProcessorsInterface
{
    protected ?ValidatorDataInterface $validator;

    protected ServiceLocator $serviceLocator;

    protected TypeResolver $typeResolver;

    /**
     * @param  ValidatorDataInterface|null  $validator  Validador para os dados
     * @param  ServiceLocator|null  $serviceLocator  Localizador de serviços (opcional)
     */
    public function __construct(
        ?ValidatorDataInterface $validator,
        ?ServiceLocator $serviceLocator = null
    ) {
        $this->validator = $validator;
        $this->serviceLocator = $serviceLocator ?? new ServiceLocator();
        $this->typeResolver = new TypeResolver();
    }

    /**
     * Retorna a classe do schema a ser utilizado
     *
     * @return string Nome completo da classe
     */
    abstract public function getSchemaClass(): string;

    /**
     * Processa os dados de entrada e retorna um DTO ou uma coleção
     *
     * @param  array  $data  Os dados a serem processados
     * @return DtoInterface|Collection O resultado do processamento
     */
    public function process(array $data): DtoInterface|Collection
    {

        $this->validateInputData($data);

        // Template Method - hook para pré-processamento
        $data = $this->preProcess($data);

        // Processamento principal - pode ser sobrescrito se necessário
        $parsedData = $this->parseData($data);

        // Template Method - hook para pós-processamento
        $parsedData = $this->postProcess($parsedData);

        // Cria o DTO ou retorna a coleção
        $result = $this->createDto($parsedData);

        if ($result instanceof DtoInterface) {
            return $result;
        }

        return collect($parsedData);
    }

    /**
     * Realiza validação dos dados de entrada
     *
     * @param  array  $data  Os dados a serem validados
     *
     * @throws InvalidArgumentException Se os dados forem inválidos
     */
    protected function validateInputData(array $data): void
    {
        if (! isset($data['attributes']['type'])) {
            throw new InvalidArgumentException('O tipo não foi especificado nos atributos');
        }
    }

    /**
     * Hook para pré-processamento dos dados antes da transformação principal
     * Pode ser sobrescrito pelas classes filhas
     *
     * @param  array  $data  Os dados originais
     * @return array Os dados pré-processados
     */
    protected function preProcess(array $data): array
    {
        return $data;
    }

    /**
     * Transforma os dados baseado no tipo
     * Este é o método principal que as classes concretas podem sobrescrever
     *
     * @param  array  $data  Os dados a serem transformados
     * @return array Os dados transformados
     */
    public function parseData(array $data): array
    {
        $type = $data['attributes']['type'];
        $transformedData = $this->transformData($data, $type);
        if ($this->validator) {
            $this->validator->isValidateSalesforceData($transformedData);
        }

        return $transformedData;
    }

    /**
     * Hook para pós-processamento dos dados após a transformação principal
     * Pode ser sobrescrito pelas classes filhas
     *
     * @param  array  $data  Os dados processados
     * @return array Os dados pós-processados
     */
    protected function postProcess(array $data): array
    {
        return $data;
    }

    /**
     * Transforma dados usando o mapeamento de campos definido no schema
     *
     * @param  array  $data  Os dados a serem transformados
     * @param  string  $type  O tipo de transformação
     * @return array Os dados transformados
     */
    public function transformData(array $data, string $type = 'default'): array
    {
        $result = [];
        $class = $this->getSchemaClass();

        // Debug para verificar a estrutura
        foreach (array_keys($class::FIELDS) as $index => $fieldName) {
            // Verifica se existe o tipo de mapeamento e o índice correspondente
            if (
                ! isset($class::SALESFORCE_FIELDS[$type]) ||
                ! isset($class::SALESFORCE_FIELDS[$type][$index])
            ) {
                continue;
            }

            // Obtém o nome do campo no Salesforce usando o índice numérico
            $salesforceField = $class::SALESFORCE_FIELDS[$type][$index];

            // Extrai o tipo/valor padrão das regras de validação
            $validationRules = $class::FIELDS[$fieldName];
            $defaultValue = null;
            $fieldType = null;

            if (! empty($validationRules)) {
                $rules = explode('|', $validationRules);

                foreach ($rules as $rule) {
                    // Detecta tipos comuns nas regras de validação
                    if (in_array($rule, ['string', 'numeric', 'boolean', 'array'])) {
                        $fieldType = $rule;

                        break;
                    }
                }
            }

            // Determina o valor padrão com base no tipo
            if ($fieldType === 'boolean') {
                $defaultValue = false;
            } elseif ($fieldType === 'array') {
                $defaultValue = [];
            } elseif ($fieldType === 'numeric') {
                $defaultValue = 0;
            } elseif ($fieldType === 'json') {
                $defaultValue = [];
            }

            // Obtém o valor do campo, com fallback para o valor padrão
            $fieldValue = data_get($data, $salesforceField, $defaultValue);

            $result[$fieldName] = $fieldValue;

            // Converte o tipo se necessário
            if ($fieldType && $result[$fieldName] !== null) {
                switch ($fieldType) {
                    case 'boolean':
                        $result[$fieldName] = (bool) $result[$fieldName];

                        break;
                    case 'numeric':
                        $result[$fieldName] = is_numeric($result[$fieldName]) ?
                            (float) $result[$fieldName] : $defaultValue;

                        break;
                    case 'array':
                        if (! is_array($result[$fieldName])) {
                            $result[$fieldName] = empty($result[$fieldName]) ?
                                [] : [$result[$fieldName]];
                        }

                        break;
                    case 'json':
                        $result[$fieldName] = json_encode($result[$fieldName]);

                        break;
                }
            }
        }

        return $result;
    }

    /**
     * Cria um DTO a partir dos dados processados
     * Pode ser sobrescrito pelas classes filhas
     *
     * @param  array  $data  Os dados processados
     * @return DtoInterface|null O DTO criado ou null
     */
    protected function createDto(array $data): ?DtoInterface
    {
        return null;
    }

    /**
     * Obtém um serviço através do ServiceLocator
     *
     * @param  string  $serviceName  Nome do serviço
     * @return mixed O serviço solicitado
     */
    protected function service(string $serviceName): mixed
    {
        return $this->serviceLocator->get($serviceName);
    }
}
