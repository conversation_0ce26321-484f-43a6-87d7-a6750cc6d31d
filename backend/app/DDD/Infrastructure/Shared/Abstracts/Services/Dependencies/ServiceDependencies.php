<?php

declare(strict_types=1);

namespace App\DDD\Infrastructure\Shared\Abstracts\Services\Dependencies;

use App\DDD\Infrastructure\Shared\Interfaces\Processors\ProcessorsInterface;

trait ServiceDependencies
{
    /**
     * Resolve a dependency using the IoC container.
     */
    private function resolveDependency(string $method): mixed
    {
        $className = $this->{$method}();

        return app($className);
    }

    /**
     * Check if the class has a repository method.
     */
    protected function hasRepository(): bool
    {
        return method_exists($this, 'repository');
    }

    /**
     * Check if the class has a validator method.
     */
    protected function hasValidator(): bool
    {
        return method_exists($this, 'validator');
    }

    /**
     * Check if the class has a processor method.
     */
    protected function hasProcessor(): bool
    {
        return method_exists($this, 'processor');
    }

    /**
     * Check if the class has a transform method.
     */
    protected function hasTransform(): bool
    {
        return method_exists($this, 'transform');
    }

    /**
     * Ensure that repository exists before using it.
     */
    protected function ensureRepositoryExists(): void
    {
        if (! $this->repository) {
            throw new \RuntimeException('Repository is not available for this service.');
        }
    }

    /**
     * Get the processor instance.
     */
    public function getProcessor(): ProcessorsInterface
    {
        return $this->processor;
    }
}
