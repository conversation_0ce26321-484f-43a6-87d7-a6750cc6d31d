<?php

declare(strict_types=1);

namespace App\DDD\Infrastructure\Shared\Abstracts\Services;

use App\DDD\Infrastructure\External\Salesforce\Interfaces\SalesforceServiceInterface;
use App\DDD\Infrastructure\External\Sienge\Interfaces\SiengeServiceInterface;
use App\DDD\Infrastructure\Shared\Abstracts\DTO\AbstractDto;
use App\DDD\Infrastructure\Shared\Abstracts\Services\Dependencies\ServiceDependencies;
use App\DDD\Infrastructure\Shared\Interfaces\Processors\ProcessorsInterface;
use App\DDD\Infrastructure\Shared\Interfaces\Repository\RepositoryInterface;
use App\DDD\Infrastructure\Shared\Interfaces\Services\ServiceInterface;
use App\DDD\Infrastructure\Shared\Interfaces\Transform\TransformInterface;
use App\DDD\Infrastructure\Shared\Interfaces\Validators\ValidatorDataInterface;
use function get_class;
use function gettype;
use function is_object;
use function method_exists;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;

/**
 * Abstract service class with common functionality for all services.
 */
abstract class AbstractService implements ServiceInterface
{
    use ServiceDependencies;

    protected ?RepositoryInterface $repository = null;

    protected ?ValidatorDataInterface $validator = null;

    protected ?ProcessorsInterface $processor = null;

    protected ?TransformInterface $transform = null;

    protected SalesforceServiceInterface $salesforceService;

    protected SiengeServiceInterface $siengeService;

    private string $schemaClass;

    /**
     * Constructor with dependency injection.
     */
    public function __construct(
        ?RepositoryInterface $repository = null,
        ?ValidatorDataInterface $validator = null,
        ?ProcessorsInterface $processor = null,
        ?TransformInterface $transform = null
    ) {
        $this->salesforceService = app(SalesforceServiceInterface::class);
        $this->siengeService = app(SiengeServiceInterface::class);

        $this->repository = $repository ?? ($this->hasRepository() ? $this->resolveDependency('repository') : null);
        $this->validator = $validator ?? ($this->hasValidator() ? $this->resolveDependency('validator') : null);
        $this->processor = $processor ?? ($this->hasProcessor() ? $this->resolveDependency('processor') : null);
        $this->transform = $transform ?? ($this->hasTransform() ? $this->resolveDependency('transform') : null);
    }

    /**
     * Transform data using the transform component.
     */
    public function transformDataInDto(array $salesforceData): array
    {
        if (! $this->transform) {
            throw new \RuntimeException('Transform is not available for this service.');
        }
        return $this->transform->transformDataInDto($salesforceData);
    }

    /**
     * Transform data using the transform component.
     */
    public function transformWebhookDataInDto(array $data): array
    {
        if (! $this->transform) {
            throw new \RuntimeException('Transform is not available for this service.');
        }

        return $this->transform->transformWebhookDataInDto($data);
    }

    /**
     * Get all records.
     */
    public function getAll(): ?Collection
    {
        $this->ensureRepositoryExists();

        return $this->repository->all();
    }

    /**
     * Find a record by ID.
     */
    public function find(int|string $id): Model|Collection
    {
        $this->ensureRepositoryExists();

        return $this->repository->find($id);
    }

    public function findOneByField(string $field, string $value): Model|Collection
    {
        $this->ensureRepositoryExists();

        return $this->repository->findOneByField($field, $value);
    }
    /**
     * Create a new record.
     */
    public function create(AbstractDto $dto): Model|Collection
    {
        $this->ensureRepositoryExists();
        $dataDto = $dto->toArray();
        $this->validator->isValidateRulesDatabase($dataDto);

        return $this->repository->create($dto);
    }

    /**
     * Update an existing record.
     */
    public function update(int|string $id, AbstractDto $dto): Model|Collection
    {
        $this->ensureRepositoryExists();
        $dataDto = $dto->toArray();
        $this->validator->isValidateRulesDatabase($dataDto);

        return $this->repository->update($id, $dto);
    }

    /**
     * Update a record using a job (without validation).
     */
    public function updateWithJob(int|string $id, AbstractDto $dto): Model|Collection
    {
        $this->ensureRepositoryExists();

        return $this->repository->update($id, $dto);
    }

    /**
     * Delete a record.
     */
    public function delete(int|string $id): bool
    {
        $this->ensureRepositoryExists();

        return $this->repository->delete($id);
    }

    /**
     * Delete all records.
     */
    public function deleteAll(): bool
    {
        $this->ensureRepositoryExists();

        return $this->repository->deleteAll();
    }

    /**
     * Process data and create or update records accordingly.
     *
     * @param  AbstractDto|Collection  $data  Data to process
     * @return Model|Collection The created or updated models
     *
     * @throws \InvalidArgumentException If data is of invalid type
     */
    public function checkAndCreateOrUpdate(AbstractDto|Collection $data): Model|Collection
    {
        if (count($data->toArray()) === 0) {
            return collect();
        }

        if ($data instanceof AbstractDto) {
            return $this->createOrUpdate($data);
        } elseif ($data instanceof Collection) {
            return $this->createOrUpdateBulk($data->toArray());
        } else {
            throw new \InvalidArgumentException('O resultado do processador deve ser um AbstractDto ou uma Collection de AbstractDto');
        }
    }

    /**
     * Create or update an entity based on DTO.
     * If the DTO contains a valid ID, updates the existing record.
     * Otherwise, creates a new record.
     *
     * @param  AbstractDto  $dto  The data transfer object
     * @return Model|Collection The created or updated model
     */
    public function createOrUpdate(AbstractDto $dto): Model|Collection
    {
        $this->ensureRepositoryExists();

        // Check if the DTO has a defined ID
        $identifier = $this->getIdentifierFromDto($dto);

        if ($identifier && $this->repository->exists($identifier)) {
            // Update existing record
            return $this->update($identifier, $dto);
        } else {
            // Create new record
            return $this->create($dto);
        }
    }

    /**
     * Create or update multiple entities based on a collection of DTOs.
     *
     * @param  array  $dtos  Array of DTOs
     * @return array|Collection Array or collection of created/updated models
     *
     * @throws \InvalidArgumentException If any item is not an AbstractDto
     */
    public function createOrUpdateBulk(array $dtos): array|Collection
    {
        $this->ensureRepositoryExists();
        $results = [];
        foreach ($dtos as $index => $dto) {
            if (! $dto instanceof AbstractDto) {
                $type = is_object($dto) ? get_class($dto) : gettype($dto);

                throw new \InvalidArgumentException(
                    "Item no índice {$index} não é uma instância de AbstractDto. Tipo encontrado: {$type}"
                );
            }
            $results[] = $this->createOrUpdate($dto);
        }

        return collect($results);
    }

    /**
     * Extract the identifier from a DTO.
     * This method can be overridden in child classes if the identifier
     * has a different name than 'id'.
     *
     * @param  AbstractDto  $dto  The data transfer object
     * @return mixed The identifier value or null
     */
    public function getIdentifierFromDto(AbstractDto $dto): mixed
    {
        $data = $dto->toArray();
        $field = $this->repository->getModel()->getIdName();

        if (isset($data[$field]) && ! empty($data[$field])) {
            return $data[$field];
        }

        return null;
    }

    /**
     * Execute Salesforce data processing.
     *
     * @param  array  $params  Parameters for processing
     * @return array Processing results
     */
    public function execute(array $params): array
    {
        $results = [];

        // Check if there's Salesforce data in the parameters
        if (isset($params['salesforceData'])) {
            $salesforceData = $params['salesforceData'];

            // Configure salesforce service with necessary components
            if (isset($this->schemaClass)) {
                $this->salesforceService->setSchemaClass($this->schemaClass);
            } elseif (isset($this->validator) && method_exists($this->validator, 'getSchemaClass')) {
                $this->salesforceService->setSchemaClass($this->validator->getSchemaClass());
            }

            if (isset($this->validator)) {
                $this->salesforceService->setValidator($this->validator);
            }

            if (isset($this->processor)) {
                $this->salesforceService->setProcessor($this->processor);
            }

            // If it's an array of data (bulk), process each one
            if (isset($salesforceData[0]) && is_array($salesforceData[0])) {
                $models = $this->salesforceService->processBulkSalesforceData($salesforceData);
                $results['models'] = count($models) > 0 ? $models : [];
            } else {
                // Otherwise process as a single record
                $model = $this->salesforceService->processSalesforceData($salesforceData);
                $results['model'] = $model;
            }
        }

        // Allow child classes to add specific behaviors
        $additionalResults = $this->executeAdditionalProcessing($params);
        if (! empty($additionalResults)) {
            $results = array_merge($results, $additionalResults);
        }

        return $results;
    }

    /**
     * Method for additional processing in child classes.
     *
     * @param  array  $params  Processing parameters
     * @return array Additional results
     */
    protected function executeAdditionalProcessing(array $params): array
    {
        // By default, does nothing additional
        // Child classes can override to add functionality
        return [];
    }

    /**
     * Process Salesforce data.
     *
     * @param  array  $salesforceData  Salesforce data
     * @param  AbstractService  $service  Service to use for processing
     */
    public function processSalesforceData(array $salesforceData, self $service): Model|Collection|array|null|AbstractDto
    {
        $this->salesforceService->setSchemaClass($service->validator->getSchemaClass());
        $this->salesforceService->setValidator($service->validator);
        $this->salesforceService->setProcessor($service->processor);

        return $this->salesforceService->processSalesforceData($salesforceData);
    }
    public function processWebhookData(array $data, self $service): Model|Collection|array|null|AbstractDto
    {
        return $service->processor->process($data);
    }
}
