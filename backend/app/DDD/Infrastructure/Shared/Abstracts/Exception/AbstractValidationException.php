<?php

declare(strict_types=1);

namespace App\DDD\Infrastructure\Shared\Abstracts\Exception;

use Throwable;

class AbstractValidationException extends AbstractException
{
    public function __construct(
        string $message = 'Validation failed',
        int $code = 422,
        ?Throwable $previous = null,
        array $errors = [],
        array $context = []
    ) {
        parent::__construct($message, $code, $previous, $errors, $context);
    }
}
