<?php

declare(strict_types=1);

namespace App\DDD\Infrastructure\Shared\Abstracts\Exception;

use Exception;
use Throwable;

class AbstractException extends Exception implements Throwable
{
    /**
     * @var array Erros de validação
     */
    protected array $errors = [];

    protected array $context = [];

    /**
     * Cria uma nova instância de exceção de validação
     *
     * @param  string  $message  Mensagem de erro
     * @param  int  $code  Código de erro HTTP
     * @param  Throwable|null  $previous  Exceção anterior
     * @param  array  $errors  Erros de validação
     */
    public function __construct(
        string $message = '',
        int $code = 0,
        ?Throwable $previous = null,
        array $errors = [],
        array $context = []
    ) {
        parent::__construct($message, $code, $previous);
        $this->errors = $errors;
        $this->context = $context;
    }

    /**
     * Retorna os erros de validação
     */
    public function getErrors(): array
    {
        return $this->errors;
    }

    /**
     * Adiciona um erro de validação
     *
     * @param  string  $field  Campo
     * @param  string  $message  Mensagem de erro
     */
    public function addError(string $field, string $message): self
    {
        $this->errors[$field] = $message;

        return $this;
    }

    /**
     * Retorna a exceção como string para exibição em notificações
     */
    public function asString(): string
    {
        $result = $this->getMessage();

        if (! empty($this->errors)) {
            $result .= ': '.json_encode($this->errors);
        }

        return $result;
    }

    public function getContext(): array
    {
        return $this->context;
    }
}
