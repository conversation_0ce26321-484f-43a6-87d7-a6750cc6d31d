<?php

declare(strict_types=1);

namespace App\DDD\Infrastructure\Shared\Abstracts\Strategies;

use App\DDD\Infrastructure\External\Salesforce\Interfaces\SalesforceServiceInterface;
use App\DDD\Infrastructure\Shared\Interfaces\Services\ServiceInterface;
use App\DDD\Infrastructure\Shared\Interfaces\Strategies\ExecutorStrategyInterface;

abstract class AbstractExecutorStrategy implements ExecutorStrategyInterface
{
    protected ServiceInterface|SalesforceServiceInterface|null $serviceExecutor = null;

    public function execute(array $params): array
    {
        return $this->serviceExecutor->execute($params);
    }
}
