<?php

declare(strict_types=1);

namespace App\DDD\Infrastructure\Shared\Abstracts\Repositories;

use App\DDD\Infrastructure\Shared\Abstracts\DTO\AbstractDto;
use App\DDD\Infrastructure\Shared\Interfaces\Repository\RepositoryInterface;
use Exception;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\Log;

abstract class AbstractRepository implements RepositoryInterface
{
    protected $model;

    public function __construct()
    {
        $className = $this->getModelClass();
        $this->model = app($className);
    }

    abstract protected function getModelClass();

    public function getModel(): Model
    {
        return $this->model;
    }

    public function find(int|string $id): Model|Collection|null
    {
        return $this->model->where($this->model->getIdName(), $id)->first();
    }
    public function findOneByField(string $field, string $value): Model|Collection|null
    {
        return $this->model->where($field, $value)->first();
    }
    /**
     * Retorna dados paginados com filtros aplicados
     *
     * @param array $filter Condições de filtro
     * @param array $select Colunas a serem selecionadas
     * @param int $page Número da página
     * @param int $perPage Itens por página
     * @param array $with Relacionamentos a serem carregados
     * @param string $orderBy Campo para ordenação
     * @param string $direction Direção da ordenação (asc/desc)
     * @return \Illuminate\Pagination\LengthAwarePaginator
     */
    public function paginationWithFilter(
        array $filter,
        array $select = ['*'],
        int $page = 1,
        int $perPage = 10,
        array $with = [],
        string $orderBy = 'created_at',
        string $direction = 'desc'
    ): LengthAwarePaginator
    {
        $query = $this->model->where($filter)->select($select);

        // Aplica eager loading se relacionamentos forem especificados
        if (!empty($with)) {
            $query->with($with);
        }

        // Aplica ordenação
        $query->orderBy($orderBy, $direction);

        // Retorna o paginador com todos os metadados necessários
        return $query->paginate($perPage, $select, 'page', $page);
    }

    /**
     * Get all records with performance optimizations
     *
     * @param array $columns Columns to select
     * @param array $with Relationships to eager load
     * @param array $filter Where conditions to apply
     * @param int|null $limit Limit the number of records
     * @param string $orderBy Column to order by
     * @param string $direction Order direction (asc/desc)
     * @param bool $useCache Whether to use cache
     * @param int $cacheTtl Cache time to live in seconds
     * @return Collection
     */
    public function all(
        array $columns = ['*'],
        array $with = [],
        array $filter = [],
        ?int $limit = null,
        string $orderBy = 'created_at',
        string $direction = 'desc',
        bool $useCache = false,
        int $cacheTtl = 60
    ): Collection {
        $query = $this->model->select($columns);

        // Apply filter conditions if specified
        if (!empty($filter)) {
            $query->where($filter);
        }

        // Apply eager loading if relationships are specified
        if (!empty($with)) {
            $query->with($with);
        }

        // Apply ordering
        $query->orderBy($orderBy, $direction);

        // Apply limit if specified
        if ($limit !== null) {
            $query->limit($limit);
        }

        // Use cache if specified
        if ($useCache) {
            $cacheKey = 'repository:all:' . md5(json_encode([
                $columns, $with, $filter, $limit, $orderBy, $direction
            ]));

            return \Illuminate\Support\Facades\Cache::remember($cacheKey, $cacheTtl, fn() => $query->get());
        }

        return $query->get();
    }

    public function create(AbstractDto $dto): Model|Collection
    {
        try {
            return $this->model->create($dto->toArray());
        } catch (Exception $e) {
            Log::error('Erro ao criar modelo: ' . $e->getMessage());

            // Create an empty model to satisfy return type
            return $this->model->newInstance();
        }
    }

    public function update(int|string $id, AbstractDto $dto): Model|Collection|null
    {
        $this->model->where($this->model->getIdName(), $id)->update($dto->toArray());

        return $this->model->where($this->model->getIdName(), $id)->first();
    }

    public function delete(int|string $id): bool
    {
        return $this->model->where($this->model->getIdName(), $id)->delete();
    }

    public function deleteAll(): bool
    {
        return $this->model->delete();
    }

    public function exists(int|string $id): bool
    {
        return $this->model->where($this->model->getIdName(), $id)->exists();
    }
}
