<?php

declare(strict_types=1);

namespace App\DDD\Infrastructure\Shared\Abstracts\Queries;

use App\DDD\Infrastructure\Shared\Interfaces\Queries\QueryStrategyInterface;

abstract class AbstractQueries implements QueryStrategyInterface
{
    private array $params;

    public function setParams(array $params): void
    {
        $this->params = $params;
    }

    public function getParams(): array
    {
        return $this->params;
    }

    abstract public function execute(): string;
}
