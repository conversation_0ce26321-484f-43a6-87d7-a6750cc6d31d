<?php

declare(strict_types=1);

namespace App\DDD\Infrastructure\Shared\Abstracts\Queries;

use App\DDD\Infrastructure\Shared\Interfaces\Queries\QueriesServiceInterface;
use App\DDD\Infrastructure\Shared\Interfaces\Queries\QueryFactoryInterface;
use App\DDD\Infrastructure\Shared\Interfaces\Queries\QueryStrategyInterface;

abstract class AbstractQueriesService implements QueriesServiceInterface
{
    public QueryFactoryInterface $queryFactory;

    public $queryType;

    public ?QueryStrategyInterface $strategy = null;

    public function __construct(array $params, QueryFactoryInterface $queryFactory, $queryType)
    {
        $this->queryFactory = $queryFactory;
        $this->queryType = $queryType;
        $this->setStrategy();
        $this->setParams($params);
    }

    public function setFactory(QueryFactoryInterface $queryFactory): self
    {
        $this->queryFactory = $queryFactory;

        return $this;
    }

    public function setQueryType($queryType): self
    {
        $this->queryType = $queryType;

        return $this;
    }

    public function setStrategy(): self
    {
        // if ($type->requiresAuthentication() && !$this->isAuthenticated()) {
        //     throw new \RuntimeException('Autenticação necessária para esta query');
        // }

        $this->strategy = $this->queryFactory->create($this->queryType);

        return $this;
    }

    // private function isAuthenticated(): bool
    // {
    //     // Implementar lógica de verificação de autenticação
    //     return true;
    // }
    public function setParams(array $params): void
    {
        if (! $this->strategy) {
            throw new \RuntimeException('Strategy not set');
        }

        $this->strategy->setParams($params);
    }

    public function execute(): string
    {
        if (! $this->strategy) {
            throw new \RuntimeException('Strategy not set');
        }

        return $this->strategy->execute();
    }
}
