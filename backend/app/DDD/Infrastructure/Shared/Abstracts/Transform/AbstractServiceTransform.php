<?php

declare(strict_types=1);

namespace App\DDD\Infrastructure\Shared\Abstracts\Transform;

use App\DDD\Infrastructure\Shared\Interfaces\Enum\ServiceTypeEnumInterface;
use App\DDD\Infrastructure\Shared\Interfaces\Factories\ServiceFactoryInterface;
use App\DDD\Infrastructure\Shared\Interfaces\Transform\TransformInterface;
use Illuminate\Support\Facades\Log;

abstract class AbstractServiceTransform implements TransformInterface
{
    public function __construct(
        protected readonly ServiceFactoryInterface $serviceFactory
    ) {
    }

    /**
     * Transform data based on service type
     */
    abstract public function getServiceTypeEnum(): string;

    /**
     * Transform data into DTO
     */
    public function transformDataInDto(array $data): array
    {
        $result = [];
        $enumClass = $this->getServiceTypeEnum();

        foreach ($enumClass::cases() as $serviceType) {
            if (!$serviceType instanceof ServiceTypeEnumInterface) {
                continue;
            }
            $serviceInstance = $this->serviceFactory->makeService($serviceType);
            $result[$serviceType->value] = $this->processTransformation($serviceInstance, $data);
        }

        return $result;
    }
    /**
     * Transform data into DTO
     */
    public function transformWebhookDataInDto(array $data): array
    {
        $result = [];
        $enumClass = $this->getServiceTypeEnum();

        foreach ($enumClass::cases() as $serviceType) {
            if (!$serviceType instanceof ServiceTypeEnumInterface) {
                continue;
            }
            $serviceInstance = $this->serviceFactory->makeService($serviceType);
            $result[$serviceType->value] = $this->processWebhook($serviceInstance, $data);
        }

        return $result;
    }

    /**
     * Process specific data transformation
     */
    // abstract protected function processTransformation(mixed $serviceInstance, array $data): mixed;

    private function processTransformation(mixed $serviceInstance, array $data): mixed
    {
        return $serviceInstance->processSalesforceData($data, $serviceInstance);
    }


    private function processWebhook(mixed $serviceInstance, array $data): mixed
    {
        return $serviceInstance->processWebhookData($data, $serviceInstance);
    }
}
