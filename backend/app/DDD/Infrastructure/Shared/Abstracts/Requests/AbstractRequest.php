<?php

declare(strict_types=1);

namespace App\DDD\Infrastructure\Shared\Abstracts\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Request;

class AbstractRequest extends FormRequest
{
    public function __construct(Request $request)
    {
        parent::__construct();
        $this->initializeThisRequest($request);
    }

    public function initializeThisRequest(Request $request): void
    {
        $this->initialize($request->query->all(), $request->request->all(), $request->attributes->all(), $request->cookies->all(), $request->files->all(), $request->server->all(), $request->getContent());
    }
}
