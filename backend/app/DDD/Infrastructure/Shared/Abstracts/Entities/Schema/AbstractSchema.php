<?php

declare(strict_types=1);

namespace App\DDD\Infrastructure\Shared\Abstracts\Entities\Schema;

use App\DDD\Infrastructure\Shared\Interfaces\Schema\SchemaInterface;
use ReflectionClass;

/**
 * Classe abstrata base para definição de schemas
 * Implementa funcionalidades comuns aos schemas da aplicação
 */
abstract class AbstractSchema implements SchemaInterface
{
    /**
     * Cache de constantes das classes filhas
     */
    private static array $constCache = [];

    /**
     * Retorna a classe concreta que estende o AbstractSchema
     */
    abstract protected static function getConcreteClass(): string;

    /**
     * Obtém os campos preenchíveis para o Eloquent
     */
    public static function getFillable(): array
    {
        return array_keys(static::getConstant('FIELDS'));
    }

    /**
     * Obtém o cast da constante CAST
     */
    public static function getCast(): array
    {
        return static::getConstant('CAST') ?? [];
    }

    /**
     * Obtém o cast da constante CAST
     */
    public static function getRetryObjectTypeRelationship(): array
    {
        return static::getConstant('RETRY_OBJECT_TYPES_RELATIONSHIP') ?? [];
    }

    /**
     * Obtém os campos obrigatórios para validação de banco de dados
     */
    public static function getRequiredFieldsDatabase(): array
    {
        return static::getConstant('REQUIRED_FIELDS_DATABASE');
    }

    /**
     * Obtém os campos obrigatórios para validação do Salesforce
     */
    public static function getRequiredFieldsSalesforce(): array
    {
        return static::getConstant('REQUIRED_FIELDS_SALESFORCE');
    }

    /**
     * Obtém a definição de um relacionamento pelo nome
     */
    public static function getRelationshipDefinition(string $relation): array
    {
        $relationships = static::getConstant('RELATIONSHIPS');

        return $relationships[$relation] ?? [];
    }

    /**
     * Obtém o nome da tabela no banco de dados
     */
    public static function getDataBaseName(): string
    {
        return static::getConstant('TABLE_NAME');
    }

    /**
     * Obtém o nome do campo ID na tabela
     */
    public static function getIdName(): string
    {
        return static::getConstant('ID_NAME');
    }

    /**
     * Obtém a mensagem de erro para validação de banco de dados
     */
    public static function getDatabaseErrorMessage(): string
    {
        return static::getConstant('MESSAGES_DATABASE');
    }

    /**
     * Obtém a mensagem de erro para validação do Salesforce
     */
    public static function getSalesforceErrorMessage(): string
    {
        return static::getConstant('MESSAGES_SALESFORCE');
    }

    /**
     * Obtém o mapeamento entre campos do Salesforce e campos do banco
     * para um tipo específico
     */
    public static function getSalesforceMapping(string $type): array
    {
        $salesforceFields = static::getConstant('SALESFORCE_FIELDS');

        if (! isset($salesforceFields[$type])) {
            throw new \InvalidArgumentException("Tipo '{$type}' não encontrado no mapeamento de campos Salesforce");
        }

        $mapping = [];
        $fields = array_keys(static::getConstant('FIELDS'));
        $typeFields = $salesforceFields[$type];

        foreach ($fields as $index => $fieldName) {
            if (isset($typeFields[$index])) {
                $mapping[$fieldName] = $typeFields[$index];
            }
        }

        return $mapping;
    }

    /**
     * Obtém regras de retry para validação
     */
    public static function getRetrySalesforceMapping(string $type): array
    {
        $retrySalesforce = static::getConstant('RETRY_SALESFORCE');

        return $retrySalesforce[$type] ?? [];
    }

    /**
     * Obtém o tipo de objeto para retry
     */
    public static function getRetryObjectType(string $type): ?string
    {
        $retryObjectTypes = static::getConstant('RETRY_OBJECT_TYPES');

        return $retryObjectTypes[$type] ?? null;
    }

    /**
     * Verifica se um campo existe no schema
     */
    public static function hasField(string $fieldName): bool
    {
        $fields = static::getConstant('FIELDS');

        return isset($fields[$fieldName]);
    }

    /**
     * Obtém as regras de validação para um campo
     */
    public static function getFieldValidationRules(string $fieldName): ?string
    {
        $fields = static::getConstant('FIELDS');

        return $fields[$fieldName] ?? null;
    }

    /**
     * Obtém e armazena em cache constantes da classe
     */
    protected static function getConstant(string $name): mixed
    {
        $class = static::getConcreteClass();

        // Verifica o cache primeiro
        $cacheKey = $class.'::'.$name;

        if (! isset(self::$constCache[$cacheKey])) {
            // Obtém o valor da constante via reflexão
            $reflection = new ReflectionClass($class);

            if (! $reflection->hasConstant($name)) {
                return $name === 'RETRY_SALESFORCE' || $name === 'RETRY_OBJECT_TYPES'
                    ? []
                    : ($name === 'RELATIONSHIPS' ? [] : null);
            }

            self::$constCache[$cacheKey] = $reflection->getConstant($name);
        }

        return self::$constCache[$cacheKey];
    }
}
