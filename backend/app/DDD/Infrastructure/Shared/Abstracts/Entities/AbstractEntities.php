<?php

declare(strict_types=1);

namespace App\DDD\Infrastructure\Shared\Abstracts\Entities;

use App\DDD\Infrastructure\Shared\Interfaces\Entities\EntitiesInterface;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Facades\Log;

abstract class AbstractEntities extends Model implements EntitiesInterface
{
    use HasFactory;

    protected $fillable = [];

    protected $casts = [];

    public function __construct()
    {
        parent::__construct();
        $this->casts = $this->getCasts();
        $this->fillable = $this->getFillable();
    }

    abstract public static function getSchemaClass(): string;

    public function getIdName(): string
    {
        return static::getSchemaClass()::ID_NAME;
    }

    public function getTable(): string
    {
        return static::getSchemaClass()::TABLE_NAME;
    }

    public function getFillable(): array
    {
        return static::getSchemaClass()::getFillable();
    }

    public function getCasts(): array
    {
        return static::getSchemaClass()::getCast();
    }

    public function createComplexRelationship(string $relationName): BelongsTo|HasMany
    {
        $rel = static::getSchemaClass()::getRelationshipDefinition($relationName);

        if (empty($rel)) {
            throw new \InvalidArgumentException("Relationship {$relationName} not found in schema");
        }

        $type = $rel['type'] ?? 'belongsTo';

        if ($type === 'belongsTo') {
            $relation = $this->belongsTo(
                $rel['related_model'],
                $rel['primary']['foreign_key'],
                $rel['primary']['local_key']
            );
        } elseif ($type === 'hasMany') {
            $relation = $this->hasMany(
                $rel['related_model'],
                $rel['primary']['foreign_key'],
                $rel['primary']['local_key']
            );
        } else {
            throw new \InvalidArgumentException("Relationship type {$type} is not supported");
        }

        if (isset($rel['select']) && is_array($rel['select']) && count($rel['select']) > 0) {
            $relation->select($rel['select']);
        }

        if (isset($rel['with']) && is_array($rel['with']) && count($rel['with']) > 0) {
            $relation->with($rel['with']);
        }

        // if (isset($rel['where']) && is_array($rel['where'])) {
        //     foreach ($rel['where'] as $whereClause) {
        //         if (isset($whereClause['column'])) {
        //             if (isset($whereClause['value']) && $whereClause['operator'] == '!=' && $whereClause['value'] == 'Cancelado') {
        //                 $relation->where(function ($query) use ($whereClause) {
        //                     $query->where($whereClause['column'], '!=', $whereClause['value'])
        //                         ->orWhereNull($whereClause['column']);
        //                 });
        //             } elseif (isset($whereClause['value'])) {
        //                 $operator = $whereClause['operator'] ?? '=';
        //                 $relation->where($whereClause['column'], $operator, $whereClause['value']);
        //             } elseif (isset($whereClause['is_null']) && $whereClause['is_null'] === true) {
        //                 $relation->whereNull($whereClause['column']);
        //             } elseif (isset($whereClause['is_null']) && $whereClause['is_null'] === false) {
        //                 $relation->whereNotNull($whereClause['column']);
        //             }
        //         }
        //     }
        // }

        if (!isset($rel['where']) || !is_array($rel['where'])) {
            return $relation;
        }

        foreach ($rel['where'] as $whereClause) {
            if (!isset($whereClause['column'])) {
                continue;
            }

            $column = $whereClause['column'];

            // Verifica condições de nulidade primeiro
            if (isset($whereClause['is_null'])) {
                if ($whereClause['is_null'] === true) {
                    $relation->whereNull($column);
                } else {
                    $relation->whereNotNull($column);
                }
                continue;
            }

            // Verifica se há valor definido
            if (!isset($whereClause['value'])) {
                continue;
            }

            $operator = $whereClause['operator'] ?? '=';
            $value = $whereClause['value'];

            // Caso especial para '!=' com 'Cancelado'
            if ($operator === '!=' && $value === 'Cancelado') {
                $relation->where(function ($query) use ($column) {
                    $query->where($column, '!=', 'Cancelado')
                        ->orWhereNull($column);
                });
            } else {
                $relation->where($column, $operator, $value);
            }



            if (isset($rel['with']) && is_array($rel['with']) && count($rel['with']) > 0) {
                foreach ($rel['with'] as $with) {
                    $relation->with($with);
                }
            }
        }


        if (isset($rel['conditions'])) {
            foreach ($rel['conditions'] as $condition) {
                $relation->where(
                    $condition['foreign_key'],
                    $this->{$condition['local_key']}
                );
            }
        }

        // if (isset($rel['with']) && is_array($rel['with']) && count($rel['with']) > 0) {
            // foreach ($rel['with'] as $with) {
            //     $relation->with($with);
            // }
        // }
        return $relation;
    }
}
