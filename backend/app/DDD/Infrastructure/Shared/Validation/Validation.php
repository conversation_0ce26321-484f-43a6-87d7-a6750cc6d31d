<?php

// declare(strict_types=1);

// namespace App\DDD\Infrastructure\Shared\Validation;

// class Validation
// {
//     public static function validatePV($proposal)
//     {
//         $returnvalidade = true;
//         $validadores = [
//             ['rule' => $proposal['totalSize'] === 0, 'message' => '1:PV-INEXISTE'],
//             ['rule' => ! isset($proposal), 'message' => '2:PV-INEXISTE'],
//             ['rule' => ! isset($proposal['records']), 'message' => '3:PV-INEXISTE'],
//             ['rule' => count($proposal['records']) === 0, 'message' => '4:PV-INEXISTE'],
//             ['rule' => (isset($proposal['records'][0]['StatusAssinatura__c']) ? $proposal['records'][0]['StatusAssinatura__c'] : null) !== 'Assinado', 'message' => '5:PV NÃO ASSINADA'],
//             ['rule' => ! isset($proposal['records'][0]['Conta__r']), 'message' => '5:CONTA INEXISTENTE'],
//             ['rule' => ! isset($proposal['records'][0]['Unidade__c']), 'message' => '6:PV SEM ATIVO / UNIDADE RELACIONADO'],
//             ['rule' => ! isset($proposal['records'][0]['Unidade__r']['Ativo__r']['ContratoVigente__c']), 'message' => '7:PV SEM CONTRATO'],
//             ['rule' => (! isset($proposal['records'][0]['Unidade__r']['Ativo__r']['ContratoVigente__r']['Status']) && (isset($proposal['records'][0]['Unidade__r']['Ativo__r']['ContratoVigente__r']['Status']) ? $proposal['records'][0]['Unidade__r']['Ativo__r']['ContratoVigente__r']['Status'] : null) !== 'Escriturado'), 'message' => '9:CONTRATO NÃO ESCRITURADO'],
//         ];

//         foreach ($validadores as $validador) {
//             if ($validador['rule']) {
//                 return $returnvalidade = $validador['message'];
//             }
//         }

//         return $returnvalidade;
//     }

//     // <EMAIL>
// }
