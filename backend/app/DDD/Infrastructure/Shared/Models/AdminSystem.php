<?php

declare(strict_types=1);

namespace App\DDD\Infrastructure\Shared\Models;

use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Notifiable;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Notification;

class AdminSystem
{
    use Notifiable;

    protected $emails;

    public function __construct($emails)
    {
        if (is_array($emails)) {
            // Se já for um array, filtra os valores vazios
            $this->emails = array_filter($emails);
        } elseif (is_string($emails)) {
            // Se for string, divide por vírgulas
            $this->emails = array_filter(explode(',', $emails));
        } else {
            // Caso contrário, inicializa como array vazio
            $this->emails = [];
        }

        // Se depois de tudo estiver vazio, adiciona um email padrão
        if (empty($this->emails)) {
            $this->emails = ['<EMAIL>'];
        }

        Log::debug('Emails configurados para notificação: '.json_encode($this->emails));
    }

    /**
     * Retorna os endereços de email para notificações por email.
     *
     * @return array
     */
    public function routeNotificationForMail()
    {
        return $this->emails;
    }

    /**
     * Envia notificação garantindo que vá para a fila
     *
     * @param  mixed  $notification
     * @return void
     */
    public function notify($notification)
    {
        try {
            if ($notification instanceof ShouldQueue) {
                Log::debug('Notificação implementa ShouldQueue, enviando para fila');

                // Opção 1: Usar o Notification facade para enfileirar
                Notification::send($this, $notification);

                Log::debug('Notificação enfileirada com sucesso');
            } else {
                Log::debug('Notificação não implementa ShouldQueue, enviando diretamente');
                Notification::sendNow($this, $notification);
            }
        } catch (\Throwable $e) {
            Log::error('Erro ao enviar notificação: '.$e->getMessage());
            Log::error($e->getTraceAsString());

            // Fallback: tenta enviar diretamente como último recurso
            try {
                Notification::sendNow($this, $notification);
                Log::info('Notificação enviada com fallback');
            } catch (\Throwable $e2) {
                Log::error('Falha completa no envio de notificação: '.$e2->getMessage());
            }
        }
    }
}
