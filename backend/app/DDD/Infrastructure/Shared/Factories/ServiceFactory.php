<?php

declare(strict_types=1);

namespace App\DDD\Infrastructure\Shared\Factories;

use App\DDD\Infrastructure\Shared\Interfaces\Enum\ServiceTypeEnumInterface;
use App\DDD\Infrastructure\Shared\Interfaces\Factories\ServiceFactoryInterface;
use Illuminate\Container\Container;

class ServiceFactory implements ServiceFactoryInterface
{
    public function __construct(
        private readonly Container $container
    ) {
    }

    /**
     * @template T
     *
     * @param  T  $type
     *
     * @throws \Illuminate\Contracts\Container\BindingResolutionException
     */
    public function makeService(mixed $type): mixed
    {
        if (! $type instanceof ServiceTypeEnumInterface) {
            throw new \InvalidArgumentException(
                sprintf('Type must implement %s', ServiceTypeEnumInterface::class)
            );
        }

        $strategyClass = $type->getStrategyClass();

        if (! class_exists($strategyClass)) {
            throw new \InvalidArgumentException(
                sprintf('Strategy class %s does not exist', $strategyClass)
            );
        }

        return $this->container->make($strategyClass);
    }
}
