<?php

declare(strict_types=1);

namespace App\DDD\Infrastructure\Shared\Interfaces\Bindings;

use Illuminate\Contracts\Foundation\Application;

interface BindingsInterface
{
    public function register(Application $app): void;

    public function registerServices(Application $app): void;

    public function registerFactories(Application $app): void;

    public function registerQueries(Application $app): void;

    public function registerValidators(Application $app): void;

    public function registerStrategies(Application $app): void;

    public function registerHandlers(Application $app): void;

    public function registerRepositories(Application $app): void;

    public function registerProcessors(Application $app): void;

    public function registerOptionalDependencies(Application $app): void;
}
