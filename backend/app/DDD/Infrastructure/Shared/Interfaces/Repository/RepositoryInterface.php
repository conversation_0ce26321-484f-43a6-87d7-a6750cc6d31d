<?php

declare(strict_types=1);

namespace App\DDD\Infrastructure\Shared\Interfaces\Repository;

use App\DDD\Infrastructure\Shared\Abstracts\DTO\AbstractDto;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Pagination\LengthAwarePaginator;

interface RepositoryInterface
{
    /**
     * Busca um objeto pelo ID
     *
     * @return Model|Collection<?>|null
     */
    public function find(int|string $id): Model|Collection|null;

    /**
     * Retorna dados paginados com filtros aplicados
     *
     * @param array $filter Condições de filtro
     * @param array $select Colunas a serem selecionadas
     * @param int $page Número da página
     * @param int $perPage Itens por página
     * @param array $with Relacionamentos a serem carregados
     * @param string $orderBy Campo para ordenação
     * @param string $direction Direção da ordenação (asc/desc)
     * @return \Illuminate\Pagination\LengthAwarePaginator
     */
    public function paginationWithFilter(
        array $filter,
        array $select = ['*'],
        int $page = 1,
        int $perPage = 10,
        array $with = [],
        string $orderBy = 'created_at',
        string $direction = 'desc'
    ): LengthAwarePaginator;

    public function getModel(): Model;

    /**
     * Retorna todos os objetos
     *
     * @return Collection<?>
     */
    public function all(): Collection;

    /**
     * Cria um novo objeto a partir de um DTO
     *
     * @return Model|Collection<?>
     */
    public function create(AbstractDto $dto): Model|Collection;

    /**
     * Remove todos os objetos
     */
    public function deleteAll(): bool;

    /**
     * Atualiza um objeto a partir de um DTO
     *
     * @return Model|Collection<?>
     */
    public function update(int|string $id, AbstractDto $dto): Model|Collection|null;

    /**
     * Remove um objeto pelo ID
     */
    public function delete(int|string $id): bool;

    /**
     * Verifica se um objeto existe pelo ID
     */
    public function exists(int|string $id): bool;

    public function findOneByField(string $field, string $value): Model|Collection|null;
}
