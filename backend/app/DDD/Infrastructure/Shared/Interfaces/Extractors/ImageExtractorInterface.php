<?php

declare(strict_types=1);

namespace App\DDD\Infrastructure\Shared\Interfaces\Extractors;

/**
 * Interface para extratores de imagens
 */
interface ImageExtractorInterface
{
    /**
     * Extrai a primeira imagem de um conteúdo HTML
     *
     * @param  string  $imageTag  Conteúdo HTML com tags de imagem
     * @param  string  $id  ID do objeto relacionado
     * @param  string  $entity  Nome da entidade
     * @param  string  $field  Nome do campo
     * @return string|null URL da imagem ou null se não encontrada
     */
    public function extractFirst(string $imageTag, string $id, string $entity, string $field): ?string;

    /**
     * Extrai todas as imagens de um conteúdo HTML
     *
     * @param  string|null  $html  Conteúdo HTML com tags de imagem
     * @param  string  $objid  ID do objeto relacionado
     * @param  string  $obj  Nome do objeto
     * @param  string  $childobject  Nome do objeto filho/campo
     * @return array|null Array de URLs das imagens ou null se não encontradas
     */
    public function extract(?string $html, string $objid, string $obj, string $childobject): ?array;
}
