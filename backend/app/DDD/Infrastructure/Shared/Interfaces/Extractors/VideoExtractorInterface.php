<?php

declare(strict_types=1);

namespace App\DDD\Infrastructure\Shared\Interfaces\Extractors;

/**
 * Interface para extratores de vídeos
 */
interface VideoExtractorInterface
{
    /**
     * Extrai IDs de vídeos de um conteúdo HTML
     *
     * @param  string|null  $html  Conteúdo HTML com links para vídeos
     * @return array|null Array de IDs de vídeos ou null se não encontrados
     */
    public function extract(?string $html): ?array;
}
