<?php

declare(strict_types=1);

namespace App\DDD\Infrastructure\Shared\Interfaces\Compress;

interface ImageCompressInterface
{
    public function convertToWebP($imageContent, $size = 2000, $quality = 80);

    public function convertPdfToWebP($pdfContent, $size = 2000, $quality = 80, $page = 0);

    public function convertToPdf($imageContent, $size = 2000, $quality = 80);

    public function compressPdf(string $pdfContent, int $quality = 60, int $density = 150): ?string;
}
