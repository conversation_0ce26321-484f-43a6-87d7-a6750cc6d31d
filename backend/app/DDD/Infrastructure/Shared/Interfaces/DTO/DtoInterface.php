<?php

declare(strict_types=1);

namespace App\DDD\Infrastructure\Shared\Interfaces\DTO;

interface DtoInterface
{
    /**
     * Retorna os dados do DTO como um array.
     */
    public function toArray(): array;

    /**
     * Retorna os dados do DTO no formato JSON.
     */
    public function toJson(): string;

    /**
     * Cria uma instância do DTO a partir de um array de dados.
     */
    public static function fromArray(array $data): self;
}
