<?php

declare(strict_types=1);

namespace App\DDD\Infrastructure\Shared\Interfaces\Schema;

/**
 * Interface para esquemas que definem estrutura de dados
 */
interface SchemaInterface
{
    /**
     * Obtém os campos preenchíveis para o Eloquent
     *
     * @return array Lista de campos preenchíveis
     */
    public static function getFillable(): array;

    /**
     * Obtém os campos obrigatórios para validação de banco de dados
     *
     * @return array Campos obrigatórios ou mapeamento de tipos para campos obrigatórios
     */
    public static function getRequiredFieldsDatabase(): array;

    /**
     * Obtém os campos obrigatórios para validação do Salesforce
     *
     * @return array Campos obrigatórios ou mapeamento de tipos para campos obrigatórios
     */
    public static function getRequiredFieldsSalesforce(): array;

    /**
     * Obtém a definição de um relacionamento pelo nome
     *
     * @param  string  $relation  Nome do relacionamento
     * @return array Definição do relacionamento ou array vazio se não existir
     */
    public static function getRelationshipDefinition(string $relation): array;

    /**
     * Obtém o nome da tabela no banco de dados
     *
     * @return string Nome da tabela
     */
    public static function getDataBaseName(): string;

    /**
     * Obtém o nome do campo ID na tabela
     *
     * @return string Nome do campo ID
     */
    public static function getIdName(): string;

    /**
     * Obtém o mapeamento entre campos do Salesforce e campos do banco
     * para um tipo específico
     *
     * @param  string  $type  Tipo de objeto
     * @return array Mapeamento de campos
     */
    public static function getSalesforceMapping(string $type): array;

    /**
     * Obtém regras de retry para validação
     *
     * @param  string  $type  Tipo de objeto
     * @return array Mapeamento de campos para retry
     */
    public static function getRetrySalesforceMapping(string $type): array;

    /**
     * Obtém o tipo de objeto para retry
     *
     * @param  string  $type  Tipo original
     * @return string|null Tipo para retry ou null se não definido
     */
    public static function getRetryObjectType(string $type): ?string;
}
