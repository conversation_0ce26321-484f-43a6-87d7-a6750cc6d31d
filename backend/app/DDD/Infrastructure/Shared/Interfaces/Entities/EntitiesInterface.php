<?php

declare(strict_types=1);

namespace App\DDD\Infrastructure\Shared\Interfaces\Entities;

use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

interface EntitiesInterface
{
    public function getIdName(): string;

    public function getTable(): string;

    public function getFillable(): array;

    public function createComplexRelationship(string $relationName): BelongsTo|HasMany;
}
