<?php

declare(strict_types=1);

namespace App\DDD\Infrastructure\Shared\Interfaces\Controllers;

use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

interface ControllerInterface
{
    public function index(?Request $request): JsonResponse;

    public function show(?Request $request): JsonResponse;

    public function store(Request $request): JsonResponse;

    public function update(Request $request): JsonResponse;

    public function destroy(Request $request): JsonResponse;
}
