<?php

declare(strict_types=1);

namespace App\DDD\Infrastructure\Shared\Interfaces\Requests;

interface RequestInterface
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool;

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array;

    /**
     * Get data from the request.
     */
    public function getData(): string | array;
}
