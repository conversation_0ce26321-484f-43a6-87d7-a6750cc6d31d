<?php

declare(strict_types=1);

namespace App\DDD\Infrastructure\Shared\Interfaces\Services;

/**
 * Interface para localizadores de serviço
 */
interface ServiceLocatorInterface
{
    /**
     * Obtém um serviço pelo nome
     *
     * @param  string  $serviceName  Nome do serviço
     * @return mixed Instância do serviço
     *
     * @throws \InvalidArgumentException Se o serviço não existir
     */
    public function get(string $serviceName): mixed;

    /**
     * Registra um serviço no ServiceLocator
     *
     * @param  string  $serviceName  Nome do serviço
     * @param  mixed  $instance  Instância do serviço
     */
    public function register(string $serviceName, mixed $instance): void;
}
