<?php

declare(strict_types=1);

namespace App\DDD\Infrastructure\Shared\Interfaces\Services;

interface ErrorCacheServiceInterface
{
    /**
     * Verifica se um erro específico já foi reportado recentemente
     *
     * @param  string  $route  Rota da API onde o erro ocorreu
     * @param  string  $errorMessage  Mensagem de erro
     * @param  array  $context  Dados de contexto (podem ser quaisquer dados)
     * @return bool True se o erro já foi reportado recentemente (não deve enviar email)
     */
    public static function isErrorRecentlyReported(string $route, string $errorMessage, array $context = []): bool;

    /**
     * Registra um erro para evitar duplicações de email
     *
     * @param  string  $route  Rota da API onde o erro ocorreu
     * @param  string  $errorMessage  Mensagem de erro
     * @param  array  $context  Dados de contexto (podem ser quaisquer dados)
     */
    public static function markErrorAsReported(string $route, string $errorMessage, array $context = []): void;

    /**
     * Verifica se deve enviar um email de erro
     *
     * @param  string  $route  Rota da API onde o erro ocorreu
     * @param  string  $errorMessage  Mensagem de erro
     * @param  array  $context  Dados de contexto (podem ser quaisquer dados)
     */
    public static function shouldSendErrorEmail(string $route, string $errorMessage, array $context = []): bool;
}
