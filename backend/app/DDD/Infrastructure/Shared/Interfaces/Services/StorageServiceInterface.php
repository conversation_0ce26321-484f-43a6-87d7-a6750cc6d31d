<?php

declare(strict_types=1);

namespace App\DDD\Infrastructure\Shared\Interfaces\Services;

use App\DDD\Infrastructure\Shared\Interfaces\DTO\DtoInterface;

/**
 * Interface para serviço de gestão de arquivos
 */
interface StorageServiceInterface
{
    /**
     * Determina o tipo de conteúdo de um arquivo
     *
     * @param  mixed  $file  Conteúdo do arquivo ou caminho
     * @param  string  $filename  Nome do arquivo
     * @return string Tipo MIME do conteúdo
     */
    public function determineContentType($file, string $filename): string;

    /**
     * Converte uma imagem para o formato WebP
     *
     * @param  string  $imageContent  Conteúdo da imagem
     * @return string|null Conteúdo da imagem convertida ou null em caso de erro
     */
    public function convertToWebP(string $imageContent): ?string;

    /**
     * Faz upload de um PDF para o S3
     *
     * @param  string  $filename  Nome do arquivo
     * @param  string  $file  Conteúdo do arquivo ou URL
     * @param  bool  $createImage  Indica se deve criar uma imagem a partir do PDF
     * @return string|null URL do arquivo no S3 ou null em caso de erro
     */
    public function uploadPdfS3(string $filename, string $file, bool $createImage = false): ?string;

    /**
     * Obtém um PDF do Google Drive
     *
     * @param  string  $viewUrl  URL do Google Drive
     * @return string Conteúdo do PDF
     */
    public function getPdfDrive(string $viewUrl): string;

    /**
     * Obtém a URL para um arquivo do Salesforce
     *
     * @param  string  $id  ID do arquivo no Salesforce
     * @return string URL do arquivo
     */
    public function showpdfsales(string $id): string;

    /**
     * Faz upload de um arquivo para o S3
     *
     * @param  string  $filename  Nome do arquivo
     * @param  string  $file  Conteúdo do arquivo
     * @return string|null URL do arquivo no S3 ou null em caso de erro
     */
    public function uploadFileS3(string $filename, string $file): ?string;

    /**
     * Obtém a extensão de arquivo baseada no tipo MIME
     *
     * @param  string  $mime_type  Tipo MIME
     * @return string Extensão do arquivo
     */
    public function getExtensionFromMimeType(string $mime_type): string;

    /**
     * Remove um arquivo do S3
     *
     * @param  string  $filename  Nome do arquivo
     * @return array|null Informações sobre a exclusão ou null em caso de erro
     */
    public function deleteFileS3(string $filename): ?array;

    /**
     * Processa o nome de um arquivo para ser adequado para armazenamento
     *
     * @param  string  $fileName  Nome do arquivo
     * @return string Nome do arquivo processado
     */
    public function processFileName(string $fileName): string;

    /**
     * Gera um nome de arquivo baseado em parâmetros
     *
     * @param  string  $table  Nome da tabela/entidade
     * @param  string  $id  ID do registro
     * @param  array  $document  Dados do documento
     * @return string Nome do arquivo gerado
     */
    public function generateFilename(string $table, string $id, array $document): string;

    /**
     * Gerencia o upload de um arquivo com opção de fila
     *
     * @param  string  $filename  Nome do arquivo
     * @param  string  $file  Conteúdo ou URL do arquivo
     * @param  bool  $useQueue  Indica se deve usar fila
     * @param  bool  $createImage  Indica se deve criar uma imagem
     */
    public function handleFileUpload(string $filename, string $file, bool $useQueue, bool $createImage): void;

    /**
     * Faz upload de um arquivo usando fila
     *
     * @param  string  $id  ID do registro
     * @param  string  $filename  Nome do arquivo
     * @param  string  $file  Conteúdo ou URL do arquivo
     * @param  ServiceInterface  $service  Serviço para atualização
     * @param  DtoInterface  $dto  DTO para atualização
     * @param  string  $fieldName  Nome do campo
     * @param  bool  $createImage  Indica se deve criar uma imagem
     */
    public function uploadFileWithQueue(
        string $id,
        string $filename,
        string $file,
        ServiceInterface $service,
        DtoInterface $dto,
        string $fieldName = 'LinkS3',
        bool $createImage = false
    ): void;

    /**
     * Faz upload de um arquivo sem usar fila
     *
     * @param  string  $filename  Nome do arquivo
     * @param  string  $file  Conteúdo ou URL do arquivo
     * @param  bool  $createImage  Indica se deve criar uma imagem
     * @return string|null URL do arquivo no S3 ou null em caso de erro
     */
    public function uploadFileWithoutQueue(string $filename, string $file, bool $createImage): ?string;
}
