<?php

declare(strict_types=1);

namespace App\DDD\Infrastructure\Shared\Interfaces\Services;

use App\DDD\Infrastructure\Shared\Abstracts\DTO\AbstractDto;
use App\DDD\Infrastructure\Shared\Abstracts\Services\AbstractService;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Collection;

interface ServiceInterface
{
    /**
     * Retorna todos os registros.
     *
     * @return mixed
     */
    public function getAll(): ?Collection;

    /**
     * Retorna um registro pelo seu identificador.
     *
     * @param  int  $id
     * @return mixed
     */
    public function find(int|string $id): Model|Collection|null;

    /**
     * Cria um novo registro com os dados informados.
     *
     * @param  array  $data
     * @return mixed
     */
    public function create(AbstractDto $dto): Model|Collection|null;

    /**
     * Atualiza o registro com o identificador informado.
     *
     * @param  int  $id
     * @param  array  $data
     * @return mixed
     */
    public function update(int|string $id, AbstractDto $dto): Model|Collection|null;

    public function updateWithJob(int|string $id, AbstractDto $dto): Model|Collection|null;

    public function delete(int|string $id): bool;

    public function execute(array $params): array;

    public function checkAndCreateOrUpdate(AbstractDto|Collection $data): Model|Collection;

    public function createOrUpdateBulk(array $dtos): array|Collection;

    public function createOrUpdate(AbstractDto $dto): Model|Collection|null;

    public function getIdentifierFromDto(AbstractDto $dto): mixed;

    public function transformDataInDto(array $salesforceData): array|Model|Collection|null|AbstractDto;

    public function processWebhookData(array $data, AbstractService $service): Model|Collection|array|null|AbstractDto;

    public function findOneByField(string $field, string $value): Model|Collection|null;
}
