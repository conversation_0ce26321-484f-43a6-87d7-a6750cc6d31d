<?php

declare(strict_types=1);

namespace App\DDD\Infrastructure\Shared\Interfaces\Queries;

/**
 * Interface para construção de queries SQL
 *
 * Exemplo de uso:
 * $queryBuilder = new ConcreteQueryBuilder();
 * $query = $queryBuilder->buildQuery();
 */
interface FactoryInterface
{
    public function setType(string $type): void;

    public function setParams(array $params): void;

    public function getParams(): array;

    public function setExecutor(): void;

    public function execute(): string;
}
