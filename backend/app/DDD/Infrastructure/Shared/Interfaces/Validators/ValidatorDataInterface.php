<?php

declare(strict_types=1);

namespace App\DDD\Infrastructure\Shared\Interfaces\Validators;

/**
 * Interface para validadores de dados
 */
interface ValidatorDataInterface
{
    /**
     * Valida dados contra regras de banco de dados
     *
     * @param  array  &$data  Dados a serem validados (passados por referência)
     * @return bool Resultado da validação
     *
     * @throws \InvalidArgumentException Se os dados forem inválidos
     */
    public function isValidateRulesDatabase(array &$data): bool;

    /**
     * Valida dados do Salesforce
     *
     * @param  array  &$data  Dados a serem validados (passados por referência)
     * @return bool Resultado da validação
     *
     * @throws \InvalidArgumentException Se os dados forem inválidos
     */
    public function isValidateSalesforceData(array &$data): bool;

    /**
     * Obtém a classe do schema a ser utilizado para validação
     *
     * @return string Nome completo da classe do schema
     */
    public function getSchemaClass(): string;

    /**
     * Valida um conjunto específico de campos obrigatórios
     *
     * @param  array  &$data  Dados a serem validados (passados por referência)
     *
     * @throws \InvalidArgumentException Se os dados forem inválidos
     */
    public function validateRequiredFieldsDatabase(array &$data): void;

    /**
     * Valida campos obrigatórios do Salesforce
     *
     * @param  array  &$data  Dados a serem validados (passados por referência)
     *
     * @throws \InvalidArgumentException Se os dados forem inválidos
     */
    public function validateRequiredFieldsSalesforce(array &$data): void;
}
