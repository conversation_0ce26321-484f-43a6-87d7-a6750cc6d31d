<?php

// declare(strict_types=1);

// namespace App\DDD\Infrastructure\Shared\Interfaces\Factories;

// use App\DDD\Infrastructure\Shared\Interfaces\Processors\ProcessorsInterface;

// /**
//  * Interface para fábricas de processadores
//  */
// interface ProcessorFactoryInterface
// {
//     /**
//      * Registra um processador para um tipo específico
//      *
//      * @param  string  $type  Tipo do objeto
//      * @param  string  $processorClass  Nome da classe do processador
//      */
//     public function registerProcessor(string $type, string $processorClass): void;

//     /**
//      * Cria uma instância do processador apropriado para o tipo
//      *
//      * @param  string  $type  Tipo do objeto
//      * @return ProcessorsInterface Instância do processador
//      *
//      * @throws \InvalidArgumentException Se não houver processador registrado para o tipo
//      */
//     public function createProcessor(string $type): ProcessorsInterface;

//     /**
//      * Verifica se existe um processador registrado para o tipo
//      *
//      * @param  string  $type  Tipo do objeto
//      */
//     public function hasProcessorFor(string $type): bool;
// }
