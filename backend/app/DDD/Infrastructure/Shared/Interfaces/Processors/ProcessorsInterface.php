<?php

declare(strict_types=1);

namespace App\DDD\Infrastructure\Shared\Interfaces\Processors;

use App\DDD\Infrastructure\Shared\Interfaces\DTO\DtoInterface;
use Illuminate\Support\Collection;

/**
 * Interface para processadores de dados
 */
interface ProcessorsInterface
{
    /**
     * Processa os dados de entrada e retorna um DTO ou uma coleção
     *
     * @param  array  $data  Os dados a serem processados
     * @return DtoInterface|Collection O resultado do processamento
     */
    public function process(array $data): DtoInterface|Collection;

    /**
     * Realiza o parsing dos dados conforme regras específicas do processador
     *
     * @param  array  $data  Os dados a serem transformados
     * @return array Os dados transformados
     */
    public function parseData(array $data): array;

    /**
     * Retorna a classe do schema a ser utilizado
     *
     * @return string Nome completo da classe do schema
     */
    public function getSchemaClass(): string;

    /**
     * Transforma dados baseado no tipo
     *
     * @param  array  $data  Os dados a serem transformados
     * @param  string  $type  O tipo de transformação
     * @return array Os dados transformados
     */
    public function transformData(array $data, string $type = 'default'): array;
}
