<?php

declare(strict_types=1);

namespace App\DDD\Infrastructure\Shared\Interfaces\Processors;

use App\DDD\Infrastructure\Shared\Interfaces\DTO\DtoInterface;
use Illuminate\Support\Collection;

/**
 * Interface para processadores de imagens de carrossel
 */
interface CarouselImageProcessorInterface extends ProcessorsInterface
{
    /**
     * Processa dados de imagens de carrossel
     *
     * @param  array  $data  Dados contendo 'table' e 'Id'
     * @return DtoInterface|Collection Coleção de imagens processadas
     */
    public function process(array $data): DtoInterface|Collection;

    /**
     * Analisa os dados de imagens de carrossel
     *
     * @param  array  $data  Dados brutos de imagens de carrossel
     * @return array Dados processados de imagens
     */
    public function parseData(array $data): array;
}
