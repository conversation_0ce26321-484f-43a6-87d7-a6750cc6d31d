<?php

declare(strict_types=1);

namespace App\DDD\Infrastructure\Shared\Providers\Config;

use App\DDD\Infrastructure\External\Salesforce\Interfaces\SalesforceServiceInterface;
use App\DDD\Infrastructure\Shared\Abstracts\Bindings\AbstractBindings;
use App\DDD\Infrastructure\Shared\Extractors\ImageExtractor;
use App\DDD\Infrastructure\Shared\Extractors\VideoExtractor;
use App\DDD\Infrastructure\Shared\Image\ImageCompress;
use App\DDD\Infrastructure\Shared\Interfaces\Bindings\BindingsInterface;
use App\DDD\Infrastructure\Shared\Interfaces\Compress\ImageCompressInterface;
use App\DDD\Infrastructure\Shared\Interfaces\Extractors\ImageExtractorInterface;
use App\DDD\Infrastructure\Shared\Interfaces\Extractors\VideoExtractorInterface;
use App\DDD\Infrastructure\Shared\Interfaces\Processors\CarouselImageProcessorInterface;
use App\DDD\Infrastructure\Shared\Interfaces\Services\StorageServiceInterface;
use App\DDD\Infrastructure\Shared\Processors\CarouselImageProcessor;
use App\DDD\Infrastructure\Shared\Services\StorageService;
use Illuminate\Contracts\Foundation\Application;

class BasicServicesBindings extends AbstractBindings implements BindingsInterface
{
    public function registerServices(Application $app): void
    {
        // Registrar as dependências
        $app->singleton(ImageCompressInterface::class, function ($app) {
            return new ImageCompress();
        });

        $app->singleton(VideoExtractorInterface::class, function ($app) {
            return new VideoExtractor();
        });

        $app->singleton(StorageServiceInterface::class, function ($app) {
            return new StorageService(
                $app->make(ImageCompressInterface::class)
            );
        });

        $app->singleton(ImageExtractorInterface::class, function ($app) {
            return new ImageExtractor(
                $app->make(StorageServiceInterface::class)
            );
        });

        $app->singleton(CarouselImageProcessorInterface::class, function ($app) {
            return new CarouselImageProcessor(
                $app->make(ImageExtractorInterface::class),
                $app->make(SalesforceServiceInterface::class)
            );
        });
    }
}
