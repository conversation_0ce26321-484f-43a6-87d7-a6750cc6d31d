<?php

declare(strict_types=1);

namespace App\DDD\Infrastructure\Shared\Utils;

class ArrayValuePopulator
{
    private array $retryObjectTypesRelationship;

    private array $fields;

    public function __construct(string $schemaClass)
    {
        $this->retryObjectTypesRelationship = $schemaClass::RETRY_OBJECT_TYPES_RELATIONSHIP;
        $this->fields = $schemaClass::FIELDS;
    }

    /**
     * Populate null values in the first array with corresponding values from the second array
     *
     * @param  array  $firstArray  The array with null values to populate
     * @param  array  $secondArray  The array with source values
     * @param  string  $objectType  The object type (e.g., 'Asset')
     * @return array The first array with populated values
     */
    public function populateNullValues(array $firstArray, array $secondArray, string $objectType = 'Asset'): array
    {
        // Check if we have a relationship mapping for this object type
        if (! isset($this->retryObjectTypesRelationship[$objectType])) {
            return $firstArray; // Return unchanged if no mapping exists
        }

        $relationshipMap = $this->retryObjectTypesRelationship[$objectType];
        $resultArray = $firstArray;

        // Process all mappings in the relationship map
        foreach ($relationshipMap as $targetPath => $sourcePath) {
            // Get the source value from the second array
            $sourceValue = $this->getNestedValue($secondArray, $sourcePath);

            // If we have a value from the second array
            if ($sourceValue !== null) {
                // Check if target is a nested path
                if (strpos($targetPath, '.') !== false) {
                    // Set the nested value in the result array
                    $resultArray = $this->setNestedValue($resultArray, $targetPath, $sourceValue);
                } else {
                    // Simple case - direct assignment
                    // Only set if current value is null or not set
                    if (! isset($resultArray[$targetPath]) || $resultArray[$targetPath] === null) {
                        $resultArray[$targetPath] = $sourceValue;
                    }
                }
            }
        }

        return $resultArray;
    }

    /**
     * Get a nested value from an array using dot notation
     *
     * @param  array  $array  The source array
     * @param  string  $path  The path in dot notation (e.g., 'ContratoVigente__r.ContractNumber')
     * @return mixed|null The value at the path or null if not found
     */
    private function getNestedValue(array $array, string $path)
    {
        $keys = explode('.', $path);
        $value = $array;

        foreach ($keys as $key) {
            if (! isset($value[$key])) {
                return null;
            }
            $value = $value[$key];
        }

        return $value;
    }

    /**
     * Set a nested value in an array using dot notation
     *
     * @param  array  $array  The target array
     * @param  string  $path  The path in dot notation (e.g., 'Unidade__r.Ativo__r.Id')
     * @param  mixed  $value  The value to set
     * @return array The modified array
     */
    private function setNestedValue(array $array, string $path, $value): array
    {
        $keys = explode('.', $path);
        $result = $array;
        $current = &$result;

        // Navigate to the nested location, creating arrays as needed
        foreach ($keys as $i => $key) {
            if ($i === count($keys) - 1) {
                // Last key - set the value
                $current[$key] = $value;
            } else {
                // Create the nested array structure if it doesn't exist
                if (! isset($current[$key]) || ! is_array($current[$key])) {
                    $current[$key] = [];
                }
                // Move the reference deeper
                $current = &$current[$key];
            }
        }

        return $result;
    }

    /**
     * Process multiple arrays
     *
     * @param  array  $firstArrays  Array of first arrays
     * @param  array  $secondArrays  Array of second arrays
     * @param  string  $objectType  The object type
     * @return array Array of processed first arrays
     */
    public function processArrays(array $firstArrays, array $secondArrays, string $objectType = 'Asset'): array
    {
        $results = [];

        foreach ($firstArrays as $index => $firstArray) {
            // If we have a matching second array for this index, use it
            $secondArray = $secondArrays[$index] ?? null;

            if ($secondArray) {
                $results[] = $this->populateNullValues($firstArray, $secondArray, $objectType);
            } else {
                // No matching second array, just include the original
                $results[] = $firstArray;
            }
        }

        return $results;
    }
}
