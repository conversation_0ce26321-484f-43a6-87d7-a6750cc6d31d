<?php

declare(strict_types=1);

namespace App\DDD\Infrastructure\Shared\Utils;

class FormatDocs
{
    /**
     * Formata CPF removendo caracteres especiais
     */
    public static function formatCpf(string $cpf): string
    {
        $cpf = preg_replace('/\D/', '', $cpf);

        return preg_replace('/(\d{3})(\d{3})(\d{3})(\d{2})/', '$1.$2.$3-$4', $cpf);
    }

    /**
     * Formata CNPJ removendo caracteres especiais
     */
    public static function formatCnpj(string $cnpj): string
    {
        $cnpj = preg_replace('/\D/', '', $cnpj);

        return preg_replace('/(\d{2})(\d{3})(\d{3})(\d{4})(\d{2})/', '$1.$2.$3/$4-$5', $cnpj);
    }
}


// private function formatCpf(string $cpf): string
// {
//     $cpf = preg_replace('/\D/', '', $cpf);

//     return preg_replace('/(\d{3})(\d{3})(\d{3})(\d{2})/', '$1.$2.$3-$4', $cpf);
// }

// private function formatCnpj(string $cnpj): string
// {
//     $cnpj = preg_replace('/\D/', '', $cnpj);

//     return preg_replace('/(\d{2})(\d{3})(\d{3})(\d{4})(\d{2})/', '$1.$2.$3/$4-$5', $cnpj);
// }