<?php

declare(strict_types=1);

namespace App\DDD\Infrastructure\Shared\Image;

use App\DDD\Infrastructure\Shared\Interfaces\Compress\ImageCompressInterface;
use Illuminate\Support\Facades\Log;
use Imagick;
use ImagickException;

class ImageCompress
{
    public function convertToWebP($imageContent, $size = 2000, $quality = 80)
    {
        try {
            $image = new Imagick();

            if (empty($imageContent)) {
                Log::error('Conteúdo da Imagem__c vazio');

                return null;
            }

            $image->readImageBlob($imageContent);

            //            $image->transformImageColorspace(Imagick::COLORSPACE_SRGB);

            if ($image->getImageWidth() > $size) {
                $image->resizeImage($size, 0, Imagick::FILTER_LANCZOS, 1);
            }

            $image->stripImage();

            $image->setImageFormat('webp');
            $image->setImageCompressionQuality($quality);
            $image->setOption('webp:method', '4');
            $image->setOption('webp:lossless', 'true');
            $image->setOption('webp:alpha-filtering', '1');
            $image->setOption('webp:alpha-compression', '1');
            $image->setOption('webp:alpha-quality', '90');
            $image->setOption('webp:thread-level', '1');

            $optimizedImageBlob = $image->getImageBlob();

            $image->clear();
            $image->destroy();

            if (empty($optimizedImageBlob)) {
                Log::error('Falha ao gerar blob da Imagem__c otimizada');

                return null;
            }

            return $optimizedImageBlob;
        } catch (ImagickException $e) {
            // Log::error('Erro ao converter Imagem__c para WebP: ' . $e->getMessage());
            Log::error('Erro ao converter Imagem__c para WebP: ');

            return null;
        }
    }

    public function convertPdfToWebP($pdfContent, $size = 2000, $quality = 80, $page = 0)
    {
        try {
            $image = new Imagick();
            $image->setResolution(300, 300);

            $image->readImageBlob($pdfContent);

            $image->setIteratorIndex($page);
            if ($image->getImageWidth() > $size) {
                $image->resizeImage($size, 0, Imagick::FILTER_LANCZOS, 1);
            }
            $image->setImageFormat('webp');
            $image->setImageCompressionQuality($quality);
            $image->setOption('webp:method', '1');
            $image->setImageBackgroundColor('white');
            $image->stripImage();

            $webpBlob = $image->getImageBlob();

            $image->clear();
            $image->destroy();

            if (empty($webpBlob)) {
                Log::error('Falha ao gerar blob da Imagem__c WebP do PDF');

                return null;
            }

            return $webpBlob;
        } catch (ImagickException $e) {
            // Log::error('Erro ao converter PDF para WebP: ' . $e->getMessage());
            Log::error('Erro ao converter PDF para WebP: ');

            return null;
        }
    }

    public function convertToPdf($imageContent, $size = 2000, $quality = 80)
    {
        try {
            $image = new Imagick();
            $image->readImageBlob($imageContent);

            if ($image->getImageWidth() > $size) {
                $image->resizeImage($size, 0, Imagick::FILTER_LANCZOS, 1);
            }

            $image->setImageFormat('pdf');
            $image->setImageCompressionQuality($quality);
            $image->setImageBackgroundColor('white');
            $image->stripImage();

            $pdfBlob = $image->getImagesBlob();

            $image->clear();
            $image->destroy();

            if (empty($pdfBlob)) {
                Log::error('Falha ao gerar blob do PDF da Imagem__c');

                return null;
            }

            return $pdfBlob;
        } catch (ImagickException $e) {
            Log::error('Erro ao converter Imagem__c para PDF: ');

            // Log::error('Erro ao converter Imagem__c para PDF: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Comprime um PDF reduzindo a qualidade das imagens internas
     */
    public function compressPdf(string $pdfContent, int $quality = 60, int $density = 150): ?string
    {
        try {
            $image = new Imagick();

            // Define a densidade para controlar a resolução
            $image->setResolution($density, $density);

            // Lê o PDF
            $image->readImageBlob($pdfContent);

            // Define formato PDF
            $image->setImageFormat('pdf');

            // Define a compressão
            $image->setImageCompressionQuality($quality);
            $image->setImageCompression(Imagick::COMPRESSION_JPEG);

            // Remove metadados para reduzir tamanho
            $image->stripImage();

            // Define compressão para todas as páginass
            $image = $image->coalesceImages();
            foreach ($image as $frame) {
                $frame->setImageCompressionQuality($quality);
                $frame->setImageCompression(Imagick::COMPRESSION_JPEG);
                $frame->stripImage();
            }

            // Gera o PDF comprimido
            $compressedPdfBlob = $image->getImagesBlob();

            $image->clear();
            $image->destroy();

            if (empty($compressedPdfBlob)) {
                Log::error('Falha ao gerar PDF comprimido');
                return null;
            }

            return $compressedPdfBlob;
        } catch (ImagickException $e) {
            Log::error('Erro ao comprimir PDF: ' . $e->getMessage());
            return null;
        }
    }
}
