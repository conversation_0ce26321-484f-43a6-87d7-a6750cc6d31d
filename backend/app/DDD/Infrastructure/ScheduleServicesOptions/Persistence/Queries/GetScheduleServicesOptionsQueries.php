<?php

declare(strict_types=1);

namespace App\DDD\Infrastructure\ScheduleServicesOptions\Persistence\Queries;

use App\DDD\Infrastructure\Shared\Abstracts\Queries\AbstractQueries;
use App\DDD\Infrastructure\Shared\Interfaces\Queries\QueryStrategyInterface;

class GetScheduleServicesOptionsQueries extends AbstractQueries implements QueryStrategyInterface
{
    public function execute(): string
    {
        return "SELECT
            Id,
            TimeSlotNumber,
            DayOfWeek,
            StartTime,
            EndTime
        FROM
            TimeSlot
        WHERE
            OperatingHoursId='{$this->getParams()['OperatingHoursId']}'
        AND
            TipoTrabalho__c = 'Assistência Técnica - Faixa 2'
        ORDER BY
            StartTime ASC";
    }
}
