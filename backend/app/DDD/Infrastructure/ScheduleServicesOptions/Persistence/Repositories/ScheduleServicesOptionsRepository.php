<?php

declare(strict_types=1);

namespace App\DDD\Infrastructure\ScheduleServicesOptions\Persistence\Repositories;

use App\DDD\Domain\ScheduleServicesOptions\Entities\ScheduleServicesOptions;
use App\DDD\Infrastructure\ScheduleServicesOptions\Persistence\Interfaces\ScheduleServicesOptionsRepositoryInterface;
use App\DDD\Infrastructure\Shared\Abstracts\Repositories\AbstractRepository;

class ScheduleServicesOptionsRepository extends AbstractRepository implements ScheduleServicesOptionsRepositoryInterface
{
    public function getModelClass(): string
    {
        return ScheduleServicesOptions::class;
    }
}
