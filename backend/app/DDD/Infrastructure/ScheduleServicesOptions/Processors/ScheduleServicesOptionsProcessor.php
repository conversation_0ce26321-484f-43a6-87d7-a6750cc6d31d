<?php

declare(strict_types=1);

namespace App\DDD\Infrastructure\ScheduleServicesOptions\Processors;

use App\DDD\Application\ScheduleServicesOptions\DTOs\CreateScheduleServicesOptionsDto;
use App\DDD\Application\ScheduleServicesOptions\Schema\ScheduleServicesOptionsSchema;
use App\DDD\Domain\ScheduleServicesOptions\Interfaces\ScheduleServicesOptionsValidationInterface;
use App\DDD\Infrastructure\External\Salesforce\Interfaces\SalesforceServiceInterface;
use App\DDD\Infrastructure\ScheduleServicesOptions\Interfaces\ScheduleServicesOptionsProcessorInterface;
use App\DDD\Infrastructure\ScheduleServicesOptions\Persistence\Queries\GetScheduleServicesOptionsQueries;
use App\DDD\Infrastructure\Shared\Abstracts\Processors\AbstractProcessor;
use App\DDD\Infrastructure\Shared\Interfaces\DTO\DtoInterface;
use App\DDD\Infrastructure\Shared\Interfaces\Services\ServiceLocatorInterface;
use Illuminate\Support\Collection;
use InvalidArgumentException;

class ScheduleServicesOptionsProcessor extends AbstractProcessor implements ScheduleServicesOptionsProcessorInterface
{
    /**
     * @param  ScheduleServicesOptionsValidationInterface  $validator  Validador para os dados
     * @param  SalesforceServiceInterface  $salesforceService  Serviço de comunicação com Salesforce
     * @param  ServiceLocatorInterface|null  $serviceLocator  Localizador de serviços (opcional)
     */
    public function __construct(
        ScheduleServicesOptionsValidationInterface $validator,
        SalesforceServiceInterface $salesforceService,
        ?ServiceLocatorInterface $serviceLocator = null
    ) {
        parent::__construct($validator, $serviceLocator);

        // Registra o serviço do Salesforce manualmente, pois foi injetado
        $this->serviceLocator->register('salesforceService', $salesforceService);
    }

    /**
     * Retorna a classe do schema a ser utilizado
     */
    public function getSchemaClass(): string
    {
        return ScheduleServicesOptionsSchema::class;
    }

    /**
     * Cria um DTO a partir dos dados processados
     * Caso o retorno seja um Collection, deixe como null.
     *
     * @param  array  $data  Os dados processados
     * @return DtoInterface|null O DTO criado ou null
     */
    protected function createDto(array $data): ?DtoInterface
    {
        return null;
    }

    /**
     * Registra as estratégias para cada tipo de objeto
     */
    public function preProcess(array $data): array
    {
        $this->typeResolver->registerStrategy('Proposta__c', function (array $data) {
            return [
                'operatingHoursId' => data_get($data, 'Empreendimento__r.TerritorioServico__r.OperatingHoursId'),
                'empreendimentoId' => data_get($data, 'Empreendimento__c'),
            ];
        });

        $this->typeResolver->registerStrategy('Empreendimento__c', function (array $data) {
            return [
                'operatingHoursId' => data_get($data, 'TerritorioServico__r.OperatingHoursId'),
                'empreendimentoId' => data_get($data, 'Id'),
            ];
        });

        return $data;
    }

    /**
     * Processa os dados de entrada conforme o tipo
     */
    public function parseData(array $data): array
    {
        $type = $data['attributes']['type'] ?? null;
        if (! $type) {
            throw new InvalidArgumentException('Tipo de dados não informado');
        }

        try {
            // Resolve os IDs de operating hours baseado no tipo
            $operatingHoursInfo = $this->typeResolver->apply($type, $data);

            // Caso não exista o operating hours, retorna um array vazio
            if ($operatingHoursInfo['operatingHoursId'] === null) {
                return [];
            }

            // Carrega os serviços de agendamento
            $scheduleServicesData = $this->getScheduleServices($operatingHoursInfo['operatingHoursId']);
            $records = $scheduleServicesData['records'] ?? null;

            if ($records === null) {
                return [];
            }

            return $this->processScheduleRecords($records, $operatingHoursInfo['empreendimentoId'], $type);
        } catch (InvalidArgumentException $e) {
            // Se o tipo não for suportado, lança exceção informativa
            throw new InvalidArgumentException("Tipo de dado não suportado: {$type}");
        }
    }

    /**
     * Processa os registros de agendamento e transforma em DTOs
     */
    private function processScheduleRecords(array $records, ?string $empreendimentoId, string $type): array
    {
        $schedules = [];

        foreach ($records as $scheduleData) {
            $this->validator->isValidateSalesforceData($scheduleData);
            $scheduleData['EmpreendimentoId'] = $empreendimentoId;
            $schedule = $this->transformData($scheduleData, $type);
            $schedules[] = new CreateScheduleServicesOptionsDto($schedule);
        }

        return $schedules;
    }

    /**
     * Busca os serviços de agendamento pelo ID de operating hours
     */
    private function getScheduleServices(?string $operatingHoursId): ?array
    {
        if ($operatingHoursId === null) {
            return [];
        }

        $paramsQuery = [
            'params' => ['OperatingHoursId' => $operatingHoursId],
            'queryType' => new GetScheduleServicesOptionsQueries(),
        ];

        /** @var SalesforceServiceInterface $salesforceService */
        $salesforceService = $this->service('salesforceService');

        return $salesforceService->queryObject('query', $paramsQuery);
    }
}
