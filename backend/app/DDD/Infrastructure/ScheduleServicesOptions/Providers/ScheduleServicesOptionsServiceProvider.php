<?php

declare(strict_types=1);

namespace App\DDD\Infrastructure\ScheduleServicesOptions\Providers;

use App\DDD\Infrastructure\ScheduleServicesOptions\Providers\Config\ScheduleServicesOptionsBindings;
use App\Providers\ApiServicesProviders\BaseApiDDDServiceProvider;

class ScheduleServicesOptionsServiceProvider extends BaseApiDDDServiceProvider
{
    public function register(): void
    {
        parent::register();
        (new ScheduleServicesOptionsBindings())->register($this->app);
    }
}

// use App\DDD\Application\ScheduleServicesOptions\Interfaces\ScheduleServicesOptionsServiceInterface;

// use App\DDD\Application\ScheduleServicesOptions\Services\ScheduleServicesOptionsService;
// use App\DDD\Domain\ScheduleServicesOptions\Interfaces\ScheduleServicesOptionsValidationInterface;
// use App\DDD\Domain\ScheduleServicesOptions\Validation\ScheduleServicesOptionsValidation;
// use App\DDD\Infrastructure\ScheduleServicesOptions\Interfaces\ScheduleServicesOptionsProcessorInterface;
// use App\DDD\Infrastructure\ScheduleServicesOptions\Persistence\Interfaces\ScheduleServicesOptionsRepositoryInterface;

// use App\DDD\Infrastructure\ScheduleServicesOptions\Persistence\Repositories\ScheduleServicesOptionsRepository;
// use App\DDD\Infrastructure\ScheduleServicesOptions\Processors\ScheduleServicesOptionsProcessor;
// use App\DDD\Infrastructure\User\Interfaces\UserProcessorInterface;

// use App\DDD\Infrastructure\User\Processors\UserProcessor;
// use App\Providers\ApiServicesProviders\BaseApiDDDServiceProvider;
// ;

// class ScheduleServicesOptionsServiceProvider extends BaseApiDDDServiceProvider
// {
//     protected const MAIN_SERVICES = [
//         ScheduleServicesOptionsServiceInterface::class => ScheduleServicesOptionsService::class,
//         ScheduleServicesOptionsRepositoryInterface::class => ScheduleServicesOptionsRepository::class,
//         ScheduleServicesOptionsProcessorInterface::class => ScheduleServicesOptionsProcessor::class,
//         ScheduleServicesOptionsValidationInterface::class => ScheduleServicesOptionsValidation::class
//     ];

//     protected function registerMainService(): void
//     {
//         $this->app->singleton(ScheduleServicesOptionsServiceInterface::class, function ($app) {
//             return new ScheduleServicesOptionsService();
//         });

//         $this->app->singleton(ScheduleServicesOptionsProcessorInterface::class, function ($app) {
//             return new ScheduleServicesOptionsProcessor(
//                 $app->make(ScheduleServicesOptionsServiceInterface::class),
//                 $app->make(ScheduleServicesOptionsValidationInterface::class)
//             );
//         });

//         $this->app->bind(ScheduleServicesOptionsRepositoryInterface::class, ScheduleServicesOptionsRepository::class);
//         $this->app->bind(ScheduleServicesOptionsValidationInterface::class, ScheduleServicesOptionsValidation::class);
//         $this->app->bind(UserProcessorInterface::class, UserProcessor::class);

//     }

// }
