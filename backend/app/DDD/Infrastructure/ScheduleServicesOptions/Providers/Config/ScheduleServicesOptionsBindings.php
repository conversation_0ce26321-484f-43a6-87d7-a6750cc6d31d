<?php

declare(strict_types=1);

namespace App\DDD\Infrastructure\ScheduleServicesOptions\Providers\Config;

use App\DDD\Application\ScheduleServicesOptions\Interfaces\ScheduleServicesOptionsServiceInterface;
use App\DDD\Application\ScheduleServicesOptions\Services\ScheduleServicesOptionsService;
use App\DDD\Domain\ScheduleServicesOptions\Interfaces\ScheduleServicesOptionsValidationInterface;
use App\DDD\Domain\ScheduleServicesOptions\Validation\ScheduleServicesOptionsValidation;
use App\DDD\Infrastructure\External\Salesforce\Interfaces\SalesforceServiceInterface;
use App\DDD\Infrastructure\ScheduleServicesOptions\Interfaces\ScheduleServicesOptionsProcessorInterface;
use App\DDD\Infrastructure\ScheduleServicesOptions\Persistence\Interfaces\ScheduleServicesOptionsRepositoryInterface;
use App\DDD\Infrastructure\ScheduleServicesOptions\Persistence\Repositories\ScheduleServicesOptionsRepository;
use App\DDD\Infrastructure\ScheduleServicesOptions\Processors\ScheduleServicesOptionsProcessor;
use App\DDD\Infrastructure\Shared\Abstracts\Bindings\AbstractBindings;
use App\DDD\Infrastructure\Shared\Interfaces\Bindings\BindingsInterface;
use App\DDD\Infrastructure\Shared\Services\NotificationService;
use Illuminate\Contracts\Foundation\Application;

class ScheduleServicesOptionsBindings extends AbstractBindings implements BindingsInterface
{
    public function registerServices(Application $app): void
    {
        if (! $app->bound(ScheduleServicesOptionsRepositoryInterface::class)) {
            $app->singleton(
                ScheduleServicesOptionsRepositoryInterface::class,
                function ($app) {
                    return new ScheduleServicesOptionsRepository();
                }
            );
        }

        $app->singleton(
            ScheduleServicesOptionsServiceInterface::class,
            function ($app) {
                return new ScheduleServicesOptionsService(
                    $app->make(ScheduleServicesOptionsRepositoryInterface::class),
                    $app->make(ScheduleServicesOptionsValidationInterface::class),
                    $app->make(ScheduleServicesOptionsProcessorInterface::class)
                );
            }
        );
    }

    public function registerRepositories(Application $app): void
    {
        $app->singleton(
            ScheduleServicesOptionsRepositoryInterface::class,
            function ($app) {
                return new ScheduleServicesOptionsRepository();
            }
        );
    }

    public function registerProcessors(Application $app): void
    {
        $app->singleton(
            ScheduleServicesOptionsProcessorInterface::class,
            function ($app) {
                return new ScheduleServicesOptionsProcessor(
                    $app->make(ScheduleServicesOptionsValidationInterface::class),
                    $app->make(SalesforceServiceInterface::class)
                );
            }
        );
    }

    public function registerValidators(Application $app): void
    {
        $app->singleton(
            ScheduleServicesOptionsValidationInterface::class,
            function ($app) {
                return new ScheduleServicesOptionsValidation($app->make(NotificationService::class));
            }
        );
    }
}
